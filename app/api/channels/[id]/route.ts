import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const channelId = parseInt(params.id);
    if (isNaN(channelId)) {
      return NextResponse.json(
        { error: 'Invalid channel ID' },
        { status: 400 }
      );
    }

    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
      include: {
        inventory: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        prices: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        deposits: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        sales: {
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        settlements: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        _count: {
          select: {
            inventory: true,
            prices: true,
            deposits: true,
            sales: true,
            settlements: true,
          },
        },
      },
    });

    if (!channel) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(channel);
  } catch (error) {
    console.error('Error fetching channel:', error);
    return NextResponse.json(
      { error: 'Failed to fetch channel' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const channelId = parseInt(params.id);
    if (isNaN(channelId)) {
      return NextResponse.json(
        { error: 'Invalid channel ID' },
        { status: 400 }
      );
    }

    const data = await request.json();

    // 检查渠道是否存在
    const existingChannel = await prisma.channel.findUnique({
      where: { id: channelId },
    });

    if (!existingChannel) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      );
    }

    // 如果要更新编码，检查是否已存在
    if (data.code && data.code !== existingChannel.code) {
      const codeExists = await prisma.channel.findUnique({
        where: { code: data.code },
      });

      if (codeExists) {
        return NextResponse.json(
          { error: '渠道编码已存在' },
          { status: 400 }
        );
      }
    }

    // 处理日期字段
    let cooperationStart = undefined;
    if (data.cooperationStart) {
      cooperationStart = new Date(data.cooperationStart);
    }

    const updateData: any = {};

    // 只更新提供的字段
    if (data.name !== undefined) updateData.name = data.name;
    if (data.code !== undefined) updateData.code = data.code;
    if (data.type !== undefined) updateData.type = data.type;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.contactName !== undefined) updateData.contactName = data.contactName;
    if (data.contactPhone !== undefined) updateData.contactPhone = data.contactPhone;
    if (data.contactEmail !== undefined) updateData.contactEmail = data.contactEmail;
    if (data.address !== undefined) updateData.address = data.address;
    if (data.bankName !== undefined) updateData.bankName = data.bankName;
    if (data.bankAccount !== undefined) updateData.bankAccount = data.bankAccount;
    if (data.settlementCycle !== undefined) updateData.settlementCycle = parseInt(data.settlementCycle);
    if (cooperationStart !== undefined) updateData.cooperationStart = cooperationStart;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    const channel = await prisma.channel.update({
      where: { id: channelId },
      data: updateData,
      include: {
        _count: {
          select: {
            inventory: true,
            prices: true,
            deposits: true,
            sales: true,
            settlements: true,
          },
        },
      },
    });

    return NextResponse.json(channel);
  } catch (error) {
    console.error('Error updating channel:', error);
    return NextResponse.json(
      { error: 'Failed to update channel' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const channelId = parseInt(params.id);
    if (isNaN(channelId)) {
      return NextResponse.json(
        { error: 'Invalid channel ID' },
        { status: 400 }
      );
    }

    // 检查渠道是否存在
    const existingChannel = await prisma.channel.findUnique({
      where: { id: channelId },
      include: {
        _count: {
          select: {
            inventory: true,
            prices: true,
            deposits: true,
            sales: true,
            settlements: true,
          },
        },
      },
    });

    if (!existingChannel) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      );
    }

    // 检查是否有相关数据，如果有则不允许删除
    const hasRelatedData = existingChannel._count.inventory > 0 ||
                          existingChannel._count.prices > 0 ||
                          existingChannel._count.deposits > 0 ||
                          existingChannel._count.sales > 0 ||
                          existingChannel._count.settlements > 0;

    if (hasRelatedData) {
      return NextResponse.json(
        { error: '该渠道存在关联数据，无法删除。请先清理相关数据或将渠道状态设置为停用。' },
        { status: 400 }
      );
    }

    await prisma.channel.delete({
      where: { id: channelId },
    });

    return NextResponse.json({ message: 'Channel deleted successfully' });
  } catch (error) {
    console.error('Error deleting channel:', error);
    return NextResponse.json(
      { error: 'Failed to delete channel' },
      { status: 500 }
    );
  }
}