import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import { GET, POST } from '@/app/api/employees/route'
import { GET as getById, PUT as updateById, DELETE as deleteById } from '@/app/api/employees/[id]/route'
import { 
  testPrisma, 
  createMockRequest, 
  mockSession, 
  setupTestDatabase,
  cleanupTestDatabase,
  expectSuccessResponse,
  expectErrorResponse,
  validateEmployeeData,
  testUsers
} from './api-test-utils'

// Mock NextAuth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

describe('Employees API', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
    await testPrisma.$disconnect()
  })

  beforeEach(async () => {
    // 清理员工数据
    await testPrisma.employee.deleteMany()
  })

  describe('GET /api/employees', () => {
    it('应该返回员工列表', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 创建测试员工
      await testPrisma.employee.create({
        data: {
          name: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          department: '生产部',
          position: '工艺师',
          salary: 5000,
          hireDate: new Date('2023-01-01')
        }
      })

      const request = createMockRequest('GET', '/api/employees')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data).toHaveLength(1)
      validateEmployeeData(data[0])
    })

    it('应该支持部门筛选', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 创建不同部门的员工
      await testPrisma.employee.create({
        data: {
          name: '生产员工',
          email: '<EMAIL>',
          phone: '13800138001',
          department: '生产部',
          position: '工艺师',
          salary: 5000,
          hireDate: new Date('2023-01-01')
        }
      })

      await testPrisma.employee.create({
        data: {
          name: '销售员工',
          email: '<EMAIL>',
          phone: '13800138002',
          department: '销售部',
          position: '销售员',
          salary: 4000,
          hireDate: new Date('2023-01-01')
        }
      })

      const request = createMockRequest('GET', '/api/employees?department=生产部')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveLength(1)
      expect(data[0].department).toBe('生产部')
    })

    it('未授权用户不应该访问员工列表', async () => {
      const request = createMockRequest('GET', '/api/employees')
      const response = await GET(request)
      
      expect(response.status).toBe(401)
    })
  })

  describe('POST /api/employees', () => {
    it('应该创建新员工', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employeeData = {
        name: '新员工',
        email: '<EMAIL>',
        phone: '13800138003',
        department: '财务部',
        position: '会计',
        salary: 4500,
        hireDate: '2023-06-01'
      }

      const request = createMockRequest('POST', '/api/employees', employeeData)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      validateEmployeeData(data)
      expect(data.name).toBe(employeeData.name)
      expect(data.email).toBe(employeeData.email)
      expect(data.department).toBe(employeeData.department)

      // 验证数据库中是否真的创建了员工
      const dbEmployee = await testPrisma.employee.findUnique({
        where: { id: data.id }
      })
      expect(dbEmployee).toBeTruthy()
    })

    it('应该验证邮箱唯一性', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 先创建一个员工
      await testPrisma.employee.create({
        data: {
          name: '已存在员工',
          email: '<EMAIL>',
          phone: '13800138004',
          department: '技术部',
          position: '程序员',
          salary: 6000,
          hireDate: new Date('2023-01-01')
        }
      })

      // 尝试创建相同邮箱的员工
      const duplicateData = {
        name: '重复员工',
        email: '<EMAIL>',
        phone: '13800138005',
        department: '技术部',
        position: '程序员',
        salary: 6000,
        hireDate: '2023-06-01'
      }

      const request = createMockRequest('POST', '/api/employees', duplicateData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('邮箱')
    })

    it('应该验证必填字段', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidData = {
        email: '<EMAIL>',
        phone: '13800138006'
        // 缺少 name, department, position 等必填字段
      }

      const request = createMockRequest('POST', '/api/employees', invalidData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('name' || 'department' || 'position')
    })
  })

  describe('GET /api/employees/[id]', () => {
    it('应该返回指定员工详情', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employee = await testPrisma.employee.create({
        data: {
          name: '详情员工',
          email: '<EMAIL>',
          phone: '13800138007',
          department: '设计部',
          position: '设计师',
          salary: 5500,
          hireDate: new Date('2023-01-01')
        }
      })

      const request = createMockRequest('GET', `/api/employees/${employee.id}`)
      const response = await getById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      validateEmployeeData(data)
      expect(data.id).toBe(employee.id)
      expect(data.name).toBe('详情员工')
    })

    it('应该处理不存在的员工', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/employees/non-existent-id')
      const response = await getById(request, { params: { id: 'non-existent-id' } })
      
      expect(response.status).toBe(404)
      const error = await response.json()
      expect(error.error).toBe('员工不存在')
    })
  })

  describe('PUT /api/employees/[id]', () => {
    it('应该更新员工信息', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employee = await testPrisma.employee.create({
        data: {
          name: '原始员工',
          email: '<EMAIL>',
          phone: '13800138008',
          department: '市场部',
          position: '市场专员',
          salary: 4200,
          hireDate: new Date('2023-01-01')
        }
      })

      const updateData = {
        name: '更新员工',
        position: '市场经理',
        salary: 6000,
        department: '市场部'
      }

      const request = createMockRequest('PUT', `/api/employees/${employee.id}`, updateData)
      const response = await updateById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.name).toBe('更新员工')
      expect(data.position).toBe('市场经理')
      expect(data.salary).toBe(6000)

      // 验证数据库更新
      const dbEmployee = await testPrisma.employee.findUnique({
        where: { id: employee.id }
      })
      expect(dbEmployee?.name).toBe('更新员工')
      expect(dbEmployee?.salary).toBe(6000)
    })

    it('应该处理薪资变更记录', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employee = await testPrisma.employee.create({
        data: {
          name: '薪资测试员工',
          email: '<EMAIL>',
          phone: '13800138009',
          department: '人事部',
          position: '人事专员',
          salary: 4000,
          hireDate: new Date('2023-01-01')
        }
      })

      const updateData = {
        salary: 5000,
        salaryChangeReason: '绩效优秀'
      }

      const request = createMockRequest('PUT', `/api/employees/${employee.id}`, updateData)
      const response = await updateById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.salary).toBe(5000)

      // 验证是否创建了薪资记录
      const salaryRecord = await testPrisma.salaryRecord.findFirst({
        where: { 
          employeeId: employee.id,
          newSalary: 5000
        }
      })
      expect(salaryRecord).toBeTruthy()
    })
  })

  describe('DELETE /api/employees/[id]', () => {
    it('应该删除员工（软删除）', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employee = await testPrisma.employee.create({
        data: {
          name: '待删除员工',
          email: '<EMAIL>',
          phone: '13800138010',
          department: '临时部门',
          position: '临时工',
          salary: 3000,
          hireDate: new Date('2023-01-01')
        }
      })

      const request = createMockRequest('DELETE', `/api/employees/${employee.id}`)
      const response = await deleteById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(200)

      // 验证软删除 - 员工状态应该变为inactive
      const dbEmployee = await testPrisma.employee.findUnique({
        where: { id: employee.id }
      })
      expect(dbEmployee?.status).toBe('INACTIVE')
      expect(dbEmployee?.deletedAt).toBeTruthy()
    })

    it('应该处理删除不存在的员工', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('DELETE', '/api/employees/non-existent')
      const response = await deleteById(request, { params: { id: 'non-existent' } })
      
      expect(response.status).toBe(404)
    })

    it('应该检查删除权限', async () => {
      const employee = await testPrisma.employee.create({
        data: {
          name: '受保护员工',
          email: '<EMAIL>',
          phone: '13800138011',
          department: '管理层',
          position: '高级经理',
          salary: 10000,
          hireDate: new Date('2023-01-01')
        }
      })

      const request = createMockRequest('DELETE', `/api/employees/${employee.id}`)
      const response = await deleteById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(401)
    })
  })

  describe('员工业务逻辑测试', () => {
    it('应该验证薪资范围', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidSalaryData = {
        name: '薪资测试',
        email: '<EMAIL>',
        phone: '13800138012',
        department: '测试部',
        position: '测试员',
        salary: -1000, // 负薪资
        hireDate: '2023-06-01'
      }

      const request = createMockRequest('POST', '/api/employees', invalidSalaryData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('薪资')
    })

    it('应该处理员工绩效数据关联', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const employee = await testPrisma.employee.create({
        data: {
          name: '绩效员工',
          email: '<EMAIL>',
          phone: '13800138013',
          department: '销售部',
          position: '销售经理',
          salary: 8000,
          hireDate: new Date('2023-01-01')
        }
      })

      // 验证可以查询员工的绩效数据
      const request = createMockRequest('GET', `/api/employees/${employee.id}?include=performance`)
      const response = await getById(request, { params: { id: employee.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveProperty('id')
      // 绩效数据可能为空，但结构应该正确
    })
  })
})