"use server"

/**
 * 服务器操作函数
 *
 * 这个文件包含原始的服务器操作函数，以确保向后兼容性。
 *
 * 新代码应该直接从模块化的文件导入函数：
 * ```ts
 * // 直接导入特定函数
 * import { getUsers } from "@/lib/actions/user-actions";
 *
 * // 导入整个模块
 * import * as userActions from "@/lib/actions/user-actions";
 * ```
 *
 * @deprecated 这个文件中的函数已被模块化的函数替代，
 * 仅为了向后兼容性而保留。请直接从模块文件导入函数。
 */

// 保留原始导入，以便兼容现有代码
import prisma from "./db"
import { revalidatePath } from "next/cache"
import { AppError } from "./errors"

// 导入模块化的函数，以便在这里重新导出
// 用户相关
import {
  getUsers as getUsersModular,
  createUser as createUserModular,
  updateUser as updateUserModular,
  deleteUser as deleteUserModular,
  getUserRoles as getUserRolesModular,
  updateUserRoles as updateUserRolesModular
} from "./actions/user-actions"

// 认证相关
import {
  getCurrentUser as getCurrentUserModular
} from "./actions/auth-actions"

// 角色相关
import {
  getRoles as getRolesModular,
  createRole as createRoleModular,
  updateRole as updateRoleModular,
  deleteRole as deleteRoleModular
} from "./actions/role-actions"

// 产品相关
import {
  getProducts as getProductsModular,
  createProduct as createProductModular,
  updateProduct as updateProductModular,
  deleteProduct as deleteProductModular
} from "./actions/product-actions"

// 库存相关
import {
  getInventory as getInventoryModular,
  updateInventory as updateInventoryModular,
  transferInventory as transferInventoryModular
} from "./actions/inventory-actions"

// 销售相关
import {
  getSalesOrders as getSalesOrdersModular,
  createSalesOrder as createSalesOrderModular
} from "./actions/sales-actions"

// 采购相关
import {
  getPurchaseOrders as getPurchaseOrdersModular,
  createPurchaseOrder as createPurchaseOrderModular
} from "./actions/purchase-actions"

// 团建相关
import {
  getWorkshopActivities as getWorkshopActivitiesModular,
  createWorkshopActivity as createWorkshopActivityModular
} from "./actions/workshop-actions"

// 系统相关
import {
  getSystemSettings as getSystemSettingsModular,
  updateSystemSetting as updateSystemSettingModular
} from "./actions/system-actions"

// 重新导出模块化的函数，以便向后兼容
export async function getUsers() { return getUsersModular(); }
export async function createUser(data: any) { return createUserModular(data); }
export async function updateUser(id: string, data: any) { return updateUserModular(id, data); }
export async function deleteUser(id: string) { return deleteUserModular(id); }
export async function getUserRoles(userId: string) { return getUserRolesModular(userId); }
export async function updateUserRoles(userId: string, roleIds: number[]) { return updateUserRolesModular(userId, roleIds); }

export async function getCurrentUserNew() { return getCurrentUserModular(); }

export async function getRolesNew() { return getRolesModular(); }
export async function createRoleNew(data: any) { return createRoleModular(data); }
export async function updateRoleNew(id: number, data: any) { return updateRoleModular(id, data); }
export async function deleteRoleNew(id: number) { return deleteRoleModular(id); }

export async function getProductsNew() { return getProductsModular(); }
export async function createProductNew(data: any) { return createProductModular(data); }
export async function updateProductNew(id: number, data: any) { return updateProductModular(id, data); }
export async function deleteProductNew(id: number) { return deleteProductModular(id); }

export async function getInventoryNew() { return getInventoryModular(); }
export async function updateInventoryNew(id: number, data: any) { return updateInventoryModular(id, data); }
export async function transferInventoryNew(data: any) { return transferInventoryModular(data); }

export async function getSalesOrdersNew(status?: string, startDate?: string, endDate?: string) {
  return getSalesOrdersModular(status, startDate, endDate);
}
export async function createSalesOrderNew(data: any) { return createSalesOrderModular(data); }

export async function getPurchaseOrdersNew(status?: string, startDate?: string, endDate?: string) {
  return getPurchaseOrdersModular(status, startDate, endDate);
}
export async function createPurchaseOrderNew(data: any) { return createPurchaseOrderModular(data); }

export async function getWorkshopActivitiesNew(status?: string, startDate?: string, endDate?: string) {
  return getWorkshopActivitiesModular(status, startDate, endDate);
}
export async function createWorkshopActivityNew(data: any) { return createWorkshopActivityModular(data); }

export async function getSystemSettingsNew() { return getSystemSettingsModular(); }
export async function updateSystemSettingNew(key: string, value: any) { return updateSystemSettingModular(key, value); }

/**
 * 以下是原始函数，保留以确保向后兼容性
 * @deprecated 请使用模块化的操作函数
 */

// 员工相关操作
export async function getEmployees() {
  try {
    return await prisma.employee.findMany({
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching employees:", error)
    throw new Error("Failed to fetch employees")
  }
}

export async function createEmployee(data: any) {
  try {
    const employee = await prisma.employee.create({
      data: {
        name: data.name,
        position: data.position,
        phone: data.phone,
        email: data.email,
        dailySalary: Number.parseFloat(data.dailySalary),
        status: data.status || "active",
      },
    })

    revalidatePath("/employees")
    return employee
  } catch (error) {
    console.error("Error creating employee:", error)
    throw new Error("Failed to create employee")
  }
}

export async function updateEmployee(id: number, data: any) {
  try {
    const employee = await prisma.employee.update({
      where: { id },
      data: {
        name: data.name,
        position: data.position,
        phone: data.phone,
        email: data.email,
        dailySalary: Number.parseFloat(data.dailySalary),
        status: data.status,
      },
    })

    revalidatePath("/employees")
    return employee
  } catch (error) {
    console.error("Error updating employee:", error)
    throw new Error("Failed to update employee")
  }
}

export async function deleteEmployee(id: number) {
  try {
    await prisma.employee.delete({
      where: { id },
    })

    revalidatePath("/employees")
    return { success: true }
  } catch (error) {
    console.error("Error deleting employee:", error)
    throw new Error("Failed to delete employee")
  }
}

// 产品相关操作
export async function getProducts() {
  try {
    const products = await prisma.product.findMany({
      include: {
        productCategory: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })

    // 添加分类名称字段
    return products.map(product => {
      return {
        ...product,
        categoryName: product.productCategory?.name || null,
      };
    });
  } catch (error) {
    console.error("Error fetching products:", error)
    throw new Error("Failed to fetch products")
  }
}

export async function createProduct(data: any) {
  try {
    console.log("Server Action: Creating product with data:", data);

    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("产品名称不能为空");
    }

    if (isNaN(Number(data.price)) || Number(data.price) <= 0) {
      throw new Error("产品价格必须大于0");
    }

    // 使用事务确保产品创建和库存同步的原子性
    const result = await prisma.$transaction(async (tx) => {
      // 创建产品
      const product = await tx.product.create({
        data: {
          name: data.name.trim(),
          price: Number.parseFloat(data.price),
          commissionRate: 0, // 移除提成比例
          type: data.type || "product",
          imageUrl: data.imageUrl || null,
          imageUrls: data.imageUrls || [], // 支持多图片
          barcode: data.barcode || null,
          categoryId: data.categoryId ? parseInt(data.categoryId) : null,
          // 新增字段
          dimensions: data.dimensions || null,
          material: data.material || null,
          unit: data.unit || null,
          details: data.details || null,
          inventory: data.inventory || 0, // 产品表中的库存字段
        },
      });

      // 如果有库存数据，同步到库存模块
      if (data.inventory && Number(data.inventory) > 0) {
        // 获取默认仓库
        const defaultWarehouse = await tx.warehouse.findFirst({
          where: { isActive: true },
          orderBy: { id: 'asc' },
        });

        if (defaultWarehouse) {
          // 创建库存记录
          await tx.inventoryItem.create({
            data: {
              warehouseId: defaultWarehouse.id,
              productId: product.id,
              quantity: Number(data.inventory),
            },
          });

          // 创建库存交易记录
          await tx.inventoryTransaction.create({
            data: {
              type: "in",
              targetWarehouseId: defaultWarehouse.id,
              productId: product.id,
              quantity: Number(data.inventory),
              notes: "产品创建初始库存",
              referenceType: "create",
            },
          });
        }
      }

      return product;
    });

    console.log("Server Action: Product created successfully:", result);

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");
    revalidatePath("/inventory");

    return result;
  } catch (error) {
    console.error("Server Action: Error creating product:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create product");
  }
}

export async function updateProduct(id: number, data: any) {
  try {
    console.log("Server Action: Updating product with data:", data);

    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("产品名称不能为空");
    }

    if (isNaN(Number(data.price)) || Number(data.price) <= 0) {
      throw new Error("产品价格必须大于0");
    }

    // 使用事务确保产品更新和库存同步的原子性
    const result = await prisma.$transaction(async (tx) => {
      // 获取当前产品信息
      const currentProduct = await tx.product.findUnique({
        where: { id },
      });

      if (!currentProduct) {
        throw new Error("产品不存在");
      }

      // 更新产品
      const product = await tx.product.update({
        where: { id },
        data: {
          name: data.name.trim(),
          price: Number.parseFloat(data.price),
          commissionRate: 0, // 移除提成比例
          type: data.type || "product",
          imageUrl: data.imageUrl || null,
          imageUrls: data.imageUrls || [], // 支持多图片
          barcode: data.barcode || null,
          categoryId: data.categoryId ? parseInt(data.categoryId) : null,
          // 新增字段
          dimensions: data.dimensions || null,
          material: data.material || null,
          unit: data.unit || null,
          details: data.details || null,
          inventory: data.inventory || 0, // 产品表中的库存字段
        },
      });

      // 如果库存发生变化，同步到库存模块
      if (data.inventory !== undefined && Number(data.inventory) !== Number(currentProduct.inventory)) {
        // 获取默认仓库
        const defaultWarehouse = await tx.warehouse.findFirst({
          where: { isActive: true },
          orderBy: { id: 'asc' },
        });

        if (defaultWarehouse) {
          // 查找现有库存记录
          const inventoryItem = await tx.inventoryItem.findFirst({
            where: {
              warehouseId: defaultWarehouse.id,
              productId: id,
            },
          });

          const newQuantity = Number(data.inventory);

          if (inventoryItem) {
            // 更新库存记录
            await tx.inventoryItem.update({
              where: { id: inventoryItem.id },
              data: { quantity: newQuantity },
            });

            // 计算变化量
            const change = newQuantity - inventoryItem.quantity;

            if (change !== 0) {
              // 创建库存交易记录
              await tx.inventoryTransaction.create({
                data: {
                  type: change > 0 ? "in" : "out",
                  targetWarehouseId: defaultWarehouse.id,
                  productId: id,
                  quantity: Math.abs(change),
                  notes: "产品编辑更新库存",
                  referenceType: "update",
                },
              });
            }
          } else if (newQuantity > 0) {
            // 创建新库存记录
            await tx.inventoryItem.create({
              data: {
                warehouseId: defaultWarehouse.id,
                productId: id,
                quantity: newQuantity,
              },
            });

            // 创建库存交易记录
            await tx.inventoryTransaction.create({
              data: {
                type: "in",
                targetWarehouseId: defaultWarehouse.id,
                productId: id,
                quantity: newQuantity,
                notes: "产品编辑创建库存",
                referenceType: "update",
              },
            });
          }
        }
      }

      return product;
    });

    console.log("Server Action: Product updated successfully:", result);

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");
    revalidatePath("/inventory");

    return result;
  } catch (error) {
    console.error("Server Action: Error updating product:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update product");
  }
}

export async function deleteProduct(id: number) {
  try {
    console.log("Server Action: Deleting product with id:", id);

    await prisma.product.delete({
      where: { id },
    })

    console.log("Server Action: Product deleted successfully");

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");

    return { success: true }
  } catch (error) {
    console.error("Server Action: Error deleting product:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to delete product")
  }
}

export async function batchUpdateProducts(data: any) {
  try {
    console.log("Server Action: Batch updating products with data:", data);

    // 验证数据
    if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
      throw new Error("未提供有效的产品ID列表");
    }

    if (!data.fields || Object.keys(data.fields).length === 0) {
      throw new Error("未提供要更新的字段");
    }

    // 准备更新数据
    const updateData: any = {};

    // 处理分类
    if (data.fields.categoryId !== undefined) {
      updateData.categoryId = data.fields.categoryId;
    }

    // 处理价格
    if (data.fields.price !== undefined && data.fields.price !== null) {
      if (isNaN(data.fields.price) || data.fields.price <= 0) {
        throw new Error("价格必须是大于0的数字");
      }
      updateData.price = data.fields.price;
    }

    // 处理状态 - 通过type字段实现
    if (data.fields.status !== undefined) {
      updateData.type = data.fields.status === "inactive" ? "category_placeholder" : "product";
    }

    // 处理单位
    if (data.fields.unit !== undefined) {
      updateData.unit = data.fields.unit;
    }

    // 处理材质
    if (data.fields.material !== undefined) {
      updateData.material = data.fields.material;
    }

    // 处理标签
    if (data.fields.tags !== undefined) {
      updateData.tags = data.fields.tags;
    }

    // 执行批量更新
    const result = await prisma.product.updateMany({
      where: {
        id: {
          in: data.ids.map((id: string | number) => Number(id))
        }
      },
      data: updateData
    });

    console.log("Server Action: Batch update result:", result);

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");

    return {
      success: true,
      updatedCount: result.count
    };
  } catch (error) {
    console.error("Server Action: Error batch updating products:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to batch update products");
  }
}

// 分类相关操作
export async function getProductCategories() {
  try {
    console.log("Server Action: Fetching product categories");

    // 获取所有分类
    const categories = await prisma.productCategory.findMany({
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        children: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: [
        { name: 'asc' },
      ],
    });

    // 计算每个分类的产品数量
    const categoriesWithProductCount = categories.map(category => ({
      ...category,
      productCount: category._count.products,
      _count: undefined,
    }));

    console.log("Server Action: Found categories:", categoriesWithProductCount.length);

    return categoriesWithProductCount;
  } catch (error) {
    console.error("Server Action: Error fetching product categories:", error);
    throw new Error(error instanceof Error ? error.message : "获取产品分类失败");
  }
}

export async function createProductCategory(data: any) {
  try {
    console.log("Server Action: Creating product category with data:", data);

    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("分类名称不能为空");
    }

    // 规范化分类名称
    const categoryName = data.name.trim();

    // 检查分类是否已存在
    const existingCategory = await prisma.productCategory.findFirst({
      where: {
        name: categoryName,
        parentId: data.parentId || null,
      },
    });

    if (existingCategory) {
      throw new Error("同一父分类下已存在相同名称的分类");
    }

    // 处理父分类
    let path = "";
    let level = 1;

    if (data.parentId) {
      const parentCategory = await prisma.productCategory.findUnique({
        where: { id: data.parentId },
      });

      if (!parentCategory) {
        throw new Error("父分类不存在");
      }

      level = (parentCategory.level || 1) + 1;
      path = parentCategory.path
        ? `${parentCategory.path},${parentCategory.id}`
        : `${parentCategory.id}`;
    }

    // 创建新分类
    const newCategory = await prisma.productCategory.create({
      data: {
        name: categoryName,
        code: data.code || null,
        parentId: data.parentId || null,
        level,
        path,
        description: data.description || "",
        imageUrl: data.imageUrl || null,
        isActive: data.isActive !== false,
        sortOrder: data.sortOrder || 0,
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log("Server Action: Created category:", newCategory.id);

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");

    return newCategory;
  } catch (error) {
    console.error("Server Action: Error creating product category:", error);
    throw new Error(error instanceof Error ? error.message : "创建产品分类失败");
  }
}

export async function updateProductCategory(data: any) {
  try {
    console.log("Server Action: Updating product category with data:", data);

    // 验证必填字段
    if (!data.id || !data.name || data.name.trim() === "") {
      throw new Error("分类ID和名称不能为空");
    }

    // 规范化分类名称
    const categoryName = data.name.trim();

    // 查找要更新的分类
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id: data.id },
    });

    if (!existingCategory) {
      throw new Error("分类不存在");
    }

    // 检查新名称是否与其他分类冲突（同一父分类下）
    if (categoryName !== existingCategory.name) {
      const conflictCategory = await prisma.productCategory.findFirst({
        where: {
          name: categoryName,
          parentId: data.parentId || null,
          id: { not: data.id },
        },
      });

      if (conflictCategory) {
        throw new Error("同一父分类下已存在相同名称的分类");
      }
    }

    // 处理父分类变更
    let path = existingCategory.path;
    let level = existingCategory.level;

    // 如果父分类发生变化
    if (data.parentId !== existingCategory.parentId) {
      // 检查是否将分类设为自己的子分类（循环引用）
      if (data.parentId === data.id) {
        throw new Error("不能将分类设为自己的子分类");
      }

      // 检查是否将分类设为自己的后代分类（循环引用）
      if (data.parentId && existingCategory.path) {
        const ancestorIds = existingCategory.path.split(',').map(id => parseInt(id));
        if (ancestorIds.includes(data.id)) {
          throw new Error("不能将分类设为自己的后代分类");
        }
      }

      // 重新计算level和path
      if (data.parentId) {
        const parentCategory = await prisma.productCategory.findUnique({
          where: { id: data.parentId },
        });

        if (!parentCategory) {
          throw new Error("父分类不存在");
        }

        level = (parentCategory.level || 1) + 1;
        path = parentCategory.path
          ? `${parentCategory.path},${parentCategory.id}`
          : `${parentCategory.id}`;
      } else {
        // 如果移动到顶级分类
        level = 1;
        path = "";
      }
    }

    // 更新分类
    const updatedCategory = await prisma.productCategory.update({
      where: { id: data.id },
      data: {
        name: categoryName,
        code: data.code !== undefined ? data.code : existingCategory.code,
        parentId: data.parentId !== undefined ? data.parentId : existingCategory.parentId,
        level,
        path,
        description: data.description !== undefined ? data.description : existingCategory.description,
        imageUrl: data.imageUrl !== undefined ? data.imageUrl : existingCategory.imageUrl,
        isActive: data.isActive !== undefined ? data.isActive : existingCategory.isActive,
        sortOrder: data.sortOrder !== undefined ? data.sortOrder : existingCategory.sortOrder,
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
          },
        },
        children: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    // 如果父分类发生变化，需要更新所有子分类的level和path
    if (data.parentId !== existingCategory.parentId) {
      // 递归更新所有子分类
      await updateChildrenLevelAndPath(data.id, level, path);
    }

    // 计算产品数量
    const categoryWithProductCount = {
      ...updatedCategory,
      productCount: updatedCategory._count.products,
      _count: undefined,
    };

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");

    return categoryWithProductCount;
  } catch (error) {
    console.error("Server Action: Error updating product category:", error);
    throw new Error(error instanceof Error ? error.message : "更新产品分类失败");
  }
}

// 递归更新子分类的level和path
async function updateChildrenLevelAndPath(parentId: number, parentLevel: number, parentPath: string) {
  // 查找所有直接子分类
  const children = await prisma.productCategory.findMany({
    where: { parentId },
  });

  for (const child of children) {
    // 计算新的level和path
    const newLevel = parentLevel + 1;
    const newPath = parentPath ? `${parentPath},${parentId}` : `${parentId}`;

    // 更新子分类
    await prisma.productCategory.update({
      where: { id: child.id },
      data: {
        level: newLevel,
        path: newPath,
      },
    });

    // 递归更新子分类的子分类
    await updateChildrenLevelAndPath(child.id, newLevel, newPath);
  }
}

export async function deleteProductCategory(id: number) {
  try {
    console.log("Server Action: Deleting product category:", id);

    // 检查分类是否存在
    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    if (!category) {
      throw new Error("分类不存在");
    }

    // 检查是否有子分类
    if (category._count.children > 0) {
      throw new Error("无法删除包含子分类的分类，请先删除所有子分类");
    }

    // 检查是否有关联的产品
    if (category._count.products > 0) {
      throw new Error(`无法删除分类。${category._count.products}个产品正在使用此分类，请先移除或删除所有关联的产品`);
    }

    // 删除分类
    await prisma.productCategory.delete({
      where: { id },
    });

    console.log("Server Action: Category deleted");

    // 重新验证多个路径，确保数据一致性
    revalidatePath("/settings");
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error deleting product category:", error);
    throw new Error(error instanceof Error ? error.message : "删除产品分类失败");
  }
}

// 计件工项相关操作
export async function getPieceWorkItems(type?: string) {
  try {
    const whereClause = type ? { type } : {}

    return await prisma.pieceWorkItem.findMany({
      where: whereClause,
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching piece work items:", error)
    throw new Error("Failed to fetch piece work items")
  }
}

export async function createPieceWorkItem(data: any) {
  try {
    const pieceWorkItem = await prisma.pieceWorkItem.create({
      data: {
        name: data.name,
        price: Number.parseFloat(data.price),
        type: data.type, // accessory or enamelling
      },
    })

    revalidatePath("/settings")
    return pieceWorkItem
  } catch (error) {
    console.error("Error creating piece work item:", error)
    throw new Error("Failed to create piece work item")
  }
}

export async function updatePieceWorkItem(id: number, data: any) {
  try {
    const pieceWorkItem = await prisma.pieceWorkItem.update({
      where: { id },
      data: {
        name: data.name,
        price: Number.parseFloat(data.price),
        type: data.type,
      },
    })

    revalidatePath("/settings")
    return pieceWorkItem
  } catch (error) {
    console.error("Error updating piece work item:", error)
    throw new Error("Failed to update piece work item")
  }
}

export async function deletePieceWorkItem(id: number) {
  try {
    await prisma.pieceWorkItem.delete({
      where: { id },
    })

    revalidatePath("/settings")
    return { success: true }
  } catch (error) {
    console.error("Error deleting piece work item:", error)
    throw new Error("Failed to delete piece work item")
  }
}

// 产品单位和材质相关操作
export async function getProductUnits() {
  try {
    console.log("Server Action: Fetching product units");

    // 从产品表中提取所有不同的单位
    const products = await prisma.product.findMany({
      select: {
        unit: true,
      },
      where: {
        unit: {
          not: null,
        },
      },
      distinct: ['unit'],
    });

    // 将单位转换为字符串数组
    const units = products
      .filter(p => p.unit) // 过滤掉null值
      .map(p => p.unit as string)
      .sort(); // 按字母顺序排序

    // 添加默认单位
    const defaultUnits = ["个", "件", "套", "对", "幅"];
    const allUnits = [...new Set([...defaultUnits, ...units])]; // 去重

    console.log("Server Action: Found units:", allUnits.length);

    return allUnits;
  } catch (error) {
    console.error("Server Action: Error fetching product units:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to fetch product units");
  }
}

export async function getProductMaterials() {
  try {
    console.log("Server Action: Fetching product materials");

    // 从产品表中提取所有不同的材质
    const products = await prisma.product.findMany({
      select: {
        material: true,
      },
      where: {
        material: {
          not: null,
        },
      },
      distinct: ['material'],
    });

    // 将材质转换为字符串数组
    const materials = products
      .filter(p => p.material) // 过滤掉null值
      .map(p => p.material as string)
      .sort(); // 按字母顺序排序

    // 添加默认材质
    const defaultMaterials = ["铜", "银", "金", "珐琅", "玉石", "木质", "陶瓷"];
    const allMaterials = [...new Set([...defaultMaterials, ...materials])]; // 去重

    console.log("Server Action: Found materials:", allMaterials.length);

    return allMaterials;
  } catch (error) {
    console.error("Server Action: Error fetching product materials:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to fetch product materials");
  }
}

export async function addProductUnit(unit: string) {
  try {
    console.log("Server Action: Adding product unit:", unit);

    if (!unit.trim()) {
      throw new Error("单位名称不能为空");
    }

    // 创建一个占位产品，用于存储单位
    const placeholderProduct = await prisma.product.create({
      data: {
        name: `${unit} 单位`,
        price: 0,
        commissionRate: 0,
        type: "unit_placeholder",
        unit: unit.trim(),
        description: `${unit} 单位的占位产品`,
      },
    });

    console.log("Server Action: Unit placeholder product created:", placeholderProduct);

    // 重新验证路径
    revalidatePath("/products");

    return { success: true, unit };
  } catch (error) {
    console.error("Server Action: Error adding product unit:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to add product unit");
  }
}

export async function removeProductUnit(unit: string) {
  try {
    console.log("Server Action: Removing product unit:", unit);

    if (!unit.trim()) {
      throw new Error("单位名称不能为空");
    }

    // 删除单位占位产品
    await prisma.product.deleteMany({
      where: {
        unit: unit.trim(),
        type: "unit_placeholder",
      },
    });

    console.log("Server Action: Unit placeholder products deleted");

    // 重新验证路径
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error removing product unit:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to remove product unit");
  }
}

export async function addProductMaterial(material: string) {
  try {
    console.log("Server Action: Adding product material:", material);

    if (!material.trim()) {
      throw new Error("材质名称不能为空");
    }

    // 创建一个占位产品，用于存储材质
    const placeholderProduct = await prisma.product.create({
      data: {
        name: `${material} 材质`,
        price: 0,
        commissionRate: 0,
        type: "material_placeholder",
        material: material.trim(),
        description: `${material} 材质的占位产品`,
      },
    });

    console.log("Server Action: Material placeholder product created:", placeholderProduct);

    // 重新验证路径
    revalidatePath("/products");

    return { success: true, material };
  } catch (error) {
    console.error("Server Action: Error adding product material:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to add product material");
  }
}

export async function removeProductMaterial(material: string) {
  try {
    console.log("Server Action: Removing product material:", material);

    if (!material.trim()) {
      throw new Error("材质名称不能为空");
    }

    // 删除材质占位产品
    await prisma.product.deleteMany({
      where: {
        material: material.trim(),
        type: "material_placeholder",
      },
    });

    console.log("Server Action: Material placeholder products deleted");

    // 重新验证路径
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error removing product material:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to remove product material");
  }
}

// 产品标签相关操作
export async function getProductTags() {
  try {
    console.log("Server Action: Fetching product tags");

    // 获取所有标签，并计算每个标签关联的产品数量
    const tags = await prisma.productTag.findMany({
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // 转换为前端需要的格式
    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color || "#3b82f6",
      description: tag.description,
      productCount: tag._count.products,
    }));

    console.log("Server Action: Found tags:", formattedTags.length);

    return formattedTags;
  } catch (error) {
    console.error("Server Action: Error fetching product tags:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to fetch product tags");
  }
}

export async function createProductTag(data: { name: string; color?: string; description?: string }) {
  try {
    console.log("Server Action: Creating product tag:", data);

    if (!data.name.trim()) {
      throw new Error("标签名称不能为空");
    }

    // 检查标签名称是否已存在
    const existingTag = await prisma.productTag.findUnique({
      where: {
        name: data.name.trim(),
      },
    });

    if (existingTag) {
      throw new Error("标签名称已存在");
    }

    // 创建标签
    const tag = await prisma.productTag.create({
      data: {
        name: data.name.trim(),
        color: data.color || "#3b82f6",
        description: data.description,
      },
    });

    console.log("Server Action: Product tag created:", tag);

    // 重新验证路径
    revalidatePath("/products");

    return tag;
  } catch (error) {
    console.error("Server Action: Error creating product tag:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create product tag");
  }
}

export async function updateProductTag(id: number, data: { name?: string; color?: string; description?: string }) {
  try {
    console.log("Server Action: Updating product tag:", id, data);

    if (data.name && !data.name.trim()) {
      throw new Error("标签名称不能为空");
    }

    // 如果要更新名称，检查名称是否已存在
    if (data.name) {
      const existingTag = await prisma.productTag.findFirst({
        where: {
          name: data.name.trim(),
          id: {
            not: id,
          },
        },
      });

      if (existingTag) {
        throw new Error("标签名称已存在");
      }
    }

    // 更新标签
    const tag = await prisma.productTag.update({
      where: {
        id,
      },
      data: {
        name: data.name ? data.name.trim() : undefined,
        color: data.color,
        description: data.description,
      },
    });

    console.log("Server Action: Product tag updated:", tag);

    // 重新验证路径
    revalidatePath("/products");

    return tag;
  } catch (error) {
    console.error("Server Action: Error updating product tag:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update product tag");
  }
}

export async function deleteProductTag(id: number) {
  try {
    console.log("Server Action: Deleting product tag:", id);

    // 删除标签
    await prisma.productTag.delete({
      where: {
        id,
      },
    });

    console.log("Server Action: Product tag deleted");

    // 重新验证路径
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error deleting product tag:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete product tag");
  }
}

export async function addTagToProduct(productId: number, tagId: number) {
  try {
    console.log("Server Action: Adding tag to product:", productId, tagId);

    // 检查产品和标签是否存在
    const [product, tag] = await Promise.all([
      prisma.product.findUnique({ where: { id: productId } }),
      prisma.productTag.findUnique({ where: { id: tagId } }),
    ]);

    if (!product) {
      throw new Error("产品不存在");
    }

    if (!tag) {
      throw new Error("标签不存在");
    }

    // 检查关联是否已存在
    const existingRelation = await prisma.productTagsOnProducts.findUnique({
      where: {
        productId_tagId: {
          productId,
          tagId,
        },
      },
    });

    if (existingRelation) {
      // 关联已存在，不需要重复添加
      return { success: true, alreadyExists: true };
    }

    // 创建关联
    await prisma.productTagsOnProducts.create({
      data: {
        productId,
        tagId,
      },
    });

    console.log("Server Action: Tag added to product");

    // 重新验证路径
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error adding tag to product:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to add tag to product");
  }
}

export async function removeTagFromProduct(productId: number, tagId: number) {
  try {
    console.log("Server Action: Removing tag from product:", productId, tagId);

    // 删除关联
    await prisma.productTagsOnProducts.delete({
      where: {
        productId_tagId: {
          productId,
          tagId,
        },
      },
    });

    console.log("Server Action: Tag removed from product");

    // 重新验证路径
    revalidatePath("/products");

    return { success: true };
  } catch (error) {
    console.error("Server Action: Error removing tag from product:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to remove tag from product");
  }
}

export async function getProductsByTag(tagId: number) {
  try {
    console.log("Server Action: Fetching products by tag:", tagId);

    // 获取标签关联的产品
    const products = await prisma.product.findMany({
      where: {
        productTags: {
          some: {
            tagId,
          },
        },
        type: "product", // 只获取产品类型
      },
      orderBy: {
        name: "asc",
      },
    });

    console.log("Server Action: Found products:", products.length);

    return products;
  } catch (error) {
    console.error("Server Action: Error fetching products by tag:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to fetch products by tag");
  }
}

// 系统设置相关操作
export async function getSystemSettings() {
  try {
    // 获取系统设置，如果不存在则创建默认设置
    let settings = await prisma.systemSetting.findFirst()

    if (!settings) {
      settings = await prisma.systemSetting.create({
        data: {
          companyName: "聆花掐丝珐琅馆",
          coffeeSalesCommissionRate: 20,
          gallerySalesCommissionRate: 10,
          teacherWorkshopFee: 200,
          assistantWorkshopFee: 130,
          teacherWorkshopFeeOutside: 200,
          assistantWorkshopFeeOutside: 130,
          teacherWorkshopFeeInside: 180,
          assistantWorkshopFeeInside: 110,
          enableImageUpload: true,
          enableNotifications: true,
          // 薪资计算规则默认值
          basicWorkingHours: 8,
          basicWorkingDays: 22,
          overtimeRate: 1.5,
          weekendOvertimeRate: 2,
          holidayOvertimeRate: 3,
          socialInsuranceRate: 0,
          taxRate: 0,
        },
      })
    }

    return settings
  } catch (error) {
    console.error("Error fetching settings:", error)
    throw new Error("Failed to fetch settings")
  }
}

export async function updateSystemSettings(data: any) {
  try {
    // 获取当前设置
    let settings = await prisma.systemSetting.findFirst()

    if (settings) {
      // 更新现有设置
      settings = await prisma.systemSetting.update({
        where: { id: settings.id },
        data: {
          companyName: data.companyName,
          coffeeSalesCommissionRate: Number.parseFloat(data.coffeeSalesCommissionRate),
          gallerySalesCommissionRate: Number.parseFloat(data.gallerySalesCommissionRate),
          teacherWorkshopFee: Number.parseFloat(data.teacherWorkshopFee),
          assistantWorkshopFee: Number.parseFloat(data.assistantWorkshopFee),
          teacherWorkshopFeeOutside: Number.parseFloat(data.teacherWorkshopFeeOutside || "200"),
          assistantWorkshopFeeOutside: Number.parseFloat(data.assistantWorkshopFeeOutside || "130"),
          teacherWorkshopFeeInside: Number.parseFloat(data.teacherWorkshopFeeInside || "180"),
          assistantWorkshopFeeInside: Number.parseFloat(data.assistantWorkshopFeeInside || "110"),
          enableImageUpload: data.enableImageUpload,
          enableNotifications: data.enableNotifications,
          // 薪资计算规则
          basicWorkingHours: Number.parseFloat(data.basicWorkingHours || 8),
          basicWorkingDays: Number.parseInt(data.basicWorkingDays || 22),
          overtimeRate: Number.parseFloat(data.overtimeRate || 1.5),
          weekendOvertimeRate: Number.parseFloat(data.weekendOvertimeRate || 2),
          holidayOvertimeRate: Number.parseFloat(data.holidayOvertimeRate || 3),
          socialInsuranceRate: Number.parseFloat(data.socialInsuranceRate || 0),
          taxRate: Number.parseFloat(data.taxRate || 0),
        },
      })
    } else {
      // 创建新设置
      settings = await prisma.systemSetting.create({
        data: {
          companyName: data.companyName,
          coffeeSalesCommissionRate: Number.parseFloat(data.coffeeSalesCommissionRate),
          gallerySalesCommissionRate: Number.parseFloat(data.gallerySalesCommissionRate),
          teacherWorkshopFee: Number.parseFloat(data.teacherWorkshopFee),
          assistantWorkshopFee: Number.parseFloat(data.assistantWorkshopFee),
          teacherWorkshopFeeOutside: Number.parseFloat(data.teacherWorkshopFeeOutside || "200"),
          assistantWorkshopFeeOutside: Number.parseFloat(data.assistantWorkshopFeeOutside || "130"),
          teacherWorkshopFeeInside: Number.parseFloat(data.teacherWorkshopFeeInside || "180"),
          assistantWorkshopFeeInside: Number.parseFloat(data.assistantWorkshopFeeInside || "110"),
          enableImageUpload: data.enableImageUpload,
          enableNotifications: data.enableNotifications,
          // 薪资计算规则
          basicWorkingHours: Number.parseFloat(data.basicWorkingHours || 8),
          basicWorkingDays: Number.parseInt(data.basicWorkingDays || 22),
          overtimeRate: Number.parseFloat(data.overtimeRate || 1.5),
          weekendOvertimeRate: Number.parseFloat(data.weekendOvertimeRate || 2),
          holidayOvertimeRate: Number.parseFloat(data.holidayOvertimeRate || 3),
          socialInsuranceRate: Number.parseFloat(data.socialInsuranceRate || 0),
          taxRate: Number.parseFloat(data.taxRate || 0),
        },
      })
    }

    revalidatePath("/settings")
    return settings
  } catch (error) {
    console.error("Error updating settings:", error)
    throw new Error("Failed to update settings")
  }
}

// 销售记录相关操作
export async function createGallerySale(data: any) {
  try {
    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建销售记录
      const gallerySale = await tx.gallerySale.create({
        data: {
          employeeId: Number.parseInt(data.employee),
          date: new Date(data.date),
          totalAmount: Number.parseFloat(data.totalAmount),
          notes: data.notes,
          imageUrl: data.imageUrl,
        },
      })

      // 创建销售项目明细
      for (const item of data.items) {
        await tx.salesItem.create({
          data: {
            gallerySaleId: gallerySale.id,
            productId: Number.parseInt(item.productId),
            quantity: Number.parseInt(item.quantity),
            price: Number.parseFloat(item.price),
          },
        })
      }

      return gallerySale
    })

    revalidatePath("/daily-log")
    revalidatePath("/")
    return result
  } catch (error) {
    console.error("Error creating gallery sale:", error)
    throw new Error("Failed to create gallery sale")
  }
}

// 手作团建记录相关操作
export async function createWorkshop(data: any) {
  try {
    // 验证必填字段
    if (!data.activityId) {
      throw new Error("团建活动是必填项")
    }

    // 检查团建活动是否存在且启用
    const activity = await prisma.workshopActivity.findUnique({
      where: {
        id: parseInt(data.activityId),
        isActive: true
      },
      include: {
        product: true
      }
    })

    if (!activity) {
      throw new Error("所选团建活动不存在或已禁用")
    }

    const workshop = await prisma.workshop.create({
      data: {
        teacherId: parseInt(data.teacherId),
        assistantId: data.assistantId ? parseInt(data.assistantId) : null,
        date: new Date(data.date),
        role: data.role || "teacher",
        locationType: data.locationType || "in-gallery",
        location: data.location || "珐琅馆",
        participants: parseInt(data.participants) || 0,
        duration: parseFloat(data.duration) || 2,
        notes: data.notes || "",
        productId: activity.productId, // 使用活动关联的产品
        activityId: parseInt(data.activityId),
        customerId: data.customerId ? parseInt(data.customerId) : null,
        channelId: data.channelId ? parseInt(data.channelId) : null,
        status: data.status || "completed",
      },
    })

    revalidatePath("/daily-log")
    revalidatePath("/workshop")
    revalidatePath("/")
    return workshop
  } catch (error) {
    console.error("Error creating workshop:", error)
    throw new Error("Failed to create workshop")
  }
}

export async function updateWorkshop(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.activityId) {
      throw new Error("团建活动是必填项")
    }

    // 检查团建活动是否存在且启用
    const activity = await prisma.workshopActivity.findUnique({
      where: {
        id: parseInt(data.activityId),
        isActive: true
      },
      include: {
        product: true
      }
    })

    if (!activity) {
      throw new Error("所选团建活动不存在或已禁用")
    }

    const workshop = await prisma.workshop.update({
      where: { id },
      data: {
        teacherId: parseInt(data.teacherId),
        assistantId: data.assistantId ? parseInt(data.assistantId) : null,
        date: new Date(data.date),
        role: data.role,
        locationType: data.locationType,
        location: data.location,
        participants: parseInt(data.participants),
        duration: parseFloat(data.duration),
        notes: data.notes,
        productId: activity.productId, // 使用活动关联的产品
        activityId: parseInt(data.activityId),
        customerId: data.customerId ? parseInt(data.customerId) : null,
        channelId: data.channelId ? parseInt(data.channelId) : null,
        status: data.status,
      },
    })

    revalidatePath("/daily-log")
    revalidatePath("/workshop")
    revalidatePath("/")
    return workshop
  } catch (error) {
    console.error("Error updating workshop:", error)
    throw new Error("Failed to update workshop")
  }
}

export async function deleteWorkshop(id: number) {
  try {
    await prisma.workshop.delete({
      where: { id },
    })

    revalidatePath("/daily-log")
    revalidatePath("/workshop")
    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error deleting workshop:", error)
    throw new Error("Failed to delete workshop")
  }
}

// 团建活动相关操作
export async function getWorkshopActivities(isActive?: boolean) {
  try {
    const whereClause = isActive !== undefined ? { isActive } : {}

    return await prisma.workshopActivity.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            imageUrl: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching workshop activities:", error)
    throw new Error("获取团建活动失败")
  }
}

export async function createWorkshopActivity(data: any) {
  try {
    const activity = await prisma.workshopActivity.create({
      data: {
        name: data.name,
        description: data.description || "",
        productId: parseInt(data.productId),
        duration: parseFloat(data.duration) || 2,
        minParticipants: parseInt(data.minParticipants) || 5,
        maxParticipants: parseInt(data.maxParticipants) || 20,
        price: parseFloat(data.price) || 0,
        materialFee: parseFloat(data.materialFee) || 0,
        teacherFee: parseFloat(data.teacherFee) || 0,
        assistantFee: parseFloat(data.assistantFee) || 0,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            imageUrl: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return activity
  } catch (error) {
    console.error("Error creating workshop activity:", error)
    throw new Error("Failed to create workshop activity")
  }
}

export async function updateWorkshopActivity(id: number, data: any) {
  try {
    const activity = await prisma.workshopActivity.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description,
        productId: parseInt(data.productId),
        duration: parseFloat(data.duration),
        minParticipants: parseInt(data.minParticipants),
        maxParticipants: parseInt(data.maxParticipants),
        price: parseFloat(data.price),
        materialFee: parseFloat(data.materialFee) || 0,
        teacherFee: parseFloat(data.teacherFee) || 0,
        assistantFee: parseFloat(data.assistantFee) || 0,
        isActive: data.isActive,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            imageUrl: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return activity
  } catch (error) {
    console.error("Error updating workshop activity:", error)
    throw new Error("Failed to update workshop activity")
  }
}

export async function deleteWorkshopActivity(id: number) {
  try {
    // 检查是否有关联的团建记录
    const existingActivity = await prisma.workshopActivity.findUnique({
      where: { id },
      include: {
        workshops: {
          select: { id: true },
        },
      },
    })

    if (existingActivity?.workshops.length) {
      // 如果有关联记录，将关联记录的activityId设为null
      await prisma.workshop.updateMany({
        where: { activityId: id },
        data: { activityId: null },
      })
    }

    await prisma.workshopActivity.delete({
      where: { id },
    })

    revalidatePath("/workshop")
    return { success: true }
  } catch (error) {
    console.error("Error deleting workshop activity:", error)
    throw new Error("Failed to delete workshop activity")
  }
}

// 计件工作记录相关操作
export async function createPieceWork(data: any) {
  try {
    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建计件工作记录
      const pieceWork = await tx.pieceWork.create({
        data: {
          employeeId: Number.parseInt(data.employee),
          date: new Date(data.date),
          workType: data.workType,
          totalAmount: Number.parseFloat(data.totalAmount),
          notes: data.notes,
        },
      })

      // 创建计件工作明细
      for (const item of data.items) {
        await tx.pieceWorkDetail.create({
          data: {
            pieceWorkId: pieceWork.id,
            pieceWorkItemId: Number.parseInt(item.itemId),
            quantity: Number.parseInt(item.quantity),
            price: Number.parseFloat(item.price),
          },
        })
      }

      return pieceWork
    })

    revalidatePath("/daily-log")
    revalidatePath("/")
    return result
  } catch (error) {
    console.error("Error creating piece work:", error)
    throw new Error("Failed to create piece work")
  }
}

// 咖啡店销售记录相关操作
export async function createCoffeeShopSale(data: any) {
  try {
    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建咖啡店销售记录
      const coffeeShopSale = await tx.coffeeShopSale.create({
        data: {
          date: new Date(data.date),
          totalSales: Number.parseFloat(data.totalSales),
          cashAmount: Number.parseFloat(data.cashAmount || 0),
          cardAmount: Number.parseFloat(data.cardAmount || 0),
          wechatAmount: Number.parseFloat(data.wechatAmount || 0),
          alipayAmount: Number.parseFloat(data.alipayAmount || 0),
          otherAmount: Number.parseFloat(data.otherAmount || 0),
          customerCount: Number.parseInt(data.customerCount || 0),
          notes: data.notes,
        },
      })

      // 创建值班员工记录
      for (const employeeId of data.staffOnDuty) {
        await tx.coffeeShopShift.create({
          data: {
            coffeeShopSaleId: coffeeShopSale.id,
            employeeId: Number.parseInt(employeeId),
          },
        })
      }

      // 创建销售项目记录
      if (data.items && data.items.length > 0) {
        for (const item of data.items) {
          await tx.coffeeShopItem.create({
            data: {
              coffeeShopSaleId: coffeeShopSale.id,
              name: item.name,
              category: item.category,
              quantity: Number.parseInt(item.quantity),
              unitPrice: Number.parseFloat(item.unitPrice),
              totalPrice: Number.parseFloat(item.totalPrice),
            },
          })
        }
      }

      return coffeeShopSale
    })

    revalidatePath("/daily-log")
    revalidatePath("/coffee-reports")
    revalidatePath("/")
    return result
  } catch (error) {
    console.error("Error creating coffee shop sale:", error)
    throw new Error("Failed to create coffee shop sale")
  }
}

// 获取指定日期的排班信息
export async function getSchedulesByDate(date: Date) {
  try {
    // 确保日期是当天的0点0分0秒
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    // 当天结束时间
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    return await prisma.schedule.findMany({
      where: {
        date: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        employee: true,
      },
      orderBy: {
        startTime: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching schedules by date:", error)
    throw new Error("Failed to fetch schedules by date")
  }
}

// 排班相关操作
export async function getSchedules(startDate?: Date, endDate?: Date) {
  try {
    let whereClause = {}

    if (startDate && endDate) {
      whereClause = {
        date: {
          gte: startDate,
          lte: endDate,
        },
      }
    }

    return await prisma.schedule.findMany({
      where: whereClause,
      include: {
        employee: true,
      },
      orderBy: {
        date: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching schedules:", error)
    throw new Error("Failed to fetch schedules")
  }
}

export async function createSchedule(data: any) {
  try {
    const schedule = await prisma.schedule.create({
      data: {
        employeeId: Number.parseInt(data.employeeId),
        date: new Date(data.date),
        startTime: data.startTime,
        endTime: data.endTime,
        note: data.note || null,
      },
      include: {
        employee: true,
      },
    })

    revalidatePath("/schedule")
    return schedule
  } catch (error) {
    console.error("Error creating schedule:", error)
    throw new Error("Failed to create schedule")
  }
}

export async function deleteSchedule(id: number) {
  try {
    await prisma.schedule.delete({
      where: { id },
    })

    revalidatePath("/schedule")
    return { success: true }
  } catch (error) {
    console.error("Error deleting schedule:", error)
    throw new Error("Failed to delete schedule")
  }
}

export async function clearAllSchedules() {
  try {
    await prisma.schedule.deleteMany({})

    revalidatePath("/schedule")
    return { success: true }
  } catch (error) {
    console.error("Error clearing all schedules:", error)
    throw new Error("Failed to clear all schedules")
  }
}

export async function createBatchSchedules(scheduleRequests: any[]) {
  try {
    const createdSchedules = []

    for (const request of scheduleRequests) {
      const schedule = await prisma.schedule.create({
        data: {
          employeeId: Number.parseInt(request.employeeId),
          date: new Date(request.date),
          startTime: request.startTime,
          endTime: request.endTime,
          note: request.note || null,
        },
        include: {
          employee: true,
        },
      })

      createdSchedules.push(schedule)
    }

    revalidatePath("/schedule")
    return createdSchedules
  } catch (error) {
    console.error("Error creating batch schedules:", error)
    throw new Error("Failed to create batch schedules")
  }
}

export async function getScheduleTemplates() {
  try {
    return await prisma.scheduleTemplate.findMany({
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching schedule templates:", error)
    throw new Error("Failed to fetch schedule templates")
  }
}

export async function createScheduleTemplate(data: any) {
  try {
    // 如果设置为默认模板，先将其他模板设为非默认
    if (data.isDefault) {
      await prisma.scheduleTemplate.updateMany({
        data: {
          isDefault: false,
        },
      })
    }

    // 如果是更新现有模板
    if (data.id) {
      const template = await prisma.scheduleTemplate.update({
        where: { id: Number(data.id) },
        data: {
          name: data.name,
          startTime: data.startTime,
          endTime: data.endTime,
          weekdays: data.weekdays,
          employeeIds: data.employeeIds || [],
          isDefault: data.isDefault || false,
        },
      })

      revalidatePath("/schedule")
      return template
    }

    // 创建新模板
    const template = await prisma.scheduleTemplate.create({
      data: {
        name: data.name,
        startTime: data.startTime,
        endTime: data.endTime,
        weekdays: data.weekdays,
        employeeIds: data.employeeIds || [],
        isDefault: data.isDefault || false,
      },
    })

    revalidatePath("/schedule")
    return template
  } catch (error) {
    console.error("Error creating/updating schedule template:", error)
    throw new Error("Failed to create/update schedule template")
  }
}

export async function deleteScheduleTemplate(id: number) {
  try {
    await prisma.scheduleTemplate.delete({
      where: { id },
    })

    revalidatePath("/schedule")
    return { success: true }
  } catch (error) {
    console.error("Error deleting schedule template:", error)
    throw new Error("Failed to delete schedule template")
  }
}

// 仓库相关操作
export async function getWarehouses() {
  try {
    return await prisma.warehouse.findMany({
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching warehouses:", error)
    throw new Error("Failed to fetch warehouses")
  }
}

export async function createWarehouse(data: any) {
  try {
    const warehouse = await prisma.warehouse.create({
      data: {
        name: data.name,
        type: data.type,
        location: data.location || null,
        description: data.description || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })

    revalidatePath("/inventory")
    return warehouse
  } catch (error) {
    console.error("Error creating warehouse:", error)
    throw new Error("Failed to create warehouse")
  }
}

export async function updateWarehouse(id: number, data: any) {
  try {
    const warehouse = await prisma.warehouse.update({
      where: { id },
      data: {
        name: data.name,
        type: data.type,
        location: data.location,
        description: data.description,
        isActive: data.isActive,
      },
    })

    revalidatePath("/inventory")
    return warehouse
  } catch (error) {
    console.error("Error updating warehouse:", error)
    throw new Error("Failed to update warehouse")
  }
}

export async function deleteWarehouse(id: number) {
  try {
    // 检查仓库是否有库存
    const inventoryCount = await prisma.inventoryItem.count({
      where: { warehouseId: id },
    })

    if (inventoryCount > 0) {
      throw new AppError("仓库中存在库存，无法删除", 400)
    }

    await prisma.warehouse.delete({
      where: { id },
    })

    revalidatePath("/inventory")
    return { success: true }
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error deleting warehouse:", error)
    throw new Error("Failed to delete warehouse")
  }
}

// 薪资记录相关操作
export async function getSalaryRecords(employeeId?: number, year?: number, month?: number) {
  try {
    let whereClause: any = {}

    if (employeeId) {
      whereClause.employeeId = employeeId
    }

    if (year) {
      whereClause.year = year
    }

    if (month) {
      whereClause.month = month
    }

    return await prisma.salaryRecord.findMany({
      where: whereClause,
      include: {
        employee: true,
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
      ],
    })
  } catch (error) {
    console.error("Error fetching salary records:", error)
    throw new Error("Failed to fetch salary records")
  }
}

export async function getSalaryRecord(id: number) {
  try {
    return await prisma.salaryRecord.findUnique({
      where: { id },
      include: {
        employee: true,
      },
    })
  } catch (error) {
    console.error("Error fetching salary record:", error)
    throw new Error("Failed to fetch salary record")
  }
}

export async function createSalaryRecord(data: any) {
  try {
    // 检查是否已存在该员工该月的薪资记录
    const existingRecord = await prisma.salaryRecord.findFirst({
      where: {
        employeeId: Number.parseInt(data.employeeId),
        year: Number.parseInt(data.year),
        month: Number.parseInt(data.month),
      },
    })

    if (existingRecord) {
      throw new AppError(`员工ID ${data.employeeId} 在 ${data.year}年${data.month}月 已有薪资记录`, 400)
    }

    // 计算总收入和实发工资
    const totalIncome =
      Number.parseFloat(data.baseSalary || 0) +
      Number.parseFloat(data.scheduleSalary || 0) +
      Number.parseFloat(data.salesCommission || 0) +
      Number.parseFloat(data.pieceWorkIncome || 0) +
      Number.parseFloat(data.workshopIncome || 0) +
      Number.parseFloat(data.coffeeShiftCommission || 0) +
      Number.parseFloat(data.overtimePay || 0) +
      Number.parseFloat(data.bonus || 0)

    const deductions =
      Number.parseFloat(data.deductions || 0) +
      Number.parseFloat(data.socialInsurance || 0) +
      Number.parseFloat(data.tax || 0)

    const netIncome = totalIncome - deductions

    const salaryRecord = await prisma.salaryRecord.create({
      data: {
        employeeId: Number.parseInt(data.employeeId),
        year: Number.parseInt(data.year),
        month: Number.parseInt(data.month),
        baseSalary: Number.parseFloat(data.baseSalary || 0),
        scheduleSalary: Number.parseFloat(data.scheduleSalary || 0),
        salesCommission: Number.parseFloat(data.salesCommission || 0),
        pieceWorkIncome: Number.parseFloat(data.pieceWorkIncome || 0),
        workshopIncome: Number.parseFloat(data.workshopIncome || 0),
        coffeeShiftCommission: Number.parseFloat(data.coffeeShiftCommission || 0),
        overtimePay: Number.parseFloat(data.overtimePay || 0),
        bonus: Number.parseFloat(data.bonus || 0),
        deductions: Number.parseFloat(data.deductions || 0),
        socialInsurance: Number.parseFloat(data.socialInsurance || 0),
        tax: Number.parseFloat(data.tax || 0),
        totalIncome,
        netIncome,
        status: data.status || "draft",
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : null,
        notes: data.notes,
      },
    })

    revalidatePath("/employees")
    return salaryRecord
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error creating salary record:", error)
    throw new Error("Failed to create salary record")
  }
}

export async function updateSalaryRecord(id: number, data: any) {
  try {
    // 计算总收入和实发工资
    const totalIncome =
      Number.parseFloat(data.baseSalary || 0) +
      Number.parseFloat(data.scheduleSalary || 0) +
      Number.parseFloat(data.salesCommission || 0) +
      Number.parseFloat(data.pieceWorkIncome || 0) +
      Number.parseFloat(data.workshopIncome || 0) +
      Number.parseFloat(data.coffeeShiftCommission || 0) +
      Number.parseFloat(data.overtimePay || 0) +
      Number.parseFloat(data.bonus || 0)

    const deductions =
      Number.parseFloat(data.deductions || 0) +
      Number.parseFloat(data.socialInsurance || 0) +
      Number.parseFloat(data.tax || 0)

    const netIncome = totalIncome - deductions

    const salaryRecord = await prisma.salaryRecord.update({
      where: { id },
      data: {
        baseSalary: Number.parseFloat(data.baseSalary || 0),
        scheduleSalary: Number.parseFloat(data.scheduleSalary || 0),
        salesCommission: Number.parseFloat(data.salesCommission || 0),
        pieceWorkIncome: Number.parseFloat(data.pieceWorkIncome || 0),
        workshopIncome: Number.parseFloat(data.workshopIncome || 0),
        coffeeShiftCommission: Number.parseFloat(data.coffeeShiftCommission || 0),
        overtimePay: Number.parseFloat(data.overtimePay || 0),
        bonus: Number.parseFloat(data.bonus || 0),
        deductions: Number.parseFloat(data.deductions || 0),
        socialInsurance: Number.parseFloat(data.socialInsurance || 0),
        tax: Number.parseFloat(data.tax || 0),
        totalIncome,
        netIncome,
        status: data.status,
        paymentDate: data.paymentDate ? new Date(data.paymentDate) : null,
        notes: data.notes,
      },
    })

    revalidatePath("/employees")
    return salaryRecord
  } catch (error) {
    console.error("Error updating salary record:", error)
    throw new Error("Failed to update salary record")
  }
}

export async function deleteSalaryRecord(id: number) {
  try {
    // 检查薪资记录状态，只有草稿状态才能删除
    const salaryRecord = await prisma.salaryRecord.findUnique({
      where: { id },
    })

    if (!salaryRecord) {
      throw new AppError("薪资记录不存在", 404)
    }

    if (salaryRecord.status !== "draft") {
      throw new AppError("只能删除草稿状态的薪资记录", 400)
    }

    await prisma.salaryRecord.delete({
      where: { id },
    })

    revalidatePath("/employees")
    return { success: true }
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error deleting salary record:", error)
    throw new Error("Failed to delete salary record")
  }
}

// 薪资调整记录相关操作
export async function getSalaryAdjustments(employeeId?: number) {
  try {
    let whereClause: any = {}

    if (employeeId) {
      whereClause.employeeId = employeeId
    }

    return await prisma.salaryAdjustment.findMany({
      where: whereClause,
      orderBy: {
        adjustmentDate: 'desc',
      },
    })
  } catch (error) {
    console.error("Error fetching salary adjustments:", error)
    throw new Error("Failed to fetch salary adjustments")
  }
}

export async function createSalaryAdjustment(data: any) {
  try {
    // 获取员工当前日薪
    const employee = await prisma.employee.findUnique({
      where: { id: Number.parseInt(data.employeeId) },
    })

    if (!employee) {
      throw new AppError("员工不存在", 404)
    }

    // 创建薪资调整记录
    const salaryAdjustment = await prisma.salaryAdjustment.create({
      data: {
        employeeId: Number.parseInt(data.employeeId),
        adjustmentDate: new Date(data.adjustmentDate),
        oldSalary: employee.dailySalary,
        newSalary: Number.parseFloat(data.newSalary),
        reason: data.reason,
        approvedBy: data.approvedBy,
        notes: data.notes,
      },
    })

    // 更新员工日薪
    await prisma.employee.update({
      where: { id: Number.parseInt(data.employeeId) },
      data: {
        dailySalary: Number.parseFloat(data.newSalary),
      },
    })

    revalidatePath("/employees")
    return salaryAdjustment
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error creating salary adjustment:", error)
    throw new Error("Failed to create salary adjustment")
  }
}

// 供应商相关操作
export async function getSuppliers(isActive?: boolean) {
  try {
    let whereClause = {}

    if (isActive !== undefined) {
      whereClause = {
        ...whereClause,
        isActive,
      }
    }

    return await prisma.supplier.findMany({
      where: whereClause,
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching suppliers:", error)
    throw new Error("Failed to fetch suppliers")
  }
}

export async function createSupplier(data: any) {
  try {
    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("供应商名称不能为空")
    }

    const supplier = await prisma.supplier.create({
      data: {
        name: data.name.trim(),
        contactPerson: data.contactPerson || null,
        phone: data.phone || null,
        email: data.email || null,
        address: data.address || null,
        description: data.description || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })

    revalidatePath("/purchase")
    return supplier
  } catch (error) {
    console.error("Error creating supplier:", error)
    throw new Error("Failed to create supplier")
  }
}

export async function updateSupplier(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("供应商名称不能为空")
    }

    const supplier = await prisma.supplier.update({
      where: { id },
      data: {
        name: data.name.trim(),
        contactPerson: data.contactPerson || null,
        phone: data.phone || null,
        email: data.email || null,
        address: data.address || null,
        description: data.description || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })

    revalidatePath("/purchase")
    return supplier
  } catch (error) {
    console.error("Error updating supplier:", error)
    throw new Error("Failed to update supplier")
  }
}

export async function deleteSupplier(id: number) {
  try {
    // 检查是否有关联的采购订单
    const purchaseOrderCount = await prisma.purchaseOrder.count({
      where: { supplierId: id },
    })

    if (purchaseOrderCount > 0) {
      throw new Error("该供应商有关联的采购订单，无法删除")
    }

    await prisma.supplier.delete({
      where: { id },
    })

    revalidatePath("/purchase")
    return { success: true }
  } catch (error) {
    console.error("Error deleting supplier:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to delete supplier")
  }
}

// 采购订单相关操作
export async function getPurchaseOrders(
  status?: string,
  supplierId?: number,
  employeeId?: number,
  startDate?: string,
  endDate?: string,
  limit: number = 50,
  offset: number = 0
) {
  try {
    let whereClause = {}

    if (status) {
      whereClause = {
        ...whereClause,
        status,
      }
    }

    if (supplierId) {
      whereClause = {
        ...whereClause,
        supplierId: Number(supplierId),
      }
    }

    if (employeeId) {
      whereClause = {
        ...whereClause,
        employeeId: Number(employeeId),
      }
    }

    if (startDate && endDate) {
      whereClause = {
        ...whereClause,
        orderDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      }
    }

    // 获取总记录数
    const total = await prisma.purchaseOrder.count({
      where: whereClause,
    })

    // 获取分页数据
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: whereClause,
      include: {
        supplier: true,
        employee: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        orderDate: "desc",
      },
      skip: offset,
      take: limit,
    })

    return {
      total,
      offset,
      limit,
      data: purchaseOrders,
    }
  } catch (error) {
    console.error("Error fetching purchase orders:", error)
    throw new Error("Failed to fetch purchase orders")
  }
}

// 生成采购订单编号
function generatePurchaseOrderNumber() {
  const now = new Date()
  const year = now.getFullYear().toString().slice(2)
  const month = (now.getMonth() + 1).toString().padStart(2, "0")
  const day = now.getDate().toString().padStart(2, "0")
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0")
  return `PO${year}${month}${day}${random}`
}

export async function createPurchaseOrder(data: any) {
  try {
    // 验证必填字段
    if (!data.supplierId || !data.employeeId || !data.items || data.items.length === 0) {
      throw new Error("供应商、员工和订单项为必填项")
    }

    // 生成订单编号
    const orderNumber = generatePurchaseOrderNumber()

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建采购订单
      const purchaseOrder = await tx.purchaseOrder.create({
        data: {
          orderNumber,
          supplierId: Number(data.supplierId),
          employeeId: Number(data.employeeId),
          orderDate: data.orderDate ? new Date(data.orderDate) : new Date(),
          expectedDate: data.expectedDate ? new Date(data.expectedDate) : null,
          status: data.status || "pending",
          totalAmount: Number(data.totalAmount),
          paidAmount: Number(data.paidAmount || 0),
          paymentStatus: data.paymentStatus || "unpaid",
          paymentMethod: data.paymentMethod,
          notes: data.notes,
        },
      })

      // 创建采购订单项
      for (const item of data.items) {
        await tx.purchaseOrderItem.create({
          data: {
            purchaseOrderId: purchaseOrder.id,
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            price: Number(item.price),
            receivedQuantity: Number(item.receivedQuantity || 0),
            notes: item.notes,
          },
        })
      }

      return purchaseOrder
    })

    revalidatePath("/purchase")
    return result
  } catch (error) {
    console.error("Error creating purchase order:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to create purchase order")
  }
}

export async function updatePurchaseOrder(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.supplierId || !data.employeeId) {
      throw new Error("供应商和员工为必填项")
    }

    // 获取原采购订单信息
    const originalPurchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        items: true,
      },
    })

    if (!originalPurchaseOrder) {
      throw new Error("采购订单不存在")
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 更新采购订单
      const purchaseOrder = await tx.purchaseOrder.update({
        where: { id },
        data: {
          supplierId: Number(data.supplierId),
          employeeId: Number(data.employeeId),
          orderDate: data.orderDate ? new Date(data.orderDate) : originalPurchaseOrder.orderDate,
          expectedDate: data.expectedDate ? new Date(data.expectedDate) : originalPurchaseOrder.expectedDate,
          status: data.status || originalPurchaseOrder.status,
          totalAmount: Number(data.totalAmount),
          paidAmount: Number(data.paidAmount || 0),
          paymentStatus: data.paymentStatus || originalPurchaseOrder.paymentStatus,
          paymentMethod: data.paymentMethod,
          notes: data.notes,
        },
      })

      // 如果有更新订单项
      if (data.items && data.items.length > 0) {
        // 删除原有订单项
        await tx.purchaseOrderItem.deleteMany({
          where: { purchaseOrderId: id },
        })

        // 创建新订单项
        for (const item of data.items) {
          await tx.purchaseOrderItem.create({
            data: {
              purchaseOrderId: purchaseOrder.id,
              productId: Number(item.productId),
              quantity: Number(item.quantity),
              price: Number(item.price),
              receivedQuantity: Number(item.receivedQuantity || 0),
              notes: item.notes,
            },
          })
        }
      }

      return purchaseOrder
    })

    revalidatePath("/purchase")
    return result
  } catch (error) {
    console.error("Error updating purchase order:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to update purchase order")
  }
}

export async function deletePurchaseOrder(id: number) {
  try {
    // 获取采购订单信息
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        items: true,
      },
    })

    if (!purchaseOrder) {
      throw new Error("采购订单不存在")
    }

    // 检查是否已经有入库记录
    const hasReceivedItems = purchaseOrder.items.some(item => item.receivedQuantity > 0)
    if (hasReceivedItems) {
      throw new Error("该采购订单已有入库记录，无法删除")
    }

    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 删除订单项
      await tx.purchaseOrderItem.deleteMany({
        where: { purchaseOrderId: id },
      })

      // 删除采购订单
      await tx.purchaseOrder.delete({
        where: { id },
      })
    })

    revalidatePath("/purchase")
    return { success: true }
  } catch (error) {
    console.error("Error deleting purchase order:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to delete purchase order")
  }
}

// 采购入库
export async function receivePurchaseOrder(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.warehouseId) {
      throw new Error("仓库为必填项")
    }

    // 获取采购订单信息
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!purchaseOrder) {
      throw new Error("采购订单不存在")
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: Number(data.warehouseId) },
    })

    if (!warehouse) {
      throw new Error("仓库不存在")
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 更新采购订单状态
      const updatedPurchaseOrder = await tx.purchaseOrder.update({
        where: { id },
        data: {
          status: "received",
        },
      })

      // 处理每个订单项
      for (const item of data.items) {
        const orderItem = purchaseOrder.items.find(i => i.id === Number(item.id))
        if (!orderItem) continue

        // 计算本次入库数量
        const receiveQuantity = Number(item.receiveQuantity || 0)
        if (receiveQuantity <= 0) continue

        // 更新订单项的已收货数量
        await tx.purchaseOrderItem.update({
          where: { id: orderItem.id },
          data: {
            receivedQuantity: orderItem.receivedQuantity + receiveQuantity,
          },
        })

        // 查找库存
        const inventoryItem = await tx.inventoryItem.findFirst({
          where: {
            warehouseId: Number(data.warehouseId),
            productId: orderItem.productId,
          },
        })

        // 更新库存
        if (inventoryItem) {
          await tx.inventoryItem.update({
            where: { id: inventoryItem.id },
            data: {
              quantity: inventoryItem.quantity + receiveQuantity,
            },
          })
        } else {
          await tx.inventoryItem.create({
            data: {
              warehouseId: Number(data.warehouseId),
              productId: orderItem.productId,
              quantity: receiveQuantity,
            },
          })
        }

        // 记录库存交易
        await tx.inventoryTransaction.create({
          data: {
            type: "in",
            targetWarehouseId: Number(data.warehouseId),
            productId: orderItem.productId,
            quantity: receiveQuantity,
            notes: `采购入库: ${purchaseOrder.orderNumber}`,
            referenceId: purchaseOrder.id,
            referenceType: "purchase",
          },
        })
      }

      return updatedPurchaseOrder
    })

    revalidatePath("/purchase")
    revalidatePath("/inventory")
    return result
  } catch (error) {
    console.error("Error receiving purchase order:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to receive purchase order")
  }
}

// 库存相关操作
export async function getInventory(warehouseId?: number, productId?: number) {
  try {
    let whereClause = {}

    if (warehouseId) {
      whereClause = {
        ...whereClause,
        warehouseId: Number(warehouseId),
      }
    }

    if (productId) {
      whereClause = {
        ...whereClause,
        productId: Number(productId),
      }
    }

    return await prisma.inventoryItem.findMany({
      where: whereClause,
      include: {
        product: true,
        warehouse: true,
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching inventory:", error)
    throw new Error("Failed to fetch inventory")
  }
}

export async function updateInventory(data: any) {
  try {
    // 验证必填字段
    if (!data.warehouseId || !data.productId || data.quantity === undefined) {
      throw new AppError("仓库ID、产品ID和数量为必填项", 400)
    }

    // 处理批量更新
    if (data.batchUpdate && Array.isArray(data.inventoryIds)) {
      const results = []

      for (const inventoryId of data.inventoryIds) {
        const inventoryItem = await prisma.inventoryItem.findUnique({
          where: { id: Number(inventoryId) },
          include: { product: true }
        })

        if (!inventoryItem) continue

        let newQuantity = inventoryItem.quantity

        // 根据操作类型计算新数量
        if (data.actionType === 'add') {
          newQuantity += Number(data.quantity)
        } else if (data.actionType === 'subtract') {
          newQuantity = Math.max(0, newQuantity - Number(data.quantity))
        } else if (data.actionType === 'set') {
          newQuantity = Number(data.quantity)
        }

        // 更新库存
        const updated = await prisma.inventoryItem.update({
          where: { id: Number(inventoryId) },
          data: { quantity: newQuantity },
          include: { product: true, warehouse: true }
        })

        // 记录交易
        const transactionQuantity = newQuantity - inventoryItem.quantity

        if (transactionQuantity !== 0) {
          await prisma.inventoryTransaction.create({
            data: {
              type: transactionQuantity > 0 ? "in" : "out",
              targetWarehouseId: inventoryItem.warehouseId,
              productId: inventoryItem.productId,
              quantity: Math.abs(transactionQuantity),
              notes: data.notes || "批量更新库存",
              referenceType: "batch",
            },
          })
        }

        results.push(updated)
      }

      revalidatePath("/inventory")
      return results
    }

    // 检查产品和仓库是否存在
    const product = await prisma.product.findUnique({
      where: { id: Number(data.productId) },
    })

    if (!product) {
      throw new AppError("产品不存在", 404)
    }

    const warehouse = await prisma.warehouse.findUnique({
      where: { id: Number(data.warehouseId) },
    })

    if (!warehouse) {
      throw new AppError("仓库不存在", 404)
    }

    // 查找现有库存
    const existingInventory = await prisma.inventoryItem.findFirst({
      where: {
        warehouseId: Number(data.warehouseId),
        productId: Number(data.productId),
      },
    })

    let inventoryItem

    if (existingInventory) {
      // 更新现有库存
      inventoryItem = await prisma.inventoryItem.update({
        where: { id: existingInventory.id },
        data: {
          quantity: Number(data.quantity),
          minQuantity: data.minQuantity ? Number(data.minQuantity) : existingInventory.minQuantity,
        },
        include: {
          product: true,
          warehouse: true,
        },
      })
    } else {
      // 创建新库存
      inventoryItem = await prisma.inventoryItem.create({
        data: {
          warehouseId: Number(data.warehouseId),
          productId: Number(data.productId),
          quantity: Number(data.quantity),
          minQuantity: data.minQuantity ? Number(data.minQuantity) : null,
        },
        include: {
          product: true,
          warehouse: true,
        },
      })
    }

    // 记录库存交易
    const transactionQuantity = existingInventory
      ? Number(data.quantity) - existingInventory.quantity
      : Number(data.quantity);

    // 只有当数量变化时才创建交易记录
    if (transactionQuantity !== 0) {
      await prisma.inventoryTransaction.create({
        data: {
          type: transactionQuantity > 0 ? "in" : "out", // 根据数量变化确定类型
          targetWarehouseId: Number(data.warehouseId),
          productId: Number(data.productId),
          quantity: Math.abs(transactionQuantity), // 使用绝对值
          notes: data.notes || "手动更新库存",
          referenceType: "manual",
        },
      })
    }

    revalidatePath("/inventory")
    return inventoryItem
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error updating inventory:", error)
    throw new Error("Failed to update inventory")
  }
}

export async function deleteInventory(inventoryId: number) {
  try {
    // 查找库存项
    const inventoryItem = await prisma.inventoryItem.findUnique({
      where: { id: Number(inventoryId) },
      include: { product: true }
    })

    if (!inventoryItem) {
      throw new AppError("库存项不存在", 404)
    }

    // 记录删除交易
    await prisma.inventoryTransaction.create({
      data: {
        type: "out",
        targetWarehouseId: inventoryItem.warehouseId,
        productId: inventoryItem.productId,
        quantity: inventoryItem.quantity,
        notes: "删除库存",
        referenceType: "delete",
      },
    })

    // 删除库存项
    await prisma.inventoryItem.delete({
      where: { id: Number(inventoryId) },
    })

    revalidatePath("/inventory")
    return { success: true }
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error deleting inventory:", error)
    throw new Error("Failed to delete inventory")
  }
}

export async function batchDeleteInventory(inventoryIds: number[]) {
  try {
    const results = []

    for (const id of inventoryIds) {
      try {
        const result = await deleteInventory(id)
        results.push({ id, success: true })
      } catch (error) {
        results.push({ id, success: false, error: error.message })
      }
    }

    revalidatePath("/inventory")
    return results
  } catch (error) {
    console.error("Error batch deleting inventory:", error)
    throw new Error("Failed to batch delete inventory")
  }
}

export async function transferInventory(data: any) {
  try {
    // 验证必填字段
    if (!data.sourceWarehouseId || !data.targetWarehouseId || !data.productId || data.quantity === undefined) {
      throw new AppError("源仓库ID、目标仓库ID、产品ID和数量为必填项", 400)
    }

    // 检查源仓库和目标仓库是否相同
    if (Number(data.sourceWarehouseId) === Number(data.targetWarehouseId)) {
      throw new AppError("源仓库和目标仓库不能相同", 400)
    }

    // 检查数量是否为正数
    if (Number(data.quantity) <= 0) {
      throw new AppError("转移数量必须大于0", 400)
    }

    // 检查源仓库库存是否足够
    const sourceInventory = await prisma.inventoryItem.findFirst({
      where: {
        warehouseId: Number(data.sourceWarehouseId),
        productId: Number(data.productId),
      },
    })

    if (!sourceInventory || sourceInventory.quantity < Number(data.quantity)) {
      throw new AppError("源仓库库存不足", 400)
    }

    // 开始事务，确保库存转移的原子性
    const result = await prisma.$transaction(async (tx) => {
      // 减少源仓库库存
      const updatedSourceInventory = await tx.inventoryItem.update({
        where: { id: sourceInventory.id },
        data: {
          quantity: sourceInventory.quantity - Number(data.quantity),
        },
      })

      // 查找目标仓库库存
      const targetInventory = await tx.inventoryItem.findFirst({
        where: {
          warehouseId: Number(data.targetWarehouseId),
          productId: Number(data.productId),
        },
      })

      let updatedTargetInventory

      if (targetInventory) {
        // 更新目标仓库库存
        updatedTargetInventory = await tx.inventoryItem.update({
          where: { id: targetInventory.id },
          data: {
            quantity: targetInventory.quantity + Number(data.quantity),
          },
        })
      } else {
        // 创建目标仓库库存
        updatedTargetInventory = await tx.inventoryItem.create({
          data: {
            warehouseId: Number(data.targetWarehouseId),
            productId: Number(data.productId),
            quantity: Number(data.quantity),
          },
        })
      }

      // 记录库存交易
      const transaction = await tx.inventoryTransaction.create({
        data: {
          type: "transfer",
          sourceWarehouseId: Number(data.sourceWarehouseId),
          targetWarehouseId: Number(data.targetWarehouseId),
          productId: Number(data.productId),
          quantity: Number(data.quantity),
          notes: data.notes || "库存转移",
          referenceType: "transfer",
          attachmentUrl: data.attachmentUrl || null,
        },
      })

      return {
        sourceInventory: updatedSourceInventory,
        targetInventory: updatedTargetInventory,
        transaction,
      }
    })

    revalidatePath("/inventory")
    return result
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error transferring inventory:", error)
    throw new Error("Failed to transfer inventory")
  }
}

export async function batchTransferInventory(data: any) {
  try {
    // 验证必填字段
    if (!data.sourceWarehouseId || !data.targetWarehouseId || !data.items || !Array.isArray(data.items) || data.items.length === 0) {
      throw new AppError("源仓库ID、目标仓库ID和转移项目为必填项", 400)
    }

    // 检查源仓库和目标仓库是否相同
    if (Number(data.sourceWarehouseId) === Number(data.targetWarehouseId)) {
      throw new AppError("源仓库和目标仓库不能相同", 400)
    }

    const results = {
      total: data.items.length,
      success: 0,
      failed: 0,
      errors: [],
      items: []
    }

    // 开始事务，确保批量转移的原子性
    const transferResults = await prisma.$transaction(async (tx) => {
      const transferredItems = []

      for (const item of data.items) {
        try {
          // 验证必填字段
          if (!item.productId) {
            throw new Error("产品ID为必填项")
          }

          if (item.quantity === undefined || Number(item.quantity) <= 0) {
            throw new Error("转移数量必须大于0")
          }

          // 检查源仓库库存是否足够
          const sourceInventory = await tx.inventoryItem.findFirst({
            where: {
              warehouseId: Number(data.sourceWarehouseId),
              productId: Number(item.productId),
            },
          })

          if (!sourceInventory || sourceInventory.quantity < Number(item.quantity)) {
            throw new Error(`产品ID ${item.productId} 的源仓库库存不足`)
          }

          // 减少源仓库库存
          const updatedSourceInventory = await tx.inventoryItem.update({
            where: { id: sourceInventory.id },
            data: {
              quantity: sourceInventory.quantity - Number(item.quantity),
            },
          })

          // 查找目标仓库库存
          const targetInventory = await tx.inventoryItem.findFirst({
            where: {
              warehouseId: Number(data.targetWarehouseId),
              productId: Number(item.productId),
            },
          })

          let updatedTargetInventory

          if (targetInventory) {
            // 更新目标仓库库存
            updatedTargetInventory = await tx.inventoryItem.update({
              where: { id: targetInventory.id },
              data: {
                quantity: targetInventory.quantity + Number(item.quantity),
              },
            })
          } else {
            // 创建目标仓库库存
            updatedTargetInventory = await tx.inventoryItem.create({
              data: {
                warehouseId: Number(data.targetWarehouseId),
                productId: Number(item.productId),
                quantity: Number(item.quantity),
              },
            })
          }

          // 记录库存交易
          const transaction = await tx.inventoryTransaction.create({
            data: {
              type: "transfer",
              sourceWarehouseId: Number(data.sourceWarehouseId),
              targetWarehouseId: Number(data.targetWarehouseId),
              productId: Number(item.productId),
              quantity: Number(item.quantity),
              notes: item.notes || data.notes || "批量转移",
              referenceType: "batch_transfer",
              attachmentUrl: data.attachmentUrl || null,
            },
          })

          transferredItems.push({
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            transaction: transaction.id,
            success: true
          })

          results.success++
        } catch (error) {
          results.failed++
          results.errors.push(error.message)
          transferredItems.push({
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            success: false,
            error: error.message
          })
        }
      }

      return transferredItems
    })

    results.items = transferResults

    revalidatePath("/inventory")
    return results
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error batch transferring inventory:", error)
    throw new Error("Failed to batch transfer inventory")
  }
}

export async function importInventory(data: any) {
  try {
    if (!data.warehouseId) {
      throw new AppError("仓库ID为必填项", 400)
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      throw new AppError("导入数据不能为空", 400)
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: Number(data.warehouseId) },
    })

    if (!warehouse) {
      throw new AppError("仓库不存在", 404)
    }

    const results = {
      total: data.items.length,
      success: 0,
      failed: 0,
      errors: [],
      items: []
    }

    // 处理每个导入项
    for (const item of data.items) {
      try {
        // 验证必填字段
        if (!item.productId && !item.sku && !item.barcode) {
          throw new Error("每个导入项必须包含产品ID、SKU或条码中的至少一个")
        }

        if (item.quantity === undefined) {
          throw new Error("库存数量为必填项")
        }

        // 查找产品
        let product

        if (item.productId) {
          product = await prisma.product.findUnique({
            where: { id: Number(item.productId) },
          })
        } else if (item.sku) {
          product = await prisma.product.findFirst({
            where: { sku: item.sku },
          })
        } else if (item.barcode) {
          product = await prisma.product.findFirst({
            where: { barcode: item.barcode },
          })
        }

        if (!product) {
          throw new Error("产品不存在")
        }

        // 查找现有库存
        const existingInventory = await prisma.inventoryItem.findFirst({
          where: {
            warehouseId: Number(data.warehouseId),
            productId: product.id,
          },
        })

        let inventoryItem

        if (existingInventory) {
          // 更新现有库存
          const newQuantity = data.updateMode === 'add'
            ? existingInventory.quantity + Number(item.quantity)
            : Number(item.quantity)

          inventoryItem = await prisma.inventoryItem.update({
            where: { id: existingInventory.id },
            data: {
              quantity: newQuantity,
              minQuantity: item.minQuantity ? Number(item.minQuantity) : existingInventory.minQuantity,
            },
            include: {
              product: true,
              warehouse: true,
            },
          })

          // 记录库存交易
          const transactionQuantity = newQuantity - existingInventory.quantity

          if (transactionQuantity !== 0) {
            await prisma.inventoryTransaction.create({
              data: {
                type: transactionQuantity > 0 ? "in" : "out",
                targetWarehouseId: Number(data.warehouseId),
                productId: product.id,
                quantity: Math.abs(transactionQuantity),
                notes: data.notes || "批量导入库存",
                referenceType: "import",
              },
            })
          }
        } else {
          // 创建新库存
          inventoryItem = await prisma.inventoryItem.create({
            data: {
              warehouseId: Number(data.warehouseId),
              productId: product.id,
              quantity: Number(item.quantity),
              minQuantity: item.minQuantity ? Number(item.minQuantity) : null,
            },
            include: {
              product: true,
              warehouse: true,
            },
          })

          // 记录库存交易
          await prisma.inventoryTransaction.create({
            data: {
              type: "in",
              targetWarehouseId: Number(data.warehouseId),
              productId: product.id,
              quantity: Number(item.quantity),
              notes: data.notes || "批量导入库存",
              referenceType: "import",
            },
          })
        }

        results.success++
        results.items.push({
          success: true,
          item: inventoryItem
        })
      } catch (error) {
        results.failed++
        results.errors.push(error.message)
        results.items.push({
          success: false,
          error: error.message,
          item
        })
      }
    }

    revalidatePath("/inventory")
    return results
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error importing inventory:", error)
    throw new Error("Failed to import inventory")
  }
}

export async function exportInventory(warehouseId?: number, format: string = 'csv') {
  try {
    // 获取库存数据
    const inventory = await getInventory(warehouseId)

    // 处理可能的特殊字符，确保CSV格式正确
    const escapeCSV = (value) => {
      if (value === null || value === undefined) return ''
      const stringValue = String(value)
      // 如果包含逗号、双引号或换行符，则需要用双引号包裹并转义内部的双引号
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`
      }
      return stringValue
    }

    if (format === 'csv') {
      // 构建CSV内容
      const headers = ['产品ID', '产品名称', 'SKU', '条码', '类别', '仓库ID', '仓库名称', '库存数量', '最低库存', '成本价', '售价']
      const rows = inventory.map(item => [
        item.productId,
        escapeCSV(item.product.name),
        escapeCSV(item.product.sku || ''),
        escapeCSV(item.product.barcode || ''),
        escapeCSV(item.product.category || '未分类'),
        item.warehouseId,
        escapeCSV(item.warehouse.name),
        item.quantity,
        item.minQuantity || '',
        item.product.cost || '',
        item.product.price || ''
      ])

      // 将数据转换为CSV格式
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n')

      return {
        content: csvContent,
        filename: `inventory_export_${new Date().toISOString().slice(0, 10)}.csv`,
        contentType: 'text/csv'
      }
    } else if (format === 'json') {
      // 创建一个简化的对象数组，避免循环引用
      const simplifiedInventory = inventory.map(item => ({
        id: item.id,
        productId: item.productId,
        productName: item.product.name,
        sku: item.product.sku || '',
        barcode: item.product.barcode || '',
        category: item.product.category || '未分类',
        warehouseId: item.warehouseId,
        warehouseName: item.warehouse.name,
        quantity: item.quantity,
        minQuantity: item.minQuantity || '',
        cost: item.product.cost || '',
        price: item.product.price || ''
      }))

      // 返回JSON格式
      return {
        content: JSON.stringify(simplifiedInventory, null, 2),
        filename: `inventory_export_${new Date().toISOString().slice(0, 10)}.json`,
        contentType: 'application/json'
      }
    } else {
      throw new AppError("不支持的导出格式", 400)
    }
  } catch (error) {
    console.error("Error exporting inventory:", error)
    throw new Error("Failed to export inventory")
  }
}

export async function getInventoryTransactions(warehouseId?: number, productId?: number, type?: string, limit: number = 50, offset: number = 0) {
  try {
    let whereClause = {}

    if (warehouseId) {
      whereClause = {
        ...whereClause,
        OR: [
          { sourceWarehouseId: Number(warehouseId) },
          { targetWarehouseId: Number(warehouseId) },
        ],
      }
    }

    if (productId) {
      whereClause = {
        ...whereClause,
        productId: Number(productId),
      }
    }

    if (type) {
      whereClause = {
        ...whereClause,
        type,
      }
    }

    // 获取总记录数
    const total = await prisma.inventoryTransaction.count({
      where: whereClause,
    })

    // 获取分页数据
    const transactions = await prisma.inventoryTransaction.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit,
    })

    return {
      total,
      offset,
      limit,
      data: transactions,
    }
  } catch (error) {
    console.error("Error fetching inventory transactions:", error)
    throw new Error("Failed to fetch inventory transactions")
  }
}

// 客户相关操作
export async function getCustomers(type?: string, query?: string) {
  try {
    let whereClause = {}

    if (type) {
      whereClause = {
        ...whereClause,
        type,
      }
    }

    if (query) {
      whereClause = {
        ...whereClause,
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { phone: { contains: query, mode: "insensitive" } },
          { email: { contains: query, mode: "insensitive" } },
        ],
      }
    }

    return await prisma.customer.findMany({
      where: whereClause,
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching customers:", error)
    throw new Error("Failed to fetch customers")
  }
}

export async function createCustomer(data: any) {
  try {
    // 验证必填字段
    if (!data.name) {
      throw new AppError("客户名称为必填项", 400)
    }

    // 检查是否已存在相同手机号的客户
    if (data.phone) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          phone: data.phone,
        },
      })

      if (existingCustomer) {
        throw new AppError("已存在相同手机号的客户", 400)
      }
    }

    const customer = await prisma.customer.create({
      data: {
        name: data.name,
        phone: data.phone || null,
        email: data.email || null,
        address: data.address || null,
        type: data.type || "individual",
        notes: data.notes || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    })

    revalidatePath("/sales")
    return customer
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error creating customer:", error)
    throw new Error("Failed to create customer")
  }
}

export async function updateCustomer(id: number, data: any) {
  try {
    // 检查是否已存在相同手机号的其他客户
    if (data.phone) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          phone: data.phone,
          NOT: {
            id: Number(id),
          },
        },
      })

      if (existingCustomer) {
        throw new AppError("已存在相同手机号的客户", 400)
      }
    }

    const customer = await prisma.customer.update({
      where: { id },
      data: {
        name: data.name,
        phone: data.phone,
        email: data.email,
        address: data.address,
        type: data.type,
        notes: data.notes,
        isActive: data.isActive,
      },
    })

    revalidatePath("/sales")
    return customer
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error updating customer:", error)
    throw new Error("Failed to update customer")
  }
}

export async function deleteCustomer(id: number) {
  try {
    // 检查客户是否有关联订单
    const orderCount = await prisma.order.count({
      where: { customerId: id },
    })

    if (orderCount > 0) {
      throw new AppError("客户有关联订单，无法删除", 400)
    }

    await prisma.customer.delete({
      where: { id },
    })

    revalidatePath("/sales")
    return { success: true }
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error deleting customer:", error)
    throw new Error("Failed to delete customer")
  }
}

// 订单相关操作
export async function getOrders(
  status?: string,
  customerId?: number,
  employeeId?: number,
  startDate?: string,
  endDate?: string,
  limit: number = 50,
  offset: number = 0,
  isCustom?: boolean
) {
  try {
    let whereClause = {}

    if (status) {
      whereClause = {
        ...whereClause,
        status,
      }
    }

    if (customerId) {
      whereClause = {
        ...whereClause,
        customerId: Number(customerId),
      }
    }

    if (employeeId) {
      whereClause = {
        ...whereClause,
        employeeId: Number(employeeId),
      }
    }

    if (startDate && endDate) {
      whereClause = {
        ...whereClause,
        orderDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      }
    }

    if (isCustom !== undefined) {
      whereClause = {
        ...whereClause,
        isCustom,
      }
    }

    // 获取总记录数
    const total = await prisma.order.count({
      where: whereClause,
    })

    // 获取分页数据
    const orders = await prisma.order.findMany({
      where: whereClause,
      include: {
        customer: true,
        employee: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        orderDate: "desc",
      },
      skip: offset,
      take: limit,
    })

    return {
      total,
      offset,
      limit,
      data: orders,
    }
  } catch (error) {
    console.error("Error fetching orders:", error)
    throw new Error("Failed to fetch orders")
  }
}

export async function getOrder(id: number) {
  try {
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        customer: true,
        employee: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!order) {
      throw new AppError("订单不存在", 404)
    }

    return order
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error fetching order:", error)
    throw new Error("Failed to fetch order")
  }
}

// 生成订单编号
function generateOrderNumber() {
  const date = new Date()
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0")
  return `LH${year}${month}${day}${random}`
}

export async function createOrder(data: any) {
  try {
    // 验证必填字段
    if (!data.customerId || !data.employeeId || !data.items || data.items.length === 0) {
      throw new AppError("客户、员工和订单项为必填项", 400)
    }

    // 生成订单编号
    const orderNumber = generateOrderNumber()

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建订单
      const order = await tx.order.create({
        data: {
          orderNumber,
          customerId: Number(data.customerId),
          employeeId: Number(data.employeeId),
          orderDate: data.orderDate ? new Date(data.orderDate) : new Date(),
          status: data.status || "pending",
          totalAmount: Number(data.totalAmount),
          paidAmount: Number(data.paidAmount || 0),
          paymentStatus: data.paymentStatus || "unpaid",
          paymentMethod: data.paymentMethod,
          notes: data.notes,
          // 定制订单相关字段
          isCustom: data.isCustom || false,
          customDesign: data.customDesign,
          customRequirements: data.customRequirements,
          designImageUrl: data.designImageUrl,
          expectedDeliveryDate: data.expectedDeliveryDate ? new Date(data.expectedDeliveryDate) : null,
          designApproved: data.designApproved,
          designerNotes: data.designerNotes,
        },
      })

      // 创建订单项
      for (const item of data.items) {
        await tx.orderItem.create({
          data: {
            orderId: order.id,
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            price: Number(item.price),
            discount: Number(item.discount || 0),
            notes: item.notes,
          },
        })

        // 如果订单状态为已完成，则减少库存
        if (order.status === "completed" && data.warehouseId) {
          // 查找库存
          const inventory = await tx.inventoryItem.findFirst({
            where: {
              warehouseId: Number(data.warehouseId),
              productId: Number(item.productId),
            },
          })

          if (inventory) {
            // 检查库存是否足够
            if (inventory.quantity < Number(item.quantity)) {
              throw new Error(`产品 ${item.productId} 库存不足`)
            }

            // 减少库存
            await tx.inventoryItem.update({
              where: { id: inventory.id },
              data: {
                quantity: inventory.quantity - Number(item.quantity),
              },
            })

            // 记录库存交易
            await tx.inventoryTransaction.create({
              data: {
                type: "out",
                sourceWarehouseId: Number(data.warehouseId),
                productId: Number(item.productId),
                quantity: Number(item.quantity),
                notes: `订单出库: ${orderNumber}`,
                referenceId: order.id,
                referenceType: "order",
              },
            })
          }
        }
      }

      return order
    })

    revalidatePath("/sales")
    return result
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error creating order:", error)
    throw new Error(error.message || "Failed to create order")
  }
}

export async function updateOrder(id: number, data: any) {
  try {
    // 获取原订单信息
    const originalOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        items: true,
      },
    })

    if (!originalOrder) {
      throw new AppError("订单不存在", 404)
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 更新订单
      const order = await tx.order.update({
        where: { id },
        data: {
          customerId: data.customerId ? Number(data.customerId) : undefined,
          employeeId: data.employeeId ? Number(data.employeeId) : undefined,
          orderDate: data.orderDate ? new Date(data.orderDate) : undefined,
          status: data.status,
          totalAmount: data.totalAmount ? Number(data.totalAmount) : undefined,
          paidAmount: data.paidAmount ? Number(data.paidAmount) : undefined,
          paymentStatus: data.paymentStatus,
          paymentMethod: data.paymentMethod,
          notes: data.notes,
          // 定制订单相关字段
          isCustom: data.isCustom !== undefined ? data.isCustom : undefined,
          customDesign: data.customDesign !== undefined ? data.customDesign : undefined,
          customRequirements: data.customRequirements !== undefined ? data.customRequirements : undefined,
          designImageUrl: data.designImageUrl !== undefined ? data.designImageUrl : undefined,
          expectedDeliveryDate: data.expectedDeliveryDate ? new Date(data.expectedDeliveryDate) : undefined,
          designApproved: data.designApproved !== undefined ? data.designApproved : undefined,
          designerNotes: data.designerNotes !== undefined ? data.designerNotes : undefined,
        },
      })

      // 如果状态从非完成变为完成，且提供了仓库ID，则减少库存
      if (originalOrder.status !== "completed" && order.status === "completed" && data.warehouseId) {
        for (const item of originalOrder.items) {
          // 查找库存
          const inventory = await tx.inventoryItem.findFirst({
            where: {
              warehouseId: Number(data.warehouseId),
              productId: item.productId,
            },
          })

          if (inventory) {
            // 检查库存是否足够
            if (inventory.quantity < item.quantity) {
              throw new Error(`产品 ${item.productId} 库存不足`)
            }

            // 减少库存
            await tx.inventoryItem.update({
              where: { id: inventory.id },
              data: {
                quantity: inventory.quantity - item.quantity,
              },
            })

            // 记录库存交易
            await tx.inventoryTransaction.create({
              data: {
                type: "out",
                sourceWarehouseId: Number(data.warehouseId),
                productId: item.productId,
                quantity: item.quantity,
                notes: `订单出库: ${originalOrder.orderNumber}`,
                referenceId: order.id,
                referenceType: "order",
              },
            })
          }
        }
      }

      return order
    })

    revalidatePath("/sales")
    return result
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error updating order:", error)
    throw new Error(error.message || "Failed to update order")
  }
}

// POS销售
export async function createPosSale(data: any) {
  try {
    // 验证必填字段
    if (!data.employeeId || !data.items || data.items.length === 0 || !data.warehouseId) {
      throw new AppError("员工、商品和仓库为必填项", 400)
    }

    // 生成订单编号
    const orderNumber = `POS${new Date().getTime()}`

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建或获取客户
      let customerId = data.customerId

      if (!customerId && data.customerInfo) {
        // 如果提供了客户信息但没有客户ID，则创建新客户或查找现有客户
        if (data.customerInfo.phone) {
          // 查找是否有相同手机号的客户
          const existingCustomer = await tx.customer.findFirst({
            where: {
              phone: data.customerInfo.phone,
            },
          })

          if (existingCustomer) {
            customerId = existingCustomer.id
          } else {
            // 创建新客户
            const newCustomer = await tx.customer.create({
              data: {
                name: data.customerInfo.name || "散客",
                phone: data.customerInfo.phone,
                email: data.customerInfo.email || null,
                address: data.customerInfo.address || null,
                type: "individual",
              },
            })
            customerId = newCustomer.id
          }
        } else {
          // 如果没有提供手机号，创建匿名客户
          const anonymousCustomer = await tx.customer.create({
            data: {
              name: data.customerInfo.name || "散客",
              type: "individual",
            },
          })
          customerId = anonymousCustomer.id
        }
      } else if (!customerId) {
        // 如果没有提供客户ID和客户信息，创建匿名客户
        const anonymousCustomer = await tx.customer.create({
          data: {
            name: "散客",
            type: "individual",
          },
        })
        customerId = anonymousCustomer.id
      }

      // 计算总金额
      const totalAmount = data.items.reduce((sum, item) => {
        return sum + (Number(item.price) * Number(item.quantity) - Number(item.discount || 0))
      }, 0)

      // 创建订单
      const order = await tx.order.create({
        data: {
          orderNumber,
          customerId: Number(customerId),
          employeeId: Number(data.employeeId),
          orderDate: new Date(),
          status: "completed", // POS销售直接完成
          totalAmount,
          paidAmount: totalAmount, // POS销售直接支付全额
          paymentStatus: "paid", // POS销售直接标记为已支付
          paymentMethod: data.paymentMethod || "cash",
          notes: data.notes,
        },
      })

      // 创建订单项并减少库存
      for (const item of data.items) {
        // 创建订单项
        await tx.orderItem.create({
          data: {
            orderId: order.id,
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            price: Number(item.price),
            discount: Number(item.discount || 0),
            notes: item.notes,
          },
        })

        // 查找库存
        const inventory = await tx.inventoryItem.findFirst({
          where: {
            warehouseId: Number(data.warehouseId),
            productId: Number(item.productId),
          },
        })

        if (inventory) {
          // 检查库存是否足够
          if (inventory.quantity < Number(item.quantity)) {
            throw new Error(`产品 ${item.productId} 库存不足`)
          }

          // 减少库存
          await tx.inventoryItem.update({
            where: { id: inventory.id },
            data: {
              quantity: inventory.quantity - Number(item.quantity),
            },
          })

          // 记录库存交易
          await tx.inventoryTransaction.create({
            data: {
              type: "out",
              sourceWarehouseId: Number(data.warehouseId),
              productId: Number(item.productId),
              quantity: Number(item.quantity),
              notes: `POS销售: ${orderNumber}`,
              referenceId: order.id,
              referenceType: "pos",
            },
          })
        } else {
          throw new Error(`产品 ${item.productId} 在仓库 ${data.warehouseId} 中没有库存记录`)
        }
      }

      // 创建销售记录（兼容现有系统）
      const gallerySale = await tx.gallerySale.create({
        data: {
          employeeId: Number(data.employeeId),
          date: new Date(),
          totalAmount,
          notes: `POS销售: ${orderNumber}`,
        },
      })

      // 创建销售项目明细
      for (const item of data.items) {
        await tx.salesItem.create({
          data: {
            gallerySaleId: gallerySale.id,
            productId: Number(item.productId),
            quantity: Number(item.quantity),
            price: Number(item.price),
          },
        })
      }

      return {
        order,
        gallerySale,
      }
    })

    revalidatePath("/sales")
    revalidatePath("/inventory")
    revalidatePath("/daily-log")
    return result
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    console.error("Error creating POS sale:", error)
    throw new Error(error.message || "Failed to create POS sale")
  }
}

// 渠道相关操作
export async function getChannels() {
  try {
    return await prisma.channel.findMany({
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching channels:", error)
    throw new Error("Failed to fetch channels")
  }
}

export async function createChannel(data: any) {
  try {
    const channel = await prisma.channel.create({
      data: {
        name: data.name,
        code: data.code,
        description: data.description,
        contactName: data.contactName,
        contactPhone: data.contactPhone,
        contactEmail: data.contactEmail,
        address: data.address,
        isActive: data.isActive,
      },
    })

    revalidatePath("/channels")
    return channel
  } catch (error) {
    console.error("Error creating channel:", error)
    throw new Error("Failed to create channel")
  }
}

export async function updateChannel(id: number, data: any) {
  try {
    const channel = await prisma.channel.update({
      where: { id },
      data: {
        name: data.name,
        code: data.code,
        description: data.description,
        contactName: data.contactName,
        contactPhone: data.contactPhone,
        contactEmail: data.contactEmail,
        address: data.address,
        isActive: data.isActive,
      },
    })

    revalidatePath("/channels")
    return channel
  } catch (error) {
    console.error("Error updating channel:", error)
    throw new Error("Failed to update channel")
  }
}

export async function deleteChannel(id: number) {
  try {
    await prisma.channel.delete({
      where: { id },
    })

    revalidatePath("/channels")
    return { success: true }
  } catch (error) {
    console.error("Error deleting channel:", error)
    throw new Error("Failed to delete channel")
  }
}

export async function getChannelPrices(channelId?: number, productId?: number) {
  try {
    let whereClause: any = {}

    if (channelId) {
      whereClause.channelId = channelId
    }

    if (productId) {
      whereClause.productId = productId
    }

    return await prisma.channelPrice.findMany({
      where: whereClause,
      include: {
        channel: true,
        product: true,
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching channel prices:", error)
    throw new Error("Failed to fetch channel prices")
  }
}

export async function createChannelPrice(data: any) {
  try {
    // 检查是否已存在相同渠道和产品的价格
    const existingPrice = await prisma.channelPrice.findFirst({
      where: {
        channelId: Number.parseInt(data.channelId),
        productId: Number.parseInt(data.productId),
      },
    })

    if (existingPrice) {
      throw new Error("已存在相同渠道和产品的价格设置")
    }

    const channelPrice = await prisma.channelPrice.create({
      data: {
        channelId: Number.parseInt(data.channelId),
        productId: Number.parseInt(data.productId),
        price: Number.parseFloat(data.price),
        isActive: data.isActive,
      },
      include: {
        channel: true,
        product: true,
      },
    })

    revalidatePath("/channels")
    return channelPrice
  } catch (error) {
    console.error("Error creating channel price:", error)
    if (error.message === "已存在相同渠道和产品的价格设置") {
      throw new Error("已存在相同渠道和产品的价格设置")
    }
    throw new Error("Failed to create channel price")
  }
}

export async function updateChannelPrice(id: number, data: any) {
  try {
    const channelPrice = await prisma.channelPrice.update({
      where: { id },
      data: {
        price: Number.parseFloat(data.price),
        isActive: data.isActive,
      },
      include: {
        channel: true,
        product: true,
      },
    })

    revalidatePath("/channels")
    return channelPrice
  } catch (error) {
    console.error("Error updating channel price:", error)
    throw new Error("Failed to update channel price")
  }
}

export async function deleteChannelPrice(id: number) {
  try {
    await prisma.channelPrice.delete({
      where: { id },
    })

    revalidatePath("/channels")
    return { success: true }
  } catch (error) {
    console.error("Error deleting channel price:", error)
    throw new Error("Failed to delete channel price")
  }
}

// 渠道库存相关操作
export async function getChannelInventory(channelId?: number, productId?: number) {
  try {
    let whereClause: any = {}

    if (channelId) {
      whereClause.channelId = channelId
    }

    if (productId) {
      whereClause.productId = productId
    }

    return await prisma.channelInventory.findMany({
      where: whereClause,
      include: {
        channel: true,
        product: true,
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching channel inventory:", error)
    throw new Error("Failed to fetch channel inventory")
  }
}

export async function createChannelInventory(data: any) {
  try {
    // 检查是否已存在相同渠道和产品的库存
    const existingInventory = await prisma.channelInventory.findFirst({
      where: {
        channelId: Number.parseInt(data.channelId),
        productId: Number.parseInt(data.productId),
      },
    })

    if (existingInventory) {
      throw new Error("已存在相同渠道和产品的库存记录")
    }

    const channelInventory = await prisma.channelInventory.create({
      data: {
        channelId: Number.parseInt(data.channelId),
        productId: Number.parseInt(data.productId),
        quantity: Number.parseInt(data.quantity),
        minQuantity: data.minQuantity ? Number.parseInt(data.minQuantity) : null,
        notes: data.notes || null,
      },
      include: {
        channel: true,
        product: true,
      },
    })

    revalidatePath("/channels")
    return channelInventory
  } catch (error) {
    console.error("Error creating channel inventory:", error)
    if (error.message === "已存在相同渠道和产品的库存记录") {
      throw new Error("已存在相同渠道和产品的库存记录")
    }
    throw new Error("Failed to create channel inventory")
  }
}

export async function updateChannelInventory(id: number, data: any) {
  try {
    const channelInventory = await prisma.channelInventory.update({
      where: { id },
      data: {
        quantity: Number.parseInt(data.quantity),
        minQuantity: data.minQuantity ? Number.parseInt(data.minQuantity) : null,
        notes: data.notes || null,
      },
      include: {
        channel: true,
        product: true,
      },
    })

    revalidatePath("/channels")
    return channelInventory
  } catch (error) {
    console.error("Error updating channel inventory:", error)
    throw new Error("Failed to update channel inventory")
  }
}

export async function deleteChannelInventory(id: number) {
  try {
    await prisma.channelInventory.delete({
      where: { id },
    })

    revalidatePath("/channels")
    return { success: true }
  } catch (error) {
    console.error("Error deleting channel inventory:", error)
    throw new Error("Failed to delete channel inventory")
  }
}

// 渠道销售统计相关操作
export async function getChannelSalesStats(startDate?: string, endDate?: string, channelId?: number) {
  try {
    // 构建查询条件
    let whereClause: any = {}

    if (startDate && endDate) {
      whereClause.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    }

    // 获取所有渠道
    const channels = await prisma.channel.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        name: "asc",
      },
    })

    // 获取所有销售记录
    const sales = await prisma.gallerySale.findMany({
      where: whereClause,
      include: {
        employee: true,
        salesItems: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    })

    // 按渠道统计销售数据
    const channelStats = []

    // 为每个渠道创建统计对象
    channels.forEach(channel => {
      channelStats.push({
        channel,
        totalSales: 0,
        totalItems: 0,
        products: [],
        monthlySales: [],
      })
    })

    // 处理销售数据
    sales.forEach(sale => {
      sale.salesItems.forEach(item => {
        // 查找产品的渠道价格
        const channelPrice = channels.map(async channel => {
          return await prisma.channelPrice.findFirst({
            where: {
              channelId: channel.id,
              productId: item.productId,
              isActive: true,
            },
          })
        })

        // 如果产品有渠道价格，则将销售计入对应渠道
        channelPrice.forEach(async (pricePromise, index) => {
          const price = await pricePromise
          if (price) {
            const channelStat = channelStats[index]

            // 更新总销售额和总销售数量
            channelStat.totalSales += item.price * item.quantity
            channelStat.totalItems += item.quantity

            // 更新产品销售统计
            const productIndex = channelStat.products.findIndex(p => p.id === item.productId)
            if (productIndex === -1) {
              channelStat.products.push({
                id: item.productId,
                name: item.product.name,
                quantity: item.quantity,
                amount: item.price * item.quantity,
              })
            } else {
              channelStat.products[productIndex].quantity += item.quantity
              channelStat.products[productIndex].amount += item.price * item.quantity
            }

            // 更新月度销售统计
            const month = `${sale.date.getFullYear()}-${String(sale.date.getMonth() + 1).padStart(2, '0')}`
            const monthIndex = channelStat.monthlySales.findIndex(m => m.month === month)
            if (monthIndex === -1) {
              channelStat.monthlySales.push({
                month,
                amount: item.price * item.quantity,
              })
            } else {
              channelStat.monthlySales[monthIndex].amount += item.price * item.quantity
            }
          }
        })
      })
    })

    // 如果指定了渠道ID，则只返回该渠道的统计数据
    if (channelId) {
      const channelStat = channelStats.find(stat => stat.channel.id === channelId)
      return channelStat || null
    }

    return channelStats
  } catch (error) {
    console.error("Error fetching channel sales stats:", error)
    throw new Error("Failed to fetch channel sales stats")
  }
}

// 薪酬计算相关操作
export async function calculatePayroll(year: number, month: number) {
  try {
    // 计算月份的开始和结束日期
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0)

    // 获取所有员工
    const employees = await prisma.employee.findMany({
      where: {
        status: "active",
      },
    })

    // 获取系统设置
    const settings = await prisma.systemSetting.findFirst()

    // 计算每个员工的薪酬
    const payrollData = await Promise.all(
      employees.map(async (employee) => {
        // 1. 计算基本工资（日薪 * 出勤天数）
        const schedules = await prisma.schedule.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })
        const workDays = schedules.length
        const baseSalary = employee.dailySalary * workDays

        // 2. 计算手作团建费
        const workshops = await prisma.workshop.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })

        let workshopFee = 0
        workshops.forEach((workshop) => {
          if (workshop.role === "teacher") {
            workshopFee += settings?.teacherWorkshopFee || 200
          } else {
            workshopFee += settings?.assistantWorkshopFee || 130
          }
        })

        // 3. 计算咖啡店提成
        const coffeeShifts = await prisma.coffeeShopShift.findMany({
          where: {
            employeeId: employee.id,
            coffeeShopSale: {
              date: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          include: {
            coffeeShopSale: true,
          },
        })

        let coffeeSalesCommission = 0
        for (const shift of coffeeShifts) {
          // 获取当天所有值班员工数量
          const totalShifts = await prisma.coffeeShopShift.count({
            where: {
              coffeeShopSaleId: shift.coffeeShopSaleId,
            },
          })

          // 计算提成：总销售额 * 提成比例 / 值班员工数
          const commissionRate = settings?.coffeeSalesCommissionRate || 20
          const commission = (shift.coffeeShopSale.totalSales * (commissionRate / 100)) / totalShifts
          coffeeSalesCommission += commission
        }

        // 4. 计算珐琅馆销售提成
        const gallerySales = await prisma.gallerySale.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
        })

        const commissionRate = settings?.gallerySalesCommissionRate || 10
        const gallerySalesCommission = gallerySales.reduce(
          (sum, sale) => sum + sale.totalAmount * (commissionRate / 100),
          0,
        )

        // 5. 计算计件工费
        const pieceWorks = await prisma.pieceWork.findMany({
          where: {
            employeeId: employee.id,
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
          include: {
            details: true,
          },
        })

        // 分别计算配饰和点蓝工费
        let accessoryFee = 0
        let enamellingFee = 0

        pieceWorks.forEach((pieceWork) => {
          if (pieceWork.workType === "accessory") {
            accessoryFee += pieceWork.totalAmount
          } else if (pieceWork.workType === "enamelling") {
            enamellingFee += pieceWork.totalAmount
          }
        })

        // 6. 计算总薪酬
        const totalSalary =
          baseSalary + workshopFee + coffeeSalesCommission + gallerySalesCommission + accessoryFee + enamellingFee

        return {
          id: employee.id,
          employee: employee.name,
          position: employee.position,
          dailySalary: employee.dailySalary,
          workDays,
          baseSalary,
          workshopFee,
          coffeeSalesCommission,
          gallerySalesCommission,
          accessoryFee,
          enamellingFee,
          totalSalary,
        }
      }),
    )

    return payrollData
  } catch (error) {
    console.error("Error calculating payroll:", error)
    throw new Error("Failed to calculate payroll")
  }
}

// 系统日志相关操作
export async function getLogs(module?: string, level?: string, startDate?: string, endDate?: string, limit: number = 100) {
  try {
    console.log("Server Action: Fetching system logs with filters:", { module, level, startDate, endDate, limit })

    let whereClause: any = {}

    if (module) {
      whereClause.module = module
    }

    if (level) {
      whereClause.level = level
    }

    if (startDate && endDate) {
      whereClause.timestamp = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    }

    // 查询系统日志
    const logs = await prisma.systemLog.findMany({
      where: whereClause,
      orderBy: {
        timestamp: "desc",
      },
      take: limit,
    })

    console.log(`Server Action: Found ${logs.length} system logs`)

    // 如果没有日志记录，创建一条测试日志
    if (logs.length === 0) {
      console.log("Server Action: No logs found, creating a test log")
      await createLog({
        module: "系统",
        level: "info",
        message: "系统日志功能已启用",
        details: "这是一条测试日志记录，表明系统日志功能正常工作"
      })

      // 重新获取日志
      return await prisma.systemLog.findMany({
        orderBy: {
          timestamp: "desc",
        },
        take: limit,
      })
    }

    return logs
  } catch (error) {
    console.error("Error fetching system logs:", error)
    throw new Error("Failed to fetch system logs")
  }
}

export async function createLog(data: any) {
  try {
    console.log("Server Action: Creating system log:", data)

    // 验证必填字段
    if (!data.module || !data.message) {
      throw new Error("模块和消息为必填项")
    }

    // 规范化日志级别
    const validLevels = ["info", "warning", "error", "debug"]
    const level = validLevels.includes(data.level) ? data.level : "info"

    // 创建日志记录
    const log = await prisma.systemLog.create({
      data: {
        module: data.module,
        level: level,
        message: data.message,
        details: data.details || null,
        userId: data.userId || null,
        timestamp: data.timestamp || new Date(),
      },
    })

    console.log("Server Action: System log created:", log.id)

    return log
  } catch (error) {
    console.error("Error creating system log:", error)
    throw new Error("Failed to create system log")
  }
}

// 仪表盘数据相关操作
export async function getDashboardData(timeRange: string = "month") {
  try {
    // 获取日期范围
    const now = new Date()
    let startDate: Date
    let endDate: Date = now

    switch (timeRange) {
      case "week":
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)
        break
      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        break
      case "quarter":
        startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1)
        break
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1)
        break
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
    }

    // 获取销售数据
    const gallerySales = await prisma.gallerySale.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        employee: true,
        salesItems: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    })

    // 获取咖啡店销售数据
    const coffeeSales = await prisma.coffeeShopSale.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        shifts: {
          include: {
            employee: true,
          },
        },
        items: true,
      },
      orderBy: {
        date: "desc",
      },
    })

    // 获取手作团建数据
    const workshops = await prisma.workshop.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        teacher: true,
      },
      orderBy: {
        date: "desc",
      },
    })

    // 计算销售总额
    const gallerySalesTotal = gallerySales.reduce((sum, sale) => sum + sale.totalAmount, 0)
    const coffeeSalesTotal = coffeeSales.reduce((sum, sale) => sum + sale.totalSales, 0)
    const totalSales = gallerySalesTotal + coffeeSalesTotal

    // 计算销售数量
    const gallerySalesCount = gallerySales.length
    const coffeeSalesCount = coffeeSales.length
    const workshopsCount = workshops.length

    // 计算客户数量
    const customerCount = await prisma.customer.count({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    })

    // 计算热销产品
    const productSales = {}

    // 防御性编程：确保 gallerySales 存在且是数组
    if (gallerySales && Array.isArray(gallerySales)) {
      gallerySales.forEach(sale => {
        // 防御性编程：确保 sale.salesItems 存在且是数组
        if (sale.salesItems && Array.isArray(sale.salesItems)) {
          sale.salesItems.forEach(item => {
            // 防御性编程：确保 item.product 存在
            if (item && item.product) {
              const productId = item.productId
              if (!productSales[productId]) {
                productSales[productId] = {
                  id: productId,
                  name: item.product.name,
                  quantity: 0,
                  amount: 0,
                }
              }
              productSales[productId].quantity += item.quantity
              productSales[productId].amount += item.price * item.quantity
            }
          })
        }
      })
    }

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5)

    // 计算咖啡店热销品类
    const coffeeItemSales = {}

    // 防御性编程：确保 coffeeSales 存在且是数组
    if (coffeeSales && Array.isArray(coffeeSales)) {
      coffeeSales.forEach(sale => {
        // 防御性编程：确保 sale.items 存在且是数组
        if (sale.items && Array.isArray(sale.items)) {
          sale.items.forEach(item => {
            const category = item.category || "其他"
            if (!coffeeItemSales[category]) {
              coffeeItemSales[category] = {
                category,
                quantity: 0,
                amount: 0,
              }
            }
            coffeeItemSales[category].quantity += item.quantity
            coffeeItemSales[category].amount += item.totalPrice
          })
        }
      })
    }

    const topCoffeeCategories = Object.values(coffeeItemSales)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5)

    // 计算销售趋势
    const salesTrend = {}

    // 根据时间范围确定趋势数据的粒度
    let dateFormat = "%Y-%m-%d" // 默认按天
    if (timeRange === "year") {
      dateFormat = "%Y-%m" // 按月
    } else if (timeRange === "quarter") {
      dateFormat = "%Y-%m-%d" // 按周
      // 按周分组需要在应用层处理
    }

    // 返回仪表盘数据
    return {
      timeRange,
      period: {
        start: startDate,
        end: endDate,
      },
      sales: {
        total: totalSales,
        gallery: gallerySalesTotal,
        coffee: coffeeSalesTotal,
      },
      counts: {
        gallerySales: gallerySalesCount,
        coffeeSales: coffeeSalesCount,
        workshops: workshopsCount,
        customers: customerCount,
      },
      topProducts,
      topCoffeeCategories,
      recentSales: gallerySales && Array.isArray(gallerySales) ? gallerySales.slice(0, 5) : [],
      recentCoffeeSales: coffeeSales && Array.isArray(coffeeSales) ? coffeeSales.slice(0, 5) : [],
      recentWorkshops: workshops && Array.isArray(workshops) ? workshops.slice(0, 5) : [],
    }
  } catch (error) {
    console.error("Error fetching dashboard data:", error)
    throw new Error("Failed to fetch dashboard data")
  }
}

// 咖啡店销售相关操作
export async function getCoffeeShopSales(startDate?: string, endDate?: string, employeeId?: string) {
  try {
    console.log(`Fetching coffee shop sales with params: startDate=${startDate}, endDate=${endDate}, employeeId=${employeeId}`);

    // 构建查询条件
    let whereClause: any = {}

    if (startDate && endDate) {
      // 确保日期范围正确处理
      const start = new Date(startDate);
      const end = new Date(endDate);

      // 设置开始日期为当天的开始时间
      start.setHours(0, 0, 0, 0);

      // 设置结束日期为当天的结束时间
      end.setHours(23, 59, 59, 999);

      whereClause.date = {
        gte: start,
        lte: end,
      }

      console.log(`Date range: ${start.toISOString()} to ${end.toISOString()}`);
    }

    // 如果指定了员工ID，使用更高效的查询方式
    if (employeeId && employeeId !== "all") {
      // 使用连接查询而不是嵌套查询
      const employeeIdNum = Number.parseInt(employeeId);
      console.log(`Filtering by employee ID: ${employeeIdNum}`);

      // 先获取该员工参与的所有销售记录ID
      const shifts = await prisma.coffeeShopShift.findMany({
        where: {
          employeeId: employeeIdNum
        },
        select: {
          coffeeShopSaleId: true
        }
      });

      const saleIds = shifts.map(shift => shift.coffeeShopSaleId);
      console.log(`Found ${saleIds.length} sales for employee ${employeeIdNum}`);

      // 如果找到了销售记录，添加到查询条件中
      if (saleIds.length > 0) {
        whereClause.id = {
          in: saleIds
        };
      } else {
        // 如果没有找到销售记录，返回空数组
        console.log("No sales found for this employee");
        return [];
      }
    }

    // 获取咖啡店销售记录
    const sales = await prisma.coffeeShopSale.findMany({
      where: whereClause,
      include: {
        shifts: {
          include: {
            employee: true,
          },
        },
        items: true,
      },
      orderBy: {
        date: "desc",
      },
    });

    console.log(`Found ${sales.length} coffee shop sales`);

    // 返回销售记录
    return sales;
  } catch (error) {
    console.error("Error fetching coffee shop sales:", error)
    throw new Error("Failed to fetch coffee shop sales")
  }
}

export async function getCoffeeShopItems(startDate?: string, endDate?: string, category?: string) {
  try {
    // 构建查询条件
    let whereClause: any = {}

    if (startDate && endDate) {
      whereClause = {
        coffeeShopSale: {
          date: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
      }
    }

    if (category && category !== "all") {
      whereClause.category = category
    }

    // 获取咖啡店商品销售记录
    const items = await prisma.coffeeShopItem.findMany({
      where: whereClause,
      include: {
        coffeeShopSale: {
          select: {
            date: true,
          },
        },
      },
      orderBy: [
        {
          category: "asc",
        },
        {
          name: "asc",
        },
      ],
    })

    return items
  } catch (error) {
    console.error("Error fetching coffee shop items:", error)
    throw new Error("Failed to fetch coffee shop items")
  }
}

// 获取单个咖啡店销售记录
export async function getCoffeeShopSale(id: number) {
  try {
    const sale = await prisma.coffeeShopSale.findUnique({
      where: { id },
      include: {
        shifts: {
          include: {
            employee: true,
          },
        },
        items: true,
      },
    })

    if (!sale) {
      throw new Error("咖啡店销售记录不存在")
    }

    return sale
  } catch (error) {
    console.error("Error fetching coffee shop sale:", error)
    throw new Error("Failed to fetch coffee shop sale")
  }
}

// 更新咖啡店销售记录
export async function updateCoffeeShopSale(id: number, data: any) {
  try {
    // 验证数据
    if (!data.date || !data.totalSales) {
      throw new Error("日期和总销售额为必填项")
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 更新咖啡店销售记录
      const coffeeShopSale = await tx.coffeeShopSale.update({
        where: { id },
        data: {
          date: new Date(data.date),
          totalSales: Number.parseFloat(data.totalSales),
          cashAmount: Number.parseFloat(data.cashAmount || 0),
          cardAmount: Number.parseFloat(data.cardAmount || 0),
          wechatAmount: Number.parseFloat(data.wechatAmount || 0),
          alipayAmount: Number.parseFloat(data.alipayAmount || 0),
          otherAmount: Number.parseFloat(data.otherAmount || 0),
          customerCount: Number.parseInt(data.customerCount || 0),
          notes: data.notes,
        },
      })

      // 如果提供了员工数据，更新值班员工记录
      if (data.staffOnDuty && Array.isArray(data.staffOnDuty)) {
        // 删除原有值班员工记录
        await tx.coffeeShopShift.deleteMany({
          where: { coffeeShopSaleId: id },
        })

        // 创建新的值班员工记录
        for (const employeeId of data.staffOnDuty) {
          await tx.coffeeShopShift.create({
            data: {
              coffeeShopSaleId: coffeeShopSale.id,
              employeeId: Number.parseInt(employeeId),
            },
          })
        }
      }

      // 如果提供了商品数据，更新商品记录
      if (data.items && Array.isArray(data.items)) {
        // 删除原有商品记录
        await tx.coffeeShopItem.deleteMany({
          where: { coffeeShopSaleId: id },
        })

        // 创建新的商品记录
        for (const item of data.items) {
          await tx.coffeeShopItem.create({
            data: {
              coffeeShopSaleId: coffeeShopSale.id,
              name: item.name,
              category: item.category,
              quantity: Number.parseInt(item.quantity),
              unitPrice: Number.parseFloat(item.unitPrice),
              totalPrice: Number.parseFloat(item.totalPrice),
            },
          })
        }
      }

      return coffeeShopSale
    })

    revalidatePath("/daily-log")
    revalidatePath("/coffee-reports")
    revalidatePath("/")
    return result
  } catch (error) {
    console.error("Error updating coffee shop sale:", error)
    throw new Error(error instanceof Error ? error.message : "Failed to update coffee shop sale")
  }
}

// 删除咖啡店销售记录
export async function deleteCoffeeShopSale(id: number) {
  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 删除关联的值班员工记录
      await tx.coffeeShopShift.deleteMany({
        where: { coffeeShopSaleId: id },
      })

      // 删除关联的商品记录
      await tx.coffeeShopItem.deleteMany({
        where: { coffeeShopSaleId: id },
      })

      // 删除咖啡店销售记录
      await tx.coffeeShopSale.delete({
        where: { id },
      })
    })

    revalidatePath("/daily-log")
    revalidatePath("/coffee-reports")
    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error deleting coffee shop sale:", error)
    throw new Error("Failed to delete coffee shop sale")
  }
}

// 导入咖啡店销售数据
export async function importCoffeeShopSales(data: any[]) {
  try {
    // 验证数据格式
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error("无效的数据格式")
    }

    // 使用事务确保数据一致性
    const results = await prisma.$transaction(async (tx) => {
      const createdSales = []

      for (const saleData of data) {
        // 验证必要字段
        if (!saleData.date || !saleData.totalSales) {
          throw new Error("销售数据缺少必要字段：日期或总销售额")
        }

        // 创建咖啡店销售记录
        const coffeeShopSale = await tx.coffeeShopSale.create({
          data: {
            date: new Date(saleData.date),
            totalSales: Number.parseFloat(saleData.totalSales),
            cashAmount: Number.parseFloat(saleData.cashAmount || 0),
            cardAmount: Number.parseFloat(saleData.cardAmount || 0),
            wechatAmount: Number.parseFloat(saleData.wechatAmount || 0),
            alipayAmount: Number.parseFloat(saleData.alipayAmount || 0),
            otherAmount: Number.parseFloat(saleData.otherAmount || 0),
            customerCount: Number.parseInt(saleData.customerCount || 0),
            notes: saleData.notes,
          },
        })

        // 创建值班员工记录
        if (saleData.staffOnDuty && Array.isArray(saleData.staffOnDuty)) {
          for (const employeeId of saleData.staffOnDuty) {
            await tx.coffeeShopShift.create({
              data: {
                coffeeShopSaleId: coffeeShopSale.id,
                employeeId: Number.parseInt(employeeId),
              },
            })
          }
        }

        // 创建销售项目记录
        if (saleData.items && Array.isArray(saleData.items) && saleData.items.length > 0) {
          for (const item of saleData.items) {
            await tx.coffeeShopItem.create({
              data: {
                coffeeShopSaleId: coffeeShopSale.id,
                name: item.name,
                category: item.category,
                quantity: Number.parseInt(item.quantity),
                unitPrice: Number.parseFloat(item.unitPrice),
                totalPrice: Number.parseFloat(item.totalPrice),
              },
            })
          }
        }

        createdSales.push(coffeeShopSale)
      }

      return createdSales
    })

    revalidatePath("/daily-log")
    revalidatePath("/coffee-reports")
    revalidatePath("/")

    return {
      success: true,
      count: results.length,
      message: `成功导入 ${results.length} 条咖啡店销售记录`
    }
  } catch (error) {
    console.error("Error importing coffee shop sales:", error)
    throw new Error(error instanceof Error ? error.message : "导入咖啡店销售数据失败")
  }
}

// 计件工作相关操作
export async function getPieceWorks(startDate?: string, endDate?: string, employeeId?: string) {
  try {
    // 构建查询条件
    let whereClause: any = {}

    if (startDate && endDate) {
      whereClause.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    }

    if (employeeId && employeeId !== "all") {
      whereClause.employeeId = Number.parseInt(employeeId)
    }

    // 获取计件工作记录
    const pieceWorks = await prisma.pieceWork.findMany({
      where: whereClause,
      include: {
        employee: true,
        details: {
          include: {
            pieceWorkItem: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    })

    return pieceWorks
  } catch (error) {
    console.error("Error fetching piece works:", error)
    throw new Error("Failed to fetch piece works")
  }
}

export async function updatePieceWork(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.employee || !data.date || !data.workType || !data.items || data.items.length === 0) {
      throw new Error("员工、日期、工作类型和工作项为必填项")
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      // 更新计件工作记录
      const pieceWork = await tx.pieceWork.update({
        where: { id },
        data: {
          employeeId: Number.parseInt(data.employee),
          date: new Date(data.date),
          workType: data.workType,
          totalAmount: Number.parseFloat(data.totalAmount),
          notes: data.notes,
        },
      })

      // 删除原有的明细记录
      await tx.pieceWorkDetail.deleteMany({
        where: { pieceWorkId: id },
      })

      // 创建新的明细记录
      for (const item of data.items) {
        await tx.pieceWorkDetail.create({
          data: {
            pieceWorkId: pieceWork.id,
            pieceWorkItemId: Number.parseInt(item.itemId),
            quantity: Number.parseInt(item.quantity),
            price: Number.parseFloat(item.price),
          },
        })
      }

      return pieceWork
    })

    revalidatePath("/production")
    return result
  } catch (error) {
    console.error("Error updating piece work:", error)
    throw new Error("Failed to update piece work")
  }
}

export async function deletePieceWork(id: number) {
  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 先删除明细记录
      await tx.pieceWorkDetail.deleteMany({
        where: { pieceWorkId: id },
      })

      // 再删除主记录
      await tx.pieceWork.delete({
        where: { id },
      })
    })

    revalidatePath("/production")
    return { success: true }
  } catch (error) {
    console.error("Error deleting piece work:", error)
    throw new Error("Failed to delete piece work")
  }
}

// 系统信息相关操作
export async function getSystemInfo() {
  try {
    // 获取系统基本信息
    const uptime = process.uptime()
    const nodeVersion = process.version
    const platform = process.platform

    // 获取内存信息（使用Node.js的os模块）
    const os = require('os')
    const totalMemory = os.totalmem()
    const freeMemory = os.freemem()
    const usedMemory = totalMemory - freeMemory

    // 获取数据库信息
    const databaseInfo = {
      type: "PostgreSQL",
      version: "Latest",
      size: 0,
      tables: 0,
      records: 0,
    }

    // 获取表数量
    const tables = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `
    databaseInfo.tables = Number(tables[0].count)

    // 获取记录总数
    const employeeCount = await prisma.employee.count()
    const productCount = await prisma.product.count()
    const customerCount = await prisma.customer.count()
    const orderCount = await prisma.order.count()
    const inventoryCount = await prisma.inventoryItem.count()

    databaseInfo.records = employeeCount + productCount + customerCount + orderCount + inventoryCount

    // 获取数据库大小（估计值）
    try {
      const sizeQuery = await prisma.$queryRaw`
        SELECT pg_database_size(current_database()) as size
      `
      databaseInfo.size = Number(sizeQuery[0].size)
    } catch (err) {
      console.error("Error getting database size:", err)
      // 如果无法获取实际大小，使用估计值
      databaseInfo.size = databaseInfo.records * 2048 // 估计每条记录2KB
    }

    // 获取数据库版本
    try {
      const versionQuery = await prisma.$queryRaw`SELECT version()`
      const versionString = String(versionQuery[0].version)
      const versionMatch = versionString.match(/PostgreSQL (\d+\.\d+)/)
      databaseInfo.version = versionMatch ? versionMatch[1] : "Latest"
    } catch (err) {
      console.error("Error getting database version:", err)
    }

    // 获取存储信息（估计值，因为无法在服务器端操作中直接执行shell命令）
    const storageInfo = {
      total: 1000 * 1024 * 1024 * 1024, // 1TB
      free: 500 * 1024 * 1024 * 1024,   // 500GB
      used: 500 * 1024 * 1024 * 1024    // 500GB
    }

    // 返回符合SystemInfo组件期望的数据结构
    return {
      version: "1.0.0", // 系统版本
      uptime,
      nodeVersion,
      platform,
      memory: {
        total: totalMemory,
        free: freeMemory,
        used: usedMemory
      },
      database: databaseInfo,
      storage: storageInfo,
      // 保留额外信息，以便将来可能的扩展
      stats: {
        employees: employeeCount,
        products: productCount,
        customers: customerCount,
        orders: orderCount,
        inventory: inventoryCount,
      }
    }
  } catch (error) {
    console.error("Error fetching system info:", error)
    throw new Error("Failed to fetch system info")
  }
}

// 用户相关操作
export async function getUsersBasic() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return users
  } catch (error) {
    console.error("Error fetching users:", error)
    throw new Error("Failed to fetch users")
  }
}

export async function createUserBasic(data: any) {
  try {
    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: {
        email: data.email,
      },
    })

    if (existingUser) {
      throw new Error("邮箱已被注册")
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        role: data.role || "user",
      },
    })

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  } catch (error) {
    console.error("Error creating user:", error)
    throw new Error(error.message || "Failed to create user")
  }
}

export async function updateUserDetailed(id: string, data: any) {
  try {
    console.log("Server Action: Updating user with data:", { id, ...data });

    // 检查邮箱是否已被其他用户使用
    if (data.email) {
      const existingUser = await prisma.user.findFirst({
        where: {
          email: data.email,
          NOT: {
            id,
          },
        },
      })

      if (existingUser) {
        throw new Error("邮箱已被其他用户使用")
      }
    }

    // 获取当前用户信息，包括角色
    const currentUser = await prisma.user.findUnique({
      where: { id },
      include: {
        userRoles: true,
        employee: true,
      }
    });

    if (!currentUser) {
      throw new Error("用户不存在");
    }

    // 检查是否更改了系统角色
    const roleChanged = data.role && data.role !== currentUser.role;
    console.log("Server Action: Role changed:", roleChanged, "Current:", currentUser.role, "New:", data.role);

    // 更新用户基本信息
    const updateData: any = {
      name: data.name,
      email: data.email,
    };

    // 如果提供了角色，更新角色
    if (data.role) {
      updateData.role = data.role;
    }

    // 如果提供了密码，更新密码
    if (data.password) {
      // 在实际应用中，应该对密码进行哈希处理
      updateData.password = data.password;
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id },
      data: updateData,
      include: {
        employee: true,
        userRoles: {
          include: {
            role: true,
          }
        }
      }
    });

    // 记录系统日志
    await prisma.systemLog.create({
      data: {
        module: "用户管理",
        level: "info",
        message: `更新用户: ${user.email}`,
        details: JSON.stringify({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          roleChanged,
        }),
        userId: id, // 记录操作者ID
      }
    });

    // 格式化返回数据
    const formattedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      roles: user.userRoles.map(ur => ur.roleId),
      employee: user.employee,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    console.log("Server Action: User updated successfully:", formattedUser);
    return formattedUser;
  } catch (error) {
    console.error("Error updating user:", error)
    throw new Error(error.message || "Failed to update user")
  }
}

// 珐琅馆销售相关操作
export async function getGallerySales(startDate?: string, endDate?: string, employeeId?: string) {
  try {
    // 构建查询条件
    let whereClause: any = {}

    if (startDate && endDate) {
      whereClause.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    }

    if (employeeId && employeeId !== "all") {
      whereClause.employeeId = Number.parseInt(employeeId)
    }

    // 获取珐琅馆销售记录
    const sales = await prisma.gallerySale.findMany({
      where: whereClause,
      include: {
        employee: true,
        customer: true,
        salesItems: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    })

    return sales
  } catch (error) {
    console.error("Error fetching gallery sales:", error)
    throw new Error("Failed to fetch gallery sales")
  }
}

// 用户设置相关操作
export async function getCurrentUser() {
  try {
    // 从会话中获取当前用户信息 - 使用动态导入避免循环依赖
    // 使用更健壮的导入方式
    const authModule = await import("@/auth").catch(e => {
      console.error("Failed to import auth module:", e);
      return { auth: null };
    });

    if (!authModule.auth) {
      throw new Error("认证模块加载失败");
    }

    const session = await authModule.auth();

    if (!session || !session.user || !session.user.id) {
      throw new Error("未登录");
    }

    // 使用会话中的用户ID查询用户信息
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      include: {
        userSettings: true,
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new Error("用户不存在");
    }

    return user;
  } catch (error) {
    console.error("Error fetching current user:", error);
    // 提供更详细的错误信息以便调试
    if (error instanceof Error) {
      throw new Error(`获取当前用户信息失败: ${error.message}`);
    } else {
      throw new Error("获取当前用户信息失败");
    }
  }
}

export async function updateUserProfile(userId: string, data: any) {
  try {
    // 验证必填字段
    if (!data.name || data.name.trim() === "") {
      throw new Error("用户名不能为空");
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw new Error("请输入有效的电子邮件地址");
    }

    // 检查电子邮件是否已被其他用户使用
    const existingUser = await prisma.user.findFirst({
      where: {
        email: data.email,
        id: {
          not: userId,
        },
      },
    });

    if (existingUser) {
      throw new Error("该电子邮件地址已被使用");
    }

    // 更新用户资料
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: data.name.trim(),
        email: data.email,
        phone: data.phone || null,
        bio: data.bio || null,
        image: data.image || null,
      },
    });

    revalidatePath("/accounts/profile");
    return updatedUser;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw new Error(error instanceof Error ? error.message : "更新用户资料失败");
  }
}

export async function updateUserPassword(userId: string, data: any) {
  try {
    // 验证必填字段
    if (!data.currentPassword) {
      throw new Error("当前密码不能为空");
    }

    if (!data.newPassword) {
      throw new Error("新密码不能为空");
    }

    if (data.newPassword !== data.confirmPassword) {
      throw new Error("新密码和确认密码不匹配");
    }

    // 获取用户
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { password: true },
    });

    if (!user) {
      throw new Error("用户不存在");
    }

    // 在实际应用中，这里应该验证当前密码是否正确
    // 例如：if (!await bcrypt.compare(data.currentPassword, user.password)) {
    //   throw new Error("当前密码不正确");
    // }

    // 更新密码
    // 在实际应用中，这里应该对新密码进行哈希处理
    // 例如：const hashedPassword = await bcrypt.hash(data.newPassword, 10);
    const hashedPassword = data.newPassword; // 简化处理，实际应用中不要这样做

    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating user password:", error);
    throw new Error(error instanceof Error ? error.message : "更新密码失败");
  }
}

export async function updateUserSettings(userId: string, data: any) {
  try {
    // 检查用户设置是否存在
    const existingSettings = await prisma.userSettings.findUnique({
      where: { userId },
    });

    if (existingSettings) {
      // 更新现有设置
      const updatedSettings = await prisma.userSettings.update({
        where: { userId },
        data: {
          theme: data.theme || existingSettings.theme,
          language: data.language || existingSettings.language,
          enableNotifications: data.enableNotifications !== undefined ? data.enableNotifications : existingSettings.enableNotifications,
          enableTwoFactorAuth: data.enableTwoFactorAuth !== undefined ? data.enableTwoFactorAuth : existingSettings.enableTwoFactorAuth,
        },
      });

      revalidatePath("/accounts/profile");
      revalidatePath("/accounts/settings");
      return updatedSettings;
    } else {
      // 创建新设置
      const newSettings = await prisma.userSettings.create({
        data: {
          userId,
          theme: data.theme || "light",
          language: data.language || "zh-CN",
          enableNotifications: data.enableNotifications !== undefined ? data.enableNotifications : true,
          enableTwoFactorAuth: data.enableTwoFactorAuth !== undefined ? data.enableTwoFactorAuth : false,
        },
      });

      revalidatePath("/accounts/profile");
      revalidatePath("/accounts/settings");
      return newSettings;
    }
  } catch (error) {
    console.error("Error updating user settings:", error);
    throw new Error(error instanceof Error ? error.message : "更新用户设置失败");
  }
}

export async function getUserLoginHistory(userId: string) {
  try {
    // 获取用户登录历史
    const loginHistory = await prisma.userLoginHistory.findMany({
      where: { userId },
      orderBy: {
        loginTime: "desc",
      },
      take: 10, // 只获取最近10条记录
    });

    return loginHistory;
  } catch (error) {
    console.error("Error fetching user login history:", error);
    throw new Error("获取登录历史失败");
  }
}

export async function recordUserLogin(userId: string, ipAddress: string, userAgent: string) {
  try {
    // 记录用户登录
    const loginRecord = await prisma.userLoginHistory.create({
      data: {
        userId,
        ipAddress,
        userAgent,
        loginTime: new Date(),
        status: "success",
      },
    });

    return loginRecord;
  } catch (error) {
    console.error("Error recording user login:", error);
    // 这里不抛出错误，因为登录记录失败不应该影响用户登录
    return null;
  }
}

export async function enableTwoFactorAuth(userId: string) {
  try {
    // 在实际应用中，这里应该生成2FA密钥并返回给用户
    // 例如：const secret = speakeasy.generateSecret({ length: 20 });

    // 更新用户设置
    await prisma.userSettings.update({
      where: { userId },
      data: {
        enableTwoFactorAuth: true,
        twoFactorAuthSecret: "dummy_secret_key", // 在实际应用中，这应该是一个真实的密钥
      },
    });

    return {
      success: true,
      qrCodeUrl: "https://example.com/qrcode", // 在实际应用中，这应该是一个真实的QR码URL
    };
  } catch (error) {
    console.error("Error enabling two-factor auth:", error);
    throw new Error("启用两步验证失败");
  }
}

export async function disableTwoFactorAuth(userId: string) {
  try {
    // 更新用户设置
    await prisma.userSettings.update({
      where: { userId },
      data: {
        enableTwoFactorAuth: false,
        twoFactorAuthSecret: null,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error disabling two-factor auth:", error);
    throw new Error("禁用两步验证失败");
  }
}

// 角色相关操作
export async function getRoles() {
  try {
    // 检查角色表是否为空，如果为空则尝试初始化
    const roleCount = await prisma.role.count();
    if (roleCount === 0) {
      console.log("角色表为空，尝试初始化账号管理系统...");
      try {
        // 动态导入初始化函数，避免循环依赖
        const { initAccountSystem } = await import("@/lib/init-account-system");
        await initAccountSystem();
        console.log("账号管理系统初始化成功");
      } catch (initError) {
        console.error("初始化账号管理系统失败:", initError);
        // 即使初始化失败，也继续尝试获取角色列表
      }
    }

    // 从数据库获取角色
    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            userRoles: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // 格式化返回数据
    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    }));

    return formattedRoles;
  } catch (error) {
    console.error("获取角色失败:", error);
    // 返回空数组而不是抛出错误，以便前端可以优雅地处理
    return [];
  }
}

export async function getRole(id: number) {
  try {
    // 获取角色
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            userRoles: true,
          },
        },
      },
    });

    if (!role) {
      throw new Error("角色不存在");
    }

    // 格式化返回数据
    return {
      id: role.id,
      name: role.name,
      code: role.code,
      description: role.description,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  } catch (error) {
    console.error("获取角色失败:", error);
    throw new Error("获取角色失败");
  }
}

export async function createRole(data: { name: string; code: string; description?: string; isSystem?: boolean }) {
  try {
    // 验证必填字段
    if (!data.name || !data.code) {
      throw new Error("角色名称和代码不能为空");
    }

    // 检查角色代码是否已存在
    const existingRole = await prisma.role.findUnique({
      where: { code: data.code },
    });

    if (existingRole) {
      throw new Error("角色代码已存在");
    }

    // 创建角色
    const role = await prisma.role.create({
      data: {
        name: data.name,
        code: data.code,
        description: data.description,
        isSystem: data.isSystem || false,
      },
    });

    return role;
  } catch (error) {
    console.error("创建角色失败:", error);
    throw new Error(error.message || "创建角色失败");
  }
}

export async function updateRole(id: number, data: { name: string; description?: string; isSystem?: boolean }) {
  try {
    // 验证必填字段
    if (!data.name) {
      throw new Error("角色名称不能为空");
    }

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      throw new Error("角色不存在");
    }

    // 更新角色
    const role = await prisma.role.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description,
        isSystem: data.isSystem !== undefined ? data.isSystem : existingRole.isSystem,
      },
    });

    return role;
  } catch (error) {
    console.error("更新角色失败:", error);
    throw new Error(error.message || "更新角色失败");
  }
}

export async function deleteRole(id: number) {
  try {
    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            userRoles: true,
          },
        },
      },
    });

    if (!existingRole) {
      throw new Error("角色不存在");
    }

    // 检查是否为系统角色
    if (existingRole.isSystem) {
      throw new Error("系统角色不能删除");
    }

    // 检查是否有用户使用此角色
    if (existingRole._count.userRoles > 0) {
      throw new Error("该角色正在被用户使用，无法删除");
    }

    // 删除角色
    await prisma.role.delete({
      where: { id },
    });

    return { success: true };
  } catch (error) {
    console.error("删除角色失败:", error);
    throw new Error(error.message || "删除角色失败");
  }
}

// 用户相关操作
export async function getUsers() {
  try {
    const users = await prisma.user.findMany({
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
          },
        },
        userRoles: {
          include: {
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // 格式化用户数据
    return users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      employeeId: user.employeeId,
      employeeName: user.employee?.name,
      employeePosition: user.employee?.position,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: user.userRoles.map(ur => ur.role.id),
      roleDetails: user.userRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        code: ur.role.code,
        description: ur.role.description,
      })),
    }));
  } catch (error) {
    console.error("获取用户列表失败:", error);
    throw new Error("获取用户列表失败");
  }
}

export async function createUser(data: any) {
  try {
    // 验证必填字段
    if (!data.name || !data.email || !data.password) {
      throw new Error("用户名、邮箱和密码为必填项");
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      throw new Error("该邮箱已被注册");
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password, // 注意：实际应用中应该对密码进行哈希处理
        role: data.role || "user",
      },
    });

    // 如果指定了角色，分配角色
    if (data.roleIds && Array.isArray(data.roleIds) && data.roleIds.length > 0) {
      await prisma.userRole.createMany({
        data: data.roleIds.map((roleId: number) => ({
          userId: user.id,
          roleId,
        })),
        skipDuplicates: true,
      });
    }

    revalidatePath("/settings/account");
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      roles: data.roleIds || [],
    };
  } catch (error) {
    console.error("创建用户失败:", error);
    throw new Error(error instanceof Error ? error.message : "创建用户失败");
  }
}

export async function updateUser(id: string, data: any) {
  try {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new Error("用户不存在");
    }

    // 如果更改了邮箱，检查新邮箱是否已被使用
    if (data.email && data.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: data.email },
      });

      if (emailExists) {
        throw new Error("该邮箱已被其他用户使用");
      }
    }

    // 准备更新数据
    const updateData: any = {};

    if (data.name) updateData.name = data.name;
    if (data.email) updateData.email = data.email;
    if (data.role) updateData.role = data.role;
    if (data.password) updateData.password = data.password; // 注意：实际应用中应该对密码进行哈希处理

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
    });

    // 如果指定了角色，更新角色
    if (data.roleIds && Array.isArray(data.roleIds)) {
      // 删除现有角色
      await prisma.userRole.deleteMany({
        where: { userId: id },
      });

      // 添加新角色
      if (data.roleIds.length > 0) {
        await prisma.userRole.createMany({
          data: data.roleIds.map((roleId: number) => ({
            userId: id,
            roleId,
          })),
          skipDuplicates: true,
        });
      }
    }

    revalidatePath("/settings/account");
    return {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      roles: data.roleIds || [],
    };
  } catch (error) {
    console.error("更新用户失败:", error);
    throw new Error(error instanceof Error ? error.message : "更新用户失败");
  }
}

export async function deleteUser(id: string) {
  try {
    console.log("Server Action: Deleting user with id:", id);

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new Error("用户不存在");
    }

    // 删除用户角色关联
    await prisma.userRole.deleteMany({
      where: { userId: id },
    });

    // 删除用户
    await prisma.user.delete({
      where: { id },
    });

    console.log("Server Action: User deleted successfully");

    revalidatePath("/settings/account");
    return { success: true };
  } catch (error) {
    console.error("Server Action: Error deleting user:", error);
    throw new Error(error instanceof Error ? error.message : "删除用户失败");
  }
}

export async function getUserRoles(userId: string) {
  try {
    // 获取用户角色
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: true,
      },
    });

    // 格式化角色数据
    const roles = userRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      code: ur.role.code,
      description: ur.role.description,
      isSystem: ur.role.isSystem,
    }));

    // 同时返回角色ID数组，方便前端使用
    const roleIds = userRoles.map(ur => ur.roleId);

    // 获取用户信息，包括旧版roles字段
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    return {
      roles,
      roleIds,
      legacyRoles: user?.roles || [],
    };
  } catch (error) {
    console.error("获取用户角色失败:", error);
    throw new Error("获取用户角色失败");
  }
}

export async function updateUserRoles(userId: string, roleIds: number[]) {
  try {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error("用户不存在");
    }

    // 开始事务
    await prisma.$transaction(async (tx) => {
      // 删除现有用户角色
      await tx.userRole.deleteMany({
        where: { userId },
      });

      // 添加新的用户角色
      if (roleIds.length > 0) {
        const roleData = roleIds.map(roleId => ({
          userId,
          roleId: typeof roleId === 'string' ? parseInt(roleId) : roleId,
        }));

        await tx.userRole.createMany({
          data: roleData,
          skipDuplicates: true,
        });
      }

      // 同时更新旧版roles字段，保持兼容性
      await tx.user.update({
        where: { id: userId },
        data: {
          roles: roleIds.map(r => typeof r === 'string' ? parseInt(r) : r),
        },
      });
    });

    // 获取更新后的用户角色
    const updatedUserRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: true,
      },
    });

    // 格式化角色数据
    const updatedRoles = updatedUserRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      code: ur.role.code,
      description: ur.role.description,
      isSystem: ur.role.isSystem,
    }));

    // 同时返回角色ID数组，方便前端使用
    const updatedRoleIds = updatedUserRoles.map(ur => ur.roleId);

    revalidatePath("/settings/account");
    return {
      success: true,
      roles: updatedRoles,
      roleIds: updatedRoleIds,
    };
  } catch (error) {
    console.error("更新用户角色失败:", error);
    throw new Error(error instanceof Error ? error.message : "更新用户角色失败");
  }
}

export async function getRolePermissions(roleId: number) {
  try {
    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new Error("角色不存在");
    }

    // 获取角色权限
    const rolePermissions = await prisma.rolePermission.findMany({
      where: { roleId },
      include: {
        permission: true,
      },
    });

    // 格式化返回数据
    const permissions = rolePermissions.map(rp => ({
      id: rp.permission.id,
      name: rp.permission.name,
      code: rp.permission.code,
      module: rp.permission.module,
      description: rp.permission.description,
    }));

    return permissions;
  } catch (error) {
    console.error("获取角色权限失败:", error);
    throw new Error("获取角色权限失败");
  }
}

export async function updateRolePermissions(roleId: number, permissionIds: number[]) {
  try {
    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new Error("角色不存在");
    }

    // 开始事务
    await prisma.$transaction(async (tx) => {
      // 删除现有角色权限
      await tx.rolePermission.deleteMany({
        where: { roleId },
      });

      // 添加新的角色权限
      if (permissionIds.length > 0) {
        const permissionData = permissionIds.map(permissionId => ({
          roleId,
          permissionId,
        }));

        await tx.rolePermission.createMany({
          data: permissionData,
        });
      }
    });

    return { success: true };
  } catch (error) {
    console.error("更新角色权限失败:", error);
    throw new Error("更新角色权限失败");
  }
}

export async function getPermissions(module?: string) {
  try {
    // 构建查询条件
    const whereClause = module ? { module } : {};

    // 从数据库获取权限
    const permissions = await prisma.permission.findMany({
      where: whereClause,
      orderBy: [
        { module: "asc" },
        { code: "asc" },
      ],
    });

    return permissions;
  } catch (error) {
    console.error("获取权限失败:", error);
    throw new Error("获取权限失败");
  }
}

// 这部分代码已被移除，因为在文件的其他部分已经有了更完整的实现

// 团建服务团队成员相关操作
export async function getWorkshopTeamMembers(role?: string) {
  try {
    const whereClause = role && role !== "all" ? { role } : {}

    return await prisma.workshopTeamMember.findMany({
      where: whereClause,
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching workshop team members:", error)
    throw new Error("获取团建服务团队成员失败")
  }
}

export async function createWorkshopTeamMember(data: any) {
  try {
    // 验证必填字段
    if (!data.employeeId || !data.role) {
      throw new Error("员工ID和角色为必填项")
    }

    // 检查员工是否存在
    const employee = await prisma.employee.findUnique({
      where: { id: parseInt(data.employeeId) },
    })

    if (!employee) {
      throw new Error("员工不存在")
    }

    // 检查是否已存在相同员工和角色的记录
    const existingMember = await prisma.workshopTeamMember.findFirst({
      where: {
        employeeId: parseInt(data.employeeId),
        role: data.role,
      },
    })

    if (existingMember) {
      throw new Error("该员工已经以相同角色存在于团队中")
    }

    // 创建团队成员
    const teamMember = await prisma.workshopTeamMember.create({
      data: {
        employeeId: parseInt(data.employeeId),
        role: data.role,
        specialties: data.specialties || [],
        rating: parseFloat(data.rating) || 5.0,
        maxWorkshopsPerDay: parseInt(data.maxWorkshopsPerDay) || 2,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return teamMember
  } catch (error) {
    console.error("Error creating workshop team member:", error)
    throw new Error(error instanceof Error ? error.message : "创建团建服务团队成员失败")
  }
}

export async function updateWorkshopTeamMember(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.employeeId || !data.role) {
      throw new Error("员工ID和角色为必填项")
    }

    // 检查团队成员是否存在
    const existingMember = await prisma.workshopTeamMember.findUnique({
      where: { id },
    })

    if (!existingMember) {
      throw new Error("团建服务团队成员不存在")
    }

    // 如果更改了员工ID或角色，检查是否已存在相同员工和角色的记录
    if (parseInt(data.employeeId) !== existingMember.employeeId || data.role !== existingMember.role) {
      const duplicateMember = await prisma.workshopTeamMember.findFirst({
        where: {
          employeeId: parseInt(data.employeeId),
          role: data.role,
          NOT: {
            id,
          },
        },
      })

      if (duplicateMember) {
        throw new Error("该员工已经以相同角色存在于团队中")
      }
    }

    // 更新团队成员
    const teamMember = await prisma.workshopTeamMember.update({
      where: { id },
      data: {
        employeeId: parseInt(data.employeeId),
        role: data.role,
        specialties: data.specialties || [],
        rating: parseFloat(data.rating) || 5.0,
        maxWorkshopsPerDay: parseInt(data.maxWorkshopsPerDay) || 2,
        isActive: data.isActive,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return teamMember
  } catch (error) {
    console.error("Error updating workshop team member:", error)
    throw new Error(error instanceof Error ? error.message : "更新团建服务团队成员失败")
  }
}

export async function deleteWorkshopTeamMember(id: number) {
  try {
    // 检查团队成员是否存在
    const existingMember = await prisma.workshopTeamMember.findUnique({
      where: { id },
    })

    if (!existingMember) {
      throw new Error("团建服务团队成员不存在")
    }

    // 删除团队成员
    await prisma.workshopTeamMember.delete({
      where: { id },
    })

    revalidatePath("/workshop")
    return { success: true }
  } catch (error) {
    console.error("Error deleting workshop team member:", error)
    throw new Error(error instanceof Error ? error.message : "删除团建服务团队成员失败")
  }
}

// 使用第1255行已定义的getWorkshopActivities函数

// 团建价格相关操作
export async function getWorkshopPrices(channelId?: string, activityId?: string) {
  try {
    let whereClause: any = {}

    if (channelId && channelId !== "all") {
      whereClause.channelId = parseInt(channelId)
    }

    if (activityId && activityId !== "all") {
      whereClause.activityId = parseInt(activityId)
    }

    return await prisma.workshopPrice.findMany({
      where: whereClause,
      include: {
        activity: {
          select: {
            id: true,
            name: true,
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
              },
            },
          },
        },
        channel: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    })
  } catch (error) {
    console.error("Error fetching workshop prices:", error)
    throw new Error("获取团建价格失败")
  }
}

export async function createWorkshopPrice(data: any) {
  try {
    // 验证必填字段
    if (!data.channelId || !data.basePrice || !data.pricePerPerson) {
      throw new Error("渠道、基础价格和人均价格为必填项")
    }

    // 检查渠道是否存在
    const channel = await prisma.channel.findUnique({
      where: { id: parseInt(data.channelId) },
    })

    if (!channel) {
      throw new Error("渠道不存在")
    }

    // 如果提供了活动ID，检查活动是否存在
    if (data.activityId) {
      const activity = await prisma.workshopActivity.findUnique({
        where: { id: parseInt(data.activityId) },
      })

      if (!activity) {
        throw new Error("团建活动不存在")
      }

      // 检查是否已存在相同活动和渠道的价格
      const existingPrice = await prisma.workshopPrice.findFirst({
        where: {
          activityId: parseInt(data.activityId),
          channelId: parseInt(data.channelId),
        },
      })

      if (existingPrice) {
        throw new Error("该活动在该渠道已有价格配置")
      }
    }

    // 创建价格
    const price = await prisma.workshopPrice.create({
      data: {
        activityId: data.activityId ? parseInt(data.activityId) : null,
        channelId: parseInt(data.channelId),
        basePrice: parseFloat(data.basePrice),
        pricePerPerson: parseFloat(data.pricePerPerson),
        minParticipants: parseInt(data.minParticipants) || 5,
        maxParticipants: parseInt(data.maxParticipants) || 30,
        materialFee: parseFloat(data.materialFee) || 0,
        teacherFee: parseFloat(data.teacherFee) || 0,
        assistantFee: parseFloat(data.assistantFee) || 0,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
      include: {
        activity: {
          select: {
            id: true,
            name: true,
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
              },
            },
          },
        },
        channel: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return price
  } catch (error) {
    console.error("Error creating workshop price:", error)
    throw new Error(error instanceof Error ? error.message : "创建团建价格失败")
  }
}

export async function updateWorkshopPrice(id: number, data: any) {
  try {
    // 验证必填字段
    if (!data.channelId || !data.basePrice || !data.pricePerPerson) {
      throw new Error("渠道、基础价格和人均价格为必填项")
    }

    // 检查价格是否存在
    const existingPrice = await prisma.workshopPrice.findUnique({
      where: { id },
    })

    if (!existingPrice) {
      throw new Error("团建价格不存在")
    }

    // 如果更改了活动ID或渠道ID，检查是否已存在相同活动和渠道的价格
    if (
      (data.activityId && parseInt(data.activityId) !== existingPrice.activityId) ||
      parseInt(data.channelId) !== existingPrice.channelId
    ) {
      const duplicatePrice = await prisma.workshopPrice.findFirst({
        where: {
          activityId: data.activityId ? parseInt(data.activityId) : null,
          channelId: parseInt(data.channelId),
          NOT: {
            id,
          },
        },
      })

      if (duplicatePrice) {
        throw new Error("该活动在该渠道已有价格配置")
      }
    }

    // 更新价格
    const price = await prisma.workshopPrice.update({
      where: { id },
      data: {
        activityId: data.activityId ? parseInt(data.activityId) : null,
        channelId: parseInt(data.channelId),
        basePrice: parseFloat(data.basePrice),
        pricePerPerson: parseFloat(data.pricePerPerson),
        minParticipants: parseInt(data.minParticipants) || 5,
        maxParticipants: parseInt(data.maxParticipants) || 30,
        materialFee: parseFloat(data.materialFee) || 0,
        teacherFee: parseFloat(data.teacherFee) || 0,
        assistantFee: parseFloat(data.assistantFee) || 0,
        isActive: data.isActive,
      },
      include: {
        activity: {
          select: {
            id: true,
            name: true,
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
              },
            },
          },
        },
        channel: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    revalidatePath("/workshop")
    return price
  } catch (error) {
    console.error("Error updating workshop price:", error)
    throw new Error(error instanceof Error ? error.message : "更新团建价格失败")
  }
}

export async function deleteWorkshopPrice(id: number) {
  try {
    // 检查价格是否存在
    const existingPrice = await prisma.workshopPrice.findUnique({
      where: { id },
    })

    if (!existingPrice) {
      throw new Error("团建价格不存在")
    }

    // 删除价格
    await prisma.workshopPrice.delete({
      where: { id },
    })

    revalidatePath("/workshop")
    return { success: true }
  } catch (error) {
    console.error("Error deleting workshop price:", error)
    throw new Error(error instanceof Error ? error.message : "删除团建价格失败")
  }
}

// 数据库备份相关操作
export async function createBackup(reason: string = "manual") {
  try {
    // 导入备份模块
    const { createBackup: createBackupFn } = await import("./backup")

    // 创建备份
    const backupPath = await createBackupFn(reason)

    return { success: true, path: backupPath }
  } catch (error) {
    console.error("Error creating backup:", error)
    throw new Error("创建数据库备份失败")
  }
}

export async function getBackupsList() {
  try {
    // 导入备份模块
    const { getBackupsList: getBackupsListFn } = await import("./backup")

    // 获取备份列表
    return getBackupsListFn()
  } catch (error) {
    console.error("Error getting backups list:", error)
    throw new Error("获取备份列表失败")
  }
}

export async function restoreBackup(filePath: string) {
  try {
    // 导入备份模块
    const { restoreBackup: restoreBackupFn } = await import("./backup")

    // 恢复备份
    return await restoreBackupFn(filePath)
  } catch (error) {
    console.error("Error restoring backup:", error)
    throw new Error("恢复数据库备份失败")
  }
}

// 团建报表相关操作
export async function getWorkshopReport(startDate?: string, endDate?: string) {
  try {
    // 解析日期参数
    let start: Date
    let end: Date

    if (startDate && endDate) {
      start = new Date(startDate)
      end = new Date(endDate)
    } else {
      // 默认为当年数据
      const now = new Date()
      start = new Date(now.getFullYear(), 0, 1) // 当年1月1日
      end = now
    }

    // 获取团建记录
    const workshops = await prisma.workshop.findMany({
      where: {
        date: {
          gte: start,
          lte: end,
        },
      },
      include: {
        teacher: true,
        assistant: true,
        product: true,
        activity: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    })

    // 计算总体统计数据
    const totalWorkshops = workshops.length
    const totalParticipants = workshops.reduce((sum, workshop) => sum + workshop.participants, 0)

    // 计算总收入（基于活动价格和参与人数）
    let totalRevenue = 0
    for (const workshop of workshops) {
      if (workshop.activity) {
        totalRevenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        totalRevenue += workshop.product.price * workshop.participants
      }
    }

    const averageParticipants = totalWorkshops > 0 ? Math.round(totalParticipants / totalWorkshops) : 0
    const averageRevenuePerWorkshop = totalWorkshops > 0 ? Math.round(totalRevenue / totalWorkshops) : 0
    const averageRevenuePerParticipant = totalParticipants > 0 ? Math.round(totalRevenue / totalParticipants) : 0

    // 按渠道分组统计
    const channelMap = new Map()
    for (const workshop of workshops) {
      const channelId = workshop.channelId || 0
      const channelName = channelId === 0 ? "直营渠道" : "未知渠道" // 这里应该从数据库获取渠道名称

      if (!channelMap.has(channelId)) {
        channelMap.set(channelId, {
          name: channelName,
          count: 0,
          participants: 0,
          revenue: 0,
        })
      }

      const channelData = channelMap.get(channelId)
      channelData.count += 1
      channelData.participants += workshop.participants

      if (workshop.activity) {
        channelData.revenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        channelData.revenue += workshop.product.price * workshop.participants
      }
    }

    const byChannel = Array.from(channelMap.values())

    // 按产品分组统计
    const productMap = new Map()
    for (const workshop of workshops) {
      const productId = workshop.activity?.productId || workshop.productId || 0
      const productName = workshop.activity?.product?.name || workshop.product?.name || "未知产品"

      if (!productMap.has(productId)) {
        productMap.set(productId, {
          name: productName,
          count: 0,
          participants: 0,
          revenue: 0,
        })
      }

      const productData = productMap.get(productId)
      productData.count += 1
      productData.participants += workshop.participants

      if (workshop.activity) {
        productData.revenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        productData.revenue += workshop.product.price * workshop.participants
      }
    }

    const byProduct = Array.from(productMap.values())

    // 按讲师分组统计
    const teacherMap = new Map()
    for (const workshop of workshops) {
      const teacherId = workshop.teacherId
      const teacherName = workshop.teacher.name

      if (!teacherMap.has(teacherId)) {
        teacherMap.set(teacherId, {
          name: teacherName,
          count: 0,
          participants: 0,
          revenue: 0,
          rating: 5.0, // 默认评分，实际应该从评价系统获取
        })
      }

      const teacherData = teacherMap.get(teacherId)
      teacherData.count += 1
      teacherData.participants += workshop.participants

      if (workshop.activity) {
        teacherData.revenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        teacherData.revenue += workshop.product.price * workshop.participants
      }
    }

    const byTeacher = Array.from(teacherMap.values()).map(teacher => ({
      ...teacher,
      averageParticipants: Math.round(teacher.participants / teacher.count),
      percentage: Math.round((teacher.count / totalWorkshops) * 100),
    }))

    // 按月份分组统计
    const monthMap = new Map()
    for (const workshop of workshops) {
      const month = `${workshop.date.getFullYear()}-${String(workshop.date.getMonth() + 1).padStart(2, '0')}`

      if (!monthMap.has(month)) {
        monthMap.set(month, {
          month,
          count: 0,
          participants: 0,
          revenue: 0,
        })
      }

      const monthData = monthMap.get(month)
      monthData.count += 1
      monthData.participants += workshop.participants

      if (workshop.activity) {
        monthData.revenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        monthData.revenue += workshop.product.price * workshop.participants
      }
    }

    // 计算月度平均值
    for (const monthData of monthMap.values()) {
      monthData.averageParticipants = Math.round(monthData.participants / monthData.count)
      monthData.averageRevenue = Math.round(monthData.revenue / monthData.count)
    }

    const byMonth = Array.from(monthMap.values()).sort((a, b) => a.month.localeCompare(b.month))

    // 按地点分组统计
    const locationMap = new Map()
    for (const workshop of workshops) {
      const locationType = workshop.locationType
      const locationName = locationType === "in-gallery" ? "馆内" : "馆外"

      if (!locationMap.has(locationType)) {
        locationMap.set(locationType, {
          name: locationName,
          count: 0,
          participants: 0,
          revenue: 0,
        })
      }

      const locationData = locationMap.get(locationType)
      locationData.count += 1
      locationData.participants += workshop.participants

      if (workshop.activity) {
        locationData.revenue += workshop.activity.price * workshop.participants
      } else if (workshop.product) {
        locationData.revenue += workshop.product.price * workshop.participants
      }
    }

    const byLocation = Array.from(locationMap.values()).map(location => ({
      ...location,
      percentage: Math.round((location.count / totalWorkshops) * 100),
    }))

    // 格式化最近的团建记录
    const recentWorkshops = workshops.slice(0, 10).map(workshop => ({
      id: workshop.id,
      date: workshop.date.toISOString().split('T')[0],
      product: workshop.activity?.product?.name || workshop.product?.name || "未知产品",
      teacher: workshop.teacher.name,
      assistant: workshop.assistant?.name || null,
      channel: "直营渠道", // 这里应该从数据库获取渠道名称
      location: workshop.locationType === "in-gallery" ? "馆内" : `馆外 - ${workshop.location}`,
      participants: workshop.participants,
      revenue: workshop.activity
        ? workshop.activity.price * workshop.participants
        : (workshop.product ? workshop.product.price * workshop.participants : 0),
    }))

    // 构建响应数据
    const reportData = {
      summary: {
        totalWorkshops,
        totalParticipants,
        totalRevenue,
        averageParticipants,
        averageRevenuePerWorkshop,
        averageRevenuePerParticipant,
      },
      byChannel,
      byProduct,
      byTeacher,
      byMonth,
      byLocation,
      recentWorkshops,
    }

    return reportData
  } catch (error) {
    console.error("Error generating workshop report:", error)
    throw new Error("生成团建报表失败")
  }
}
