import { PrismaClient, Prisma, FinancialTransactionType } from '@prisma/client'
import { z } from 'zod'
import { Decimal } from '@prisma/client/runtime/library'

// 财务业务逻辑核心类
export class FinanceBusiness {
  constructor(private prisma: PrismaClient) {}

  // 自动对账功能
  async performReconciliation(data: {
    accountId: string
    bankStatementBalance: number
    reconciliationDate: Date
    operatorId: string
  }): Promise<{
    success: boolean
    reconciliationId?: string
    differences: Array<{
      transactionId: string
      description: string
      amount: number
      type: 'missing' | 'extra' | 'amount_mismatch'
    }>
    balanceDifference: number
    suggestedAdjustments: Array<{
      description: string
      amount: number
      type: 'debit' | 'credit'
    }>
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 获取账户信息
        const account = await tx.financialAccount.findUnique({
          where: { id: data.accountId }
        })

        if (!account) {
          return {
            success: false,
            differences: [],
            balanceDifference: 0,
            suggestedAdjustments: []
          }
        }

        // 计算系统账户余额
        const systemBalance = account.balance.toNumber()
        const balanceDifference = data.bankStatementBalance - systemBalance

        // 获取最近的交易记录
        const recentTransactions = await tx.financialTransaction.findMany({
          where: {
            OR: [
              { fromAccountId: data.accountId },
              { toAccountId: data.accountId }
            ],
            transactionDate: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
            }
          },
          orderBy: { transactionDate: 'desc' }
        })

        // 分析差异
        const differences = []
        const suggestedAdjustments = []

        if (Math.abs(balanceDifference) > 0.01) { // 考虑浮点数精度
          if (balanceDifference > 0) {
            suggestedAdjustments.push({
              description: '银行对账调整 - 补记收入',
              amount: balanceDifference,
              type: 'credit' as const
            })
          } else {
            suggestedAdjustments.push({
              description: '银行对账调整 - 补记支出',
              amount: Math.abs(balanceDifference),
              type: 'debit' as const
            })
          }
        }

        // 创建对账记录
        const reconciliation = await tx.reconciliation.create({
          data: {
            accountId: data.accountId,
            reconciliationDate: data.reconciliationDate,
            systemBalance: new Decimal(systemBalance),
            bankBalance: new Decimal(data.bankStatementBalance),
            difference: new Decimal(balanceDifference),
            status: Math.abs(balanceDifference) < 0.01 ? 'MATCHED' : 'UNMATCHED',
            operatorId: data.operatorId
          }
        })

        return {
          success: true,
          reconciliationId: reconciliation.id,
          differences,
          balanceDifference,
          suggestedAdjustments
        }
      } catch (error) {
        console.error('对账失败:', error)
        return {
          success: false,
          differences: [],
          balanceDifference: 0,
          suggestedAdjustments: []
        }
      }
    })
  }

  // 成本核算
  async calculateProductCosts(data: {
    productId?: string
    categoryId?: string
    dateRange?: {
      startDate: Date
      endDate: Date
    }
  }): Promise<{
    productCosts: Array<{
      productId: string
      productName: string
      directMaterialCost: number
      directLaborCost: number
      manufacturingOverhead: number
      totalCost: number
      unitCost: number
      profitMargin: number
      sellingPrice: number
    }>
    summary: {
      totalDirectMaterials: number
      totalDirectLabor: number
      totalOverhead: number
      totalCost: number
      averageUnitCost: number
    }
  }> {
    // 构建查询条件
    const whereClause: any = {}
    if (data.productId) whereClause.id = data.productId
    if (data.categoryId) whereClause.categoryId = data.categoryId

    const products = await this.prisma.product.findMany({
      where: whereClause,
      include: {
        category: true,
        productionOrders: {
          where: data.dateRange ? {
            createdAt: {
              gte: data.dateRange.startDate,
              lte: data.dateRange.endDate
            }
          } : undefined,
          include: {
            costRecords: true
          }
        }
      }
    })

    const productCosts = []
    let totalDirectMaterials = 0
    let totalDirectLabor = 0
    let totalOverhead = 0

    for (const product of products) {
      let directMaterialCost = 0
      let directLaborCost = 0
      let manufacturingOverhead = 0
      let totalQuantityProduced = 0

      // 计算所有生产订单的成本
      for (const order of product.productionOrders) {
        totalQuantityProduced += order.quantity
        
        for (const costRecord of order.costRecords) {
          switch (costRecord.costType) {
            case 'MATERIAL':
              directMaterialCost += costRecord.amount.toNumber()
              break
            case 'LABOR':
              directLaborCost += costRecord.amount.toNumber()
              break
            case 'OVERHEAD':
              manufacturingOverhead += costRecord.amount.toNumber()
              break
          }
        }
      }

      const totalCost = directMaterialCost + directLaborCost + manufacturingOverhead
      const unitCost = totalQuantityProduced > 0 ? totalCost / totalQuantityProduced : 0
      const sellingPrice = product.price?.toNumber() || 0
      const profitMargin = sellingPrice > 0 ? ((sellingPrice - unitCost) / sellingPrice) * 100 : 0

      productCosts.push({
        productId: product.id,
        productName: product.name,
        directMaterialCost,
        directLaborCost,
        manufacturingOverhead,
        totalCost,
        unitCost,
        profitMargin,
        sellingPrice
      })

      totalDirectMaterials += directMaterialCost
      totalDirectLabor += directLaborCost
      totalOverhead += manufacturingOverhead
    }

    const totalCost = totalDirectMaterials + totalDirectLabor + totalOverhead
    const averageUnitCost = productCosts.length > 0 
      ? productCosts.reduce((sum, p) => sum + p.unitCost, 0) / productCosts.length 
      : 0

    return {
      productCosts,
      summary: {
        totalDirectMaterials,
        totalDirectLabor,
        totalOverhead,
        totalCost,
        averageUnitCost
      }
    }
  }

  // 现金流分析
  async analyzeCashFlow(data: {
    accountIds?: string[]
    dateRange: {
      startDate: Date
      endDate: Date
    }
    granularity: 'daily' | 'weekly' | 'monthly'
  }): Promise<{
    cashFlowData: Array<{
      period: string
      date: Date
      inflow: number
      outflow: number
      netFlow: number
      cumulativeFlow: number
    }>
    summary: {
      totalInflow: number
      totalOutflow: number
      netCashFlow: number
      averageDailyFlow: number
      peakInflow: { date: Date; amount: number }
      peakOutflow: { date: Date; amount: number }
    }
    trends: {
      inflowTrend: 'increasing' | 'decreasing' | 'stable'
      outflowTrend: 'increasing' | 'decreasing' | 'stable'
      netFlowTrend: 'improving' | 'deteriorating' | 'stable'
    }
  }> {
    // 构建查询条件
    const whereClause: any = {
      transactionDate: {
        gte: data.dateRange.startDate,
        lte: data.dateRange.endDate
      }
    }

    if (data.accountIds && data.accountIds.length > 0) {
      whereClause.OR = [
        { fromAccountId: { in: data.accountIds } },
        { toAccountId: { in: data.accountIds } }
      ]
    }

    const transactions = await this.prisma.financialTransaction.findMany({
      where: whereClause,
      include: {
        fromAccount: true,
        toAccount: true
      },
      orderBy: { transactionDate: 'asc' }
    })

    // 按时间粒度分组数据
    const groupedData = new Map()
    let cumulativeFlow = 0

    for (const transaction of transactions) {
      const date = new Date(transaction.transactionDate)
      let periodKey: string

      switch (data.granularity) {
        case 'daily':
          periodKey = date.toISOString().split('T')[0]
          break
        case 'weekly':
          const weekStart = new Date(date)
          weekStart.setDate(date.getDate() - date.getDay())
          periodKey = weekStart.toISOString().split('T')[0]
          break
        case 'monthly':
          periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
          break
        default:
          periodKey = date.toISOString().split('T')[0]
      }

      if (!groupedData.has(periodKey)) {
        groupedData.set(periodKey, {
          period: periodKey,
          date: new Date(periodKey),
          inflow: 0,
          outflow: 0,
          netFlow: 0,
          cumulativeFlow: 0
        })
      }

      const group = groupedData.get(periodKey)
      const amount = transaction.amount.toNumber()

      // 确定是流入还是流出
      const isInflow = data.accountIds 
        ? data.accountIds.includes(transaction.toAccountId || '')
        : transaction.type === FinancialTransactionType.INCOME

      if (isInflow) {
        group.inflow += amount
      } else {
        group.outflow += amount
      }
    }

    // 计算净流量和累计流量
    const cashFlowData = Array.from(groupedData.values()).map(group => {
      group.netFlow = group.inflow - group.outflow
      cumulativeFlow += group.netFlow
      group.cumulativeFlow = cumulativeFlow
      return group
    })

    // 计算汇总数据
    const totalInflow = cashFlowData.reduce((sum, item) => sum + item.inflow, 0)
    const totalOutflow = cashFlowData.reduce((sum, item) => sum + item.outflow, 0)
    const netCashFlow = totalInflow - totalOutflow
    const averageDailyFlow = cashFlowData.length > 0 ? netCashFlow / cashFlowData.length : 0

    const peakInflow = cashFlowData.reduce((max, item) => 
      item.inflow > max.amount ? { date: item.date, amount: item.inflow } : max,
      { date: new Date(), amount: 0 }
    )

    const peakOutflow = cashFlowData.reduce((max, item) => 
      item.outflow > max.amount ? { date: item.date, amount: item.outflow } : max,
      { date: new Date(), amount: 0 }
    )

    // 分析趋势
    const firstHalf = cashFlowData.slice(0, Math.floor(cashFlowData.length / 2))
    const secondHalf = cashFlowData.slice(Math.floor(cashFlowData.length / 2))

    const avgInflowFirst = firstHalf.reduce((sum, item) => sum + item.inflow, 0) / firstHalf.length
    const avgInflowSecond = secondHalf.reduce((sum, item) => sum + item.inflow, 0) / secondHalf.length
    const avgOutflowFirst = firstHalf.reduce((sum, item) => sum + item.outflow, 0) / firstHalf.length
    const avgOutflowSecond = secondHalf.reduce((sum, item) => sum + item.outflow, 0) / secondHalf.length
    const avgNetFirst = firstHalf.reduce((sum, item) => sum + item.netFlow, 0) / firstHalf.length
    const avgNetSecond = secondHalf.reduce((sum, item) => sum + item.netFlow, 0) / secondHalf.length

    const inflowTrend = avgInflowSecond > avgInflowFirst * 1.05 ? 'increasing' 
                      : avgInflowSecond < avgInflowFirst * 0.95 ? 'decreasing' 
                      : 'stable'
    
    const outflowTrend = avgOutflowSecond > avgOutflowFirst * 1.05 ? 'increasing'
                       : avgOutflowSecond < avgOutflowFirst * 0.95 ? 'decreasing'
                       : 'stable'
    
    const netFlowTrend = avgNetSecond > avgNetFirst * 1.05 ? 'improving'
                       : avgNetSecond < avgNetFirst * 0.95 ? 'deteriorating'
                       : 'stable'

    return {
      cashFlowData,
      summary: {
        totalInflow,
        totalOutflow,
        netCashFlow,
        averageDailyFlow,
        peakInflow,
        peakOutflow
      },
      trends: {
        inflowTrend,
        outflowTrend,
        netFlowTrend
      }
    }
  }

  // 财务预算管理
  async manageBudget(data: {
    categoryId: string
    budgetAmount: number
    period: 'monthly' | 'quarterly' | 'yearly'
    startDate: Date
    operatorId: string
  }): Promise<{
    success: boolean
    budgetId?: string
    currentUsage: {
      spent: number
      remaining: number
      percentageUsed: number
    }
    forecast: {
      projectedSpend: number
      projectedOverage: number
      recommendedActions: string[]
    }
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 计算预算期间的结束日期
        const endDate = new Date(data.startDate)
        switch (data.period) {
          case 'monthly':
            endDate.setMonth(endDate.getMonth() + 1)
            break
          case 'quarterly':
            endDate.setMonth(endDate.getMonth() + 3)
            break
          case 'yearly':
            endDate.setFullYear(endDate.getFullYear() + 1)
            break
        }

        // 创建或更新预算
        const budget = await tx.budget.upsert({
          where: {
            categoryId_period_startDate: {
              categoryId: data.categoryId,
              period: data.period.toUpperCase() as any,
              startDate: data.startDate
            }
          },
          update: {
            amount: new Decimal(data.budgetAmount),
            updatedAt: new Date()
          },
          create: {
            categoryId: data.categoryId,
            amount: new Decimal(data.budgetAmount),
            period: data.period.toUpperCase() as any,
            startDate: data.startDate,
            endDate: endDate,
            operatorId: data.operatorId
          }
        })

        // 计算当前支出
        const currentSpent = await tx.financialTransaction.aggregate({
          where: {
            categoryId: data.categoryId,
            transactionDate: {
              gte: data.startDate,
              lte: endDate
            },
            type: FinancialTransactionType.EXPENSE
          },
          _sum: {
            amount: true
          }
        })

        const spent = currentSpent._sum.amount?.toNumber() || 0
        const remaining = data.budgetAmount - spent
        const percentageUsed = data.budgetAmount > 0 ? (spent / data.budgetAmount) * 100 : 0

        // 计算预测
        const daysSinceStart = Math.ceil((Date.now() - data.startDate.getTime()) / (1000 * 60 * 60 * 24))
        const totalDays = Math.ceil((endDate.getTime() - data.startDate.getTime()) / (1000 * 60 * 60 * 24))
        const daysRemaining = totalDays - daysSinceStart

        const dailySpendRate = daysSinceStart > 0 ? spent / daysSinceStart : 0
        const projectedSpend = spent + (dailySpendRate * daysRemaining)
        const projectedOverage = Math.max(0, projectedSpend - data.budgetAmount)

        // 生成建议
        const recommendedActions = []
        if (percentageUsed > 80) {
          recommendedActions.push('预算使用率超过80%，需要控制支出')
        }
        if (projectedOverage > 0) {
          recommendedActions.push(`预计将超支 ${projectedOverage.toFixed(2)} 元，建议调整支出计划`)
        }
        if (percentageUsed < 50 && daysRemaining < totalDays * 0.3) {
          recommendedActions.push('预算使用率较低，可以考虑增加投入或调整预算')
        }

        return {
          success: true,
          budgetId: budget.id,
          currentUsage: {
            spent,
            remaining,
            percentageUsed
          },
          forecast: {
            projectedSpend,
            projectedOverage,
            recommendedActions
          }
        }
      } catch (error) {
        console.error('预算管理失败:', error)
        return {
          success: false,
          currentUsage: { spent: 0, remaining: 0, percentageUsed: 0 },
          forecast: { projectedSpend: 0, projectedOverage: 0, recommendedActions: [] }
        }
      }
    })
  }

  // 财务报表生成
  async generateFinancialReports(data: {
    reportType: 'balance_sheet' | 'income_statement' | 'cash_flow'
    dateRange: {
      startDate: Date
      endDate: Date
    }
    includeComparisons?: boolean
  }): Promise<{
    success: boolean
    report: any
    generatedAt: Date
  }> {
    try {
      let report: any = {}

      switch (data.reportType) {
        case 'balance_sheet':
          report = await this.generateBalanceSheet(data.dateRange.endDate)
          break
        case 'income_statement':
          report = await this.generateIncomeStatement(data.dateRange)
          break
        case 'cash_flow':
          report = await this.generateCashFlowStatement(data.dateRange)
          break
      }

      return {
        success: true,
        report,
        generatedAt: new Date()
      }
    } catch (error) {
      console.error('生成财务报表失败:', error)
      return {
        success: false,
        report: {},
        generatedAt: new Date()
      }
    }
  }

  // 生成资产负债表
  private async generateBalanceSheet(asOfDate: Date) {
    // 获取所有账户余额
    const accounts = await this.prisma.financialAccount.findMany({
      include: {
        category: true
      }
    })

    const assets = { current: 0, fixed: 0, total: 0 }
    const liabilities = { current: 0, longTerm: 0, total: 0 }
    const equity = { total: 0 }

    accounts.forEach(account => {
      const balance = account.balance.toNumber()
      
      switch (account.type) {
        case 'CASH':
        case 'BANK':
          assets.current += balance
          break
        case 'ASSET':
          assets.fixed += balance
          break
        case 'LIABILITY':
          liabilities.current += balance
          break
        case 'EQUITY':
          equity.total += balance
          break
      }
    })

    assets.total = assets.current + assets.fixed
    liabilities.total = liabilities.current + liabilities.longTerm

    return {
      asOfDate,
      assets,
      liabilities,
      equity,
      balanceCheck: assets.total === (liabilities.total + equity.total)
    }
  }

  // 生成损益表
  private async generateIncomeStatement(dateRange: { startDate: Date; endDate: Date }) {
    const transactions = await this.prisma.financialTransaction.findMany({
      where: {
        transactionDate: {
          gte: dateRange.startDate,
          lte: dateRange.endDate
        }
      },
      include: {
        category: true
      }
    })

    let revenue = 0
    let expenses = 0
    const categoryBreakdown: Record<string, number> = {}

    transactions.forEach(transaction => {
      const amount = transaction.amount.toNumber()
      const categoryName = transaction.category?.name || '其他'

      if (transaction.type === FinancialTransactionType.INCOME) {
        revenue += amount
      } else if (transaction.type === FinancialTransactionType.EXPENSE) {
        expenses += amount
      }

      if (!categoryBreakdown[categoryName]) {
        categoryBreakdown[categoryName] = 0
      }
      categoryBreakdown[categoryName] += amount
    })

    const grossProfit = revenue - expenses
    const netIncome = grossProfit // 简化版本，实际应包含税费等

    return {
      period: dateRange,
      revenue,
      expenses,
      grossProfit,
      netIncome,
      categoryBreakdown
    }
  }

  // 生成现金流量表
  private async generateCashFlowStatement(dateRange: { startDate: Date; endDate: Date }) {
    // 这里应该实现现金流量表的生成逻辑
    // 为了简化，返回基本结构
    return {
      period: dateRange,
      operatingActivities: {
        netIncome: 0,
        adjustments: 0,
        netCashFromOperating: 0
      },
      investingActivities: {
        assetPurchases: 0,
        assetSales: 0,
        netCashFromInvesting: 0
      },
      financingActivities: {
        borrowings: 0,
        repayments: 0,
        netCashFromFinancing: 0
      },
      netCashFlow: 0
    }
  }
}

// 财务业务逻辑验证规则
export const financeValidationSchemas = {
  reconciliation: z.object({
    accountId: z.string().min(1, '账户ID不能为空'),
    bankStatementBalance: z.number(),
    reconciliationDate: z.date(),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  budget: z.object({
    categoryId: z.string().min(1, '分类ID不能为空'),
    budgetAmount: z.number().min(0, '预算金额不能为负数'),
    period: z.enum(['monthly', 'quarterly', 'yearly']),
    startDate: z.date(),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  cashFlowAnalysis: z.object({
    accountIds: z.array(z.string()).optional(),
    dateRange: z.object({
      startDate: z.date(),
      endDate: z.date()
    }),
    granularity: z.enum(['daily', 'weekly', 'monthly'])
  }),

  financialReport: z.object({
    reportType: z.enum(['balance_sheet', 'income_statement', 'cash_flow']),
    dateRange: z.object({
      startDate: z.date(),
      endDate: z.date()
    }),
    includeComparisons: z.boolean().optional()
  })
}