import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

export async function GET() {
  try {
    // 查询员工绩效数据
    const employees = await prisma.user.findMany({
      where: {
        role: 'EMPLOYEE'
      },
      include: {
        salaryRecords: {
          take: 1,
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    // 计算绩效数据
    const performanceData = employees.map(employee => {
      const latestSalary = employee.salaryRecords[0]
      
      return {
        id: employee.id,
        name: employee.name || '未知员工',
        position: employee.position || '员工',
        performance: {
          sales: Math.floor(Math.random() * 50000) + 10000, // 模拟销售额
          orders: Math.floor(Math.random() * 50) + 10, // 模拟订单数
          rating: (Math.random() * 2 + 3).toFixed(1), // 3-5分评分
          efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%效率
        },
        salary: latestSalary?.baseSalary || 0,
        bonus: latestSalary?.bonus || 0,
        attendance: Math.floor(Math.random() * 5) + 20, // 20-25天出勤
        lastActive: employee.updatedAt.toISOString()
      }
    })

    return NextResponse.json(performanceData)
  } catch (error) {
    console.error('查询员工绩效失败:', error)
    
    // 返回示例数据
    const samplePerformance = [
      {
        id: '1',
        name: '张小明',
        position: '销售员',
        performance: {
          sales: 35000,
          orders: 28,
          rating: '4.2',
          efficiency: 85
        },
        salary: 5000,
        bonus: 1200,
        attendance: 22,
        lastActive: new Date().toISOString()
      },
      {
        id: '2',
        name: '李小红',
        position: '设计师',
        performance: {
          sales: 28000,
          orders: 15,
          rating: '4.5',
          efficiency: 92
        },
        salary: 6000,
        bonus: 800,
        attendance: 24,
        lastActive: new Date(Date.now() - 3600000).toISOString()
      }
    ]

    return NextResponse.json(samplePerformance)
  }
}
