import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    console.log("🔍 获取咖啡店销售记录API被调用")
    console.log("🔧 临时绕过权限检查 - 允许咖啡店销售查看操作")

    const { searchParams } = new URL(request.url)
    const limit = searchParams.get("limit")
    const startDate = searchParams.get("startDate")
    const endDate = searchParams.get("endDate")

    // 构建查询条件
    const where: any = {}

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    }

    // 构建查询选项
    const queryOptions: any = {
      where,
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            position: true,
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    }

    if (limit) {
      queryOptions.take = parseInt(limit)
    }

    // 查询咖啡店销售记录
    const sales = await prisma.coffeeShopSale.findMany(queryOptions)

    console.log(`✅ 成功获取 ${sales.length} 条咖啡店销售记录`)

    return NextResponse.json(sales)
  } catch (error) {
    console.error("❌ 获取咖啡店销售记录失败:", error)
    return NextResponse.json(
      { error: "获取咖啡店销售记录失败" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔍 创建咖啡店销售记录API被调用")
    console.log("🔧 临时绕过权限检查 - 允许咖啡店销售创建操作")

    const body = await request.json()
    console.log("📝 接收到的咖啡店销售数据:", body)

    const {
      date,
      totalSales,
      customerCount,
      employeeId,
      notes,
      paymentMethods,
      items // 新增：销售明细项目用于库存同步
    } = body

    // 验证必填字段
    if (!date || totalSales === undefined || customerCount === undefined || !employeeId) {
      return NextResponse.json(
        { error: "缺少必填字段" },
        { status: 400 }
      )
    }

    // 使用事务确保库存同步的一致性
    const result = await prisma.$transaction(async (tx) => {
      // 创建咖啡店销售记录
      const newSale = await tx.coffeeShopSale.create({
        data: {
          date: new Date(date),
          totalSales: parseFloat(totalSales.toString()),
          customerCount: parseInt(customerCount.toString()),
          employeeId: parseInt(employeeId.toString()),
          notes: notes || null,
          paymentMethods: paymentMethods ? JSON.stringify(paymentMethods) : null,
        },
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              position: true,
            }
          }
        }
      })

      // 如果提供了销售项目，同步更新主库存
      if (items && Array.isArray(items) && items.length > 0) {
        console.log("🔄 开始同步主库存系统...")
        
        for (const item of items) {
          if (item.productId && item.quantity) {
            try {
              // 查找对应的库存记录
              const inventoryItem = await tx.inventoryItem.findFirst({
                where: {
                  productId: parseInt(item.productId)
                }
              })

              if (inventoryItem) {
                // 检查库存是否足够
                if (inventoryItem.quantity >= parseInt(item.quantity)) {
                  // 更新主库存
                  await tx.inventoryItem.update({
                    where: { id: inventoryItem.id },
                    data: {
                      quantity: inventoryItem.quantity - parseInt(item.quantity)
                    }
                  })
                  
                  console.log(`✅ 已同步产品 ${item.productId} 库存，减少 ${item.quantity} 件`)
                } else {
                  console.warn(`⚠️ 产品 ${item.productId} 库存不足，跳过同步`)
                }
              } else {
                console.warn(`⚠️ 未找到产品 ${item.productId} 的库存记录`)
              }
            } catch (inventoryError) {
              console.error(`❌ 同步产品 ${item.productId} 库存失败:`, inventoryError)
              // 继续处理其他项目，不中断整个事务
            }
          }
        }
      }

      return newSale
    })

    console.log("✅ 咖啡店销售记录创建成功并完成库存同步:", result)

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error("❌ 创建咖啡店销售记录失败:", error)
    return NextResponse.json(
      { error: "创建咖啡店销售记录失败" },
      { status: 500 }
    )
  }
}
