'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

interface EditProductionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pieceWork: any;
  onProductionUpdated: () => void;
}

export function EditProductionDialog({ open, onOpenChange, pieceWork, onProductionUpdated }: EditProductionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [employees, setEmployees] = useState([]);
  const [formData, setFormData] = useState({
    employeeId: '',
    workDate: '',
    workType: '',
    location: '',
    pieceCount: '',
    pieceRate: '',
    qualityGrade: '',
    qualityBonus: '',
    qualityPenalty: '',
    notes: ''
  });

  useEffect(() => {
    if (open && pieceWork) {
      setFormData({
        employeeId: pieceWork.employeeId?.toString() || '',
        workDate: pieceWork.date ? new Date(pieceWork.date).toISOString().split('T')[0] : '',
        workType: pieceWork.workType || '',
        location: pieceWork.location || '',
        pieceCount: pieceWork.pieceCount?.toString() || '',
        pieceRate: pieceWork.pieceRate?.toString() || '',
        qualityGrade: pieceWork.qualityGrade || '',
        qualityBonus: pieceWork.qualityBonus?.toString() || '',
        qualityPenalty: pieceWork.qualityPenalty?.toString() || '',
        notes: pieceWork.notes || ''
      });
      loadEmployees();
    }
  }, [open, pieceWork]);

  const loadEmployees = async () => {
    try {
      const response = await fetch('/api/employees');
      if (response.ok) {
        const data = await response.json();
        setEmployees(data);
      }
    } catch (error) {
      console.error('Error loading employees:', error);
    }
  };

  const workTypeOptions = [
    { value: 'accessory', label: '配饰制作', location: '广州' },
    { value: 'enamelling', label: '点蓝制作', location: '广州' },
    { value: 'polishing', label: '抛光工艺', location: '广西' },
    { value: 'assembly', label: '组装工艺', location: '广西' },
    { value: 'packaging', label: '包装工艺', location: '广州' },
    { value: 'quality_check', label: '质量检验', location: '广西' }
  ];

  const handleWorkTypeChange = (value: string) => {
    const workType = workTypeOptions.find(option => option.value === value);
    setFormData({
      ...formData,
      workType: value,
      location: workType?.location || ''
    });
  };

  const calculateTotalAmount = () => {
    const pieceCount = parseInt(formData.pieceCount) || 0;
    const pieceRate = parseFloat(formData.pieceRate) || 0;
    const qualityBonus = parseFloat(formData.qualityBonus) || 0;
    const qualityPenalty = parseFloat(formData.qualityPenalty) || 0;
    
    return (pieceCount * pieceRate) + qualityBonus - qualityPenalty;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const totalAmount = calculateTotalAmount();
      
      const response = await fetch(`/api/piece-works/${pieceWork.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId: parseInt(formData.employeeId),
          date: formData.workDate,
          workType: formData.workType,
          location: formData.location,
          pieceCount: parseInt(formData.pieceCount),
          pieceRate: parseFloat(formData.pieceRate),
          totalAmount,
          qualityGrade: formData.qualityGrade || null,
          qualityBonus: parseFloat(formData.qualityBonus) || 0,
          qualityPenalty: parseFloat(formData.qualityPenalty) || 0,
          notes: formData.notes || null,
        }),
      });

      if (!response.ok) {
        throw new Error('更新制作工单失败');
      }

      toast({
        title: '更新成功',
        description: '制作工单已成功更新',
      });

      onProductionUpdated();
      onOpenChange(false);
    } catch (error) {
      toast({
        title: '更新失败',
        description: error instanceof Error ? error.message : '更新制作工单时发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!pieceWork) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>编辑制作工单</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="employeeId">员工 *</Label>
              <Select value={formData.employeeId} onValueChange={(value) => setFormData({ ...formData, employeeId: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择员工" />
                </SelectTrigger>
                <SelectContent>
                  {employees.map((employee: any) => (
                    <SelectItem key={employee.id} value={employee.id.toString()}>
                      {employee.name} - {employee.position}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="workDate">工作日期 *</Label>
              <Input
                id="workDate"
                type="date"
                value={formData.workDate}
                onChange={(e) => setFormData({ ...formData, workDate: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="workType">工作类型 *</Label>
              <Select value={formData.workType} onValueChange={handleWorkTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择工作类型" />
                </SelectTrigger>
                <SelectContent>
                  {workTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="location">工作地点</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="工作地点"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pieceCount">完成件数 *</Label>
              <Input
                id="pieceCount"
                type="number"
                value={formData.pieceCount}
                onChange={(e) => setFormData({ ...formData, pieceCount: e.target.value })}
                placeholder="请输入完成件数"
                required
              />
            </div>
            <div>
              <Label htmlFor="pieceRate">计件单价 *</Label>
              <Input
                id="pieceRate"
                type="number"
                step="0.01"
                value={formData.pieceRate}
                onChange={(e) => setFormData({ ...formData, pieceRate: e.target.value })}
                placeholder="请输入计件单价"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="qualityGrade">质量等级</Label>
              <Select value={formData.qualityGrade} onValueChange={(value) => setFormData({ ...formData, qualityGrade: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">A级 (优秀)</SelectItem>
                  <SelectItem value="B">B级 (良好)</SelectItem>
                  <SelectItem value="C">C级 (合格)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="qualityBonus">质量奖金</Label>
              <Input
                id="qualityBonus"
                type="number"
                step="0.01"
                value={formData.qualityBonus}
                onChange={(e) => setFormData({ ...formData, qualityBonus: e.target.value })}
                placeholder="质量奖金"
              />
            </div>
            <div>
              <Label htmlFor="qualityPenalty">质量扣款</Label>
              <Input
                id="qualityPenalty"
                type="number"
                step="0.01"
                value={formData.qualityPenalty}
                onChange={(e) => setFormData({ ...formData, qualityPenalty: e.target.value })}
                placeholder="质量扣款"
              />
            </div>
          </div>

          <div>
            <Label>更新后总金额</Label>
            <div className="text-lg font-bold text-green-600">
              ¥{calculateTotalAmount().toFixed(2)}
            </div>
            <div className="text-sm text-gray-500">
              原金额: ¥{pieceWork.totalAmount?.toFixed(2) || '0.00'}
            </div>
          </div>

          <div>
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="请输入备注信息"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? '更新中...' : '更新工单'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}