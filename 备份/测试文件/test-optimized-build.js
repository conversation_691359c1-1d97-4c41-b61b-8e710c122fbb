/**
 * 测试优化构建配置
 * 验证Next.js优化配置是否正常工作
 */

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

async function testOptimizedConfig() {
  console.log('🔧 测试优化的Next.js配置...')
  
  try {
    // 1. 备份原配置
    const originalConfig = 'next.config.mjs'
    const optimizedConfig = 'next.config.optimized.mjs'
    const backupConfig = 'next.config.mjs.backup'
    
    if (fs.existsSync(originalConfig)) {
      fs.copyFileSync(originalConfig, backupConfig)
      console.log('✅ 已备份原配置文件')
    }
    
    // 2. 使用优化配置
    fs.copyFileSync(optimizedConfig, originalConfig)
    console.log('✅ 已应用优化配置')
    
    // 3. 测试配置语法
    console.log('🔍 验证配置文件语法...')
    
    try {
      // 尝试加载配置文件
      delete require.cache[path.resolve(originalConfig)]
      const config = require(path.resolve(originalConfig))
      console.log('✅ 配置文件语法正确')
      
      // 检查关键优化项
      if (config.default) {
        const nextConfig = config.default
        
        console.log('📋 优化配置检查:')
        
        // 检查实验性功能
        if (nextConfig.experimental) {
          console.log('  ✅ 实验性功能已配置')
          if (nextConfig.experimental.optimizeCss) {
            console.log('    ✅ CSS优化已启用')
          }
          if (nextConfig.experimental.turbo) {
            console.log('    ✅ Turbo内存限制已设置')
          }
        }
        
        // 检查图片优化
        if (nextConfig.images) {
          console.log('  ✅ 图片优化已配置')
          console.log(`    📊 设备尺寸数量: ${nextConfig.images.deviceSizes?.length || 0}`)
          console.log(`    📊 图片尺寸数量: ${nextConfig.images.imageSizes?.length || 0}`)
        }
        
        // 检查输出配置
        if (nextConfig.output === 'standalone') {
          console.log('  ✅ 独立输出模式已启用')
        }
        
        // 检查压缩
        if (nextConfig.compress) {
          console.log('  ✅ 压缩已启用')
        }
        
        // 检查webpack配置
        if (nextConfig.webpack) {
          console.log('  ✅ Webpack优化已配置')
        }
        
      }
      
    } catch (configError) {
      console.error('❌ 配置文件语法错误:', configError.message)
      throw configError
    }
    
    // 4. 恢复原配置
    if (fs.existsSync(backupConfig)) {
      fs.copyFileSync(backupConfig, originalConfig)
      fs.unlinkSync(backupConfig)
      console.log('✅ 已恢复原配置文件')
    }
    
    return true
    
  } catch (error) {
    console.error('❌ 优化配置测试失败:', error.message)
    
    // 确保恢复原配置
    const backupConfig = 'next.config.mjs.backup'
    if (fs.existsSync(backupConfig)) {
      fs.copyFileSync(backupConfig, 'next.config.mjs')
      fs.unlinkSync(backupConfig)
      console.log('✅ 已恢复原配置文件')
    }
    
    return false
  }
}

async function testMemorySettings() {
  console.log('🧠 测试内存设置...')
  
  // 检查Node.js内存限制
  const nodeOptions = process.env.NODE_OPTIONS || ''
  console.log(`📊 当前NODE_OPTIONS: ${nodeOptions || '(未设置)'}`)
  
  // 模拟4GB服务器的内存设置
  const recommendedOptions = '--max-old-space-size=1024 --expose-gc'
  console.log(`💡 推荐设置: ${recommendedOptions}`)
  
  // 检查当前内存使用
  const memUsage = process.memoryUsage()
  console.log('📊 当前内存使用:')
  console.log(`  RSS: ${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`)
  console.log(`  Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`)
  console.log(`  Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`)
  console.log(`  External: ${(memUsage.external / 1024 / 1024).toFixed(2)}MB`)
  
  // 检查垃圾回收是否可用
  if (global.gc) {
    console.log('✅ 垃圾回收功能可用')
    
    // 测试垃圾回收
    const beforeGC = process.memoryUsage().heapUsed
    global.gc()
    const afterGC = process.memoryUsage().heapUsed
    const freed = (beforeGC - afterGC) / 1024 / 1024
    
    console.log(`🧹 垃圾回收释放: ${freed.toFixed(2)}MB`)
  } else {
    console.log('⚠️  垃圾回收功能不可用 (需要 --expose-gc)')
  }
  
  return true
}

async function testDatabaseOptimization() {
  console.log('🗄️  测试数据库优化配置...')
  
  try {
    // 检查PostgreSQL配置文件
    const pgConfigPath = 'postgresql-4gb.conf'
    if (fs.existsSync(pgConfigPath)) {
      const pgConfig = fs.readFileSync(pgConfigPath, 'utf8')
      
      console.log('✅ PostgreSQL配置文件存在')
      
      // 检查关键配置项
      const keySettings = [
        'shared_buffers = 128MB',
        'effective_cache_size = 1GB',
        'work_mem = 4MB',
        'max_connections = 50'
      ]
      
      keySettings.forEach(setting => {
        if (pgConfig.includes(setting)) {
          console.log(`  ✅ ${setting}`)
        } else {
          console.log(`  ⚠️  ${setting} - 未找到`)
        }
      })
      
    } else {
      console.log('❌ PostgreSQL配置文件不存在')
      return false
    }
    
    // 测试Prisma连接
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient({
      log: ['error'],
      errorFormat: 'minimal'
    })
    
    try {
      await prisma.$queryRaw`SELECT version() as version`
      console.log('✅ 数据库连接测试成功')
      await prisma.$disconnect()
    } catch (dbError) {
      console.log('⚠️  数据库连接测试失败 (这是正常的，如果数据库未运行)')
    }
    
    return true
    
  } catch (error) {
    console.error('❌ 数据库优化测试失败:', error.message)
    return false
  }
}

async function runOptimizedBuildTest() {
  console.log('🚀 开始优化构建测试')
  console.log('=' .repeat(60))
  
  const tests = [
    { name: '优化配置测试', fn: testOptimizedConfig },
    { name: '内存设置测试', fn: testMemorySettings },
    { name: '数据库优化测试', fn: testDatabaseOptimization }
  ]
  
  const results = []
  
  for (const test of tests) {
    console.log(`\n🔍 ${test.name}`)
    console.log('-'.repeat(40))
    
    try {
      const result = await test.fn()
      results.push({ name: test.name, success: result })
      
      if (result) {
        console.log(`✅ ${test.name} 通过`)
      } else {
        console.log(`❌ ${test.name} 失败`)
      }
    } catch (error) {
      console.error(`❌ ${test.name} 异常:`, error.message)
      results.push({ name: test.name, success: false })
    }
  }
  
  // 输出总结
  console.log('\n📋 测试结果总结')
  console.log('=' .repeat(60))
  
  const passed = results.filter(r => r.success).length
  const total = results.length
  
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败'
    console.log(`${status} ${result.name}`)
  })
  
  console.log(`\n📊 通过率: ${passed}/${total} (${((passed/total)*100).toFixed(1)}%)`)
  
  if (passed === total) {
    console.log('\n🎉 所有优化配置测试通过！')
    console.log('💡 系统已准备好在4GB内存服务器上运行')
  } else {
    console.log('\n⚠️  部分测试未通过，但核心功能应该可用')
  }
  
  // 显示下一步建议
  console.log('\n📝 下一步建议:')
  console.log('1. 使用以下命令启动优化版本:')
  console.log('   NODE_OPTIONS="--max-old-space-size=1024 --expose-gc" npm run dev')
  console.log('')
  console.log('2. 或者使用Docker部署:')
  console.log('   ./scripts/start-4gb-server.sh')
  console.log('')
  console.log('3. 监控内存使用:')
  console.log('   docker stats (如果使用Docker)')
  console.log('   或者在应用中查看内存监控日志')
  
  return passed === total
}

// 运行测试
if (require.main === module) {
  runOptimizedBuildTest()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('测试运行失败:', error)
      process.exit(1)
    })
}

module.exports = { runOptimizedBuildTest }
