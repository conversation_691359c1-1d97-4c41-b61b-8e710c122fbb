#!/bin/bash

# 数据迁移脚本
# 从现有数据库迁移数据到独立Docker环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SOURCE_DB_URL="postgresql://macmini@localhost:5432/linghua_enamel_gallery"
TARGET_DB_URL="postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated"
BACKUP_DIR="./backups/migration-$(date +%Y%m%d_%H%M%S)"
TEMP_DUMP_FILE="$BACKUP_DIR/source_database_dump.sql"

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    # 检查pg_dump
    if ! command -v pg_dump &> /dev/null; then
        log_error "pg_dump 未找到，请安装 PostgreSQL 客户端工具"
        exit 1
    fi
    
    # 检查psql
    if ! command -v psql &> /dev/null; then
        log_error "psql 未找到，请安装 PostgreSQL 客户端工具"
        exit 1
    fi
    
    log_success "依赖工具检查完成"
}

# 检查源数据库连接
check_source_database() {
    log_info "检查源数据库连接..."
    
    if ! psql "$SOURCE_DB_URL" -c "SELECT 1;" >/dev/null 2>&1; then
        log_error "无法连接到源数据库: $SOURCE_DB_URL"
        log_info "请确保源数据库正在运行且连接信息正确"
        exit 1
    fi
    
    log_success "源数据库连接正常"
}

# 检查目标数据库连接
check_target_database() {
    log_info "检查目标数据库连接..."
    
    # 等待目标数据库就绪
    timeout=60
    while ! psql "$TARGET_DB_URL" -c "SELECT 1;" >/dev/null 2>&1; do
        log_info "等待目标数据库就绪..."
        sleep 5
        timeout=$((timeout - 5))
        if [[ $timeout -le 0 ]]; then
            log_error "目标数据库连接超时"
            log_info "请确保独立Docker环境已启动: ./scripts/start-isolated.sh"
            exit 1
        fi
    done
    
    log_success "目标数据库连接正常"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    
    mkdir -p "$BACKUP_DIR" || {
        log_error "无法创建备份目录: $BACKUP_DIR"
        exit 1
    }
    
    log_success "备份目录创建完成: $BACKUP_DIR"
}

# 导出源数据库
export_source_database() {
    log_info "导出源数据库数据..."
    
    # 导出数据（不包含模式，因为目标数据库已有模式）
    pg_dump "$SOURCE_DB_URL" \
        --data-only \
        --no-owner \
        --no-privileges \
        --disable-triggers \
        --file="$TEMP_DUMP_FILE" || {
        log_error "数据库导出失败"
        exit 1
    }
    
    log_success "数据库导出完成: $TEMP_DUMP_FILE"
}

# 备份目标数据库（可选）
backup_target_database() {
    echo
    read -p "是否备份目标数据库现有数据？(推荐) (Y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_info "备份目标数据库..."
        
        local target_backup_file="$BACKUP_DIR/target_database_backup.sql"
        pg_dump "$TARGET_DB_URL" \
            --data-only \
            --no-owner \
            --no-privileges \
            --file="$target_backup_file" || {
            log_warning "目标数据库备份失败，但继续执行迁移"
        }
        
        if [[ -f "$target_backup_file" ]]; then
            log_success "目标数据库备份完成: $target_backup_file"
        fi
    else
        log_info "跳过目标数据库备份"
    fi
}

# 清空目标数据库数据
clear_target_database() {
    echo
    read -p "是否清空目标数据库现有数据？(Y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_warning "清空目标数据库数据..."
        
        # 获取所有表名并清空
        local tables=$(psql "$TARGET_DB_URL" -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';" | grep -v '^$')
        
        if [[ -n "$tables" ]]; then
            # 禁用外键约束
            psql "$TARGET_DB_URL" -c "SET session_replication_role = replica;" >/dev/null 2>&1
            
            # 清空所有表
            for table in $tables; do
                table=$(echo "$table" | xargs)  # 去除空格
                if [[ -n "$table" ]]; then
                    log_info "清空表: $table"
                    psql "$TARGET_DB_URL" -c "TRUNCATE TABLE \"$table\" CASCADE;" >/dev/null 2>&1 || {
                        log_warning "清空表 $table 失败，继续..."
                    }
                fi
            done
            
            # 重新启用外键约束
            psql "$TARGET_DB_URL" -c "SET session_replication_role = DEFAULT;" >/dev/null 2>&1
            
            log_success "目标数据库数据清空完成"
        else
            log_info "目标数据库无数据，跳过清空"
        fi
    else
        log_info "保留目标数据库现有数据"
    fi
}

# 导入数据到目标数据库
import_to_target_database() {
    log_info "导入数据到目标数据库..."
    
    # 禁用触发器以提高导入速度
    psql "$TARGET_DB_URL" -c "SET session_replication_role = replica;" >/dev/null 2>&1
    
    # 导入数据
    psql "$TARGET_DB_URL" -f "$TEMP_DUMP_FILE" || {
        log_error "数据导入失败"
        # 重新启用触发器
        psql "$TARGET_DB_URL" -c "SET session_replication_role = DEFAULT;" >/dev/null 2>&1
        exit 1
    }
    
    # 重新启用触发器
    psql "$TARGET_DB_URL" -c "SET session_replication_role = DEFAULT;" >/dev/null 2>&1
    
    log_success "数据导入完成"
}

# 验证数据迁移
verify_migration() {
    log_info "验证数据迁移..."
    
    # 获取源数据库表的记录数
    log_info "统计源数据库记录数..."
    local source_counts=$(psql "$SOURCE_DB_URL" -t -c "
        SELECT schemaname,tablename,n_tup_ins-n_tup_del as rowcount 
        FROM pg_stat_user_tables 
        WHERE schemaname='public' 
        ORDER BY tablename;
    " 2>/dev/null || echo "")
    
    # 获取目标数据库表的记录数
    log_info "统计目标数据库记录数..."
    local target_counts=$(psql "$TARGET_DB_URL" -t -c "
        SELECT schemaname,tablename,n_tup_ins-n_tup_del as rowcount 
        FROM pg_stat_user_tables 
        WHERE schemaname='public' 
        ORDER BY tablename;
    " 2>/dev/null || echo "")
    
    # 保存统计信息
    echo "源数据库记录统计:" > "$BACKUP_DIR/migration_verification.txt"
    echo "$source_counts" >> "$BACKUP_DIR/migration_verification.txt"
    echo "" >> "$BACKUP_DIR/migration_verification.txt"
    echo "目标数据库记录统计:" >> "$BACKUP_DIR/migration_verification.txt"
    echo "$target_counts" >> "$BACKUP_DIR/migration_verification.txt"
    
    log_success "数据验证完成，详细信息保存在: $BACKUP_DIR/migration_verification.txt"
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    if [[ -f "$TEMP_DUMP_FILE" ]]; then
        rm -f "$TEMP_DUMP_FILE" || {
            log_warning "清理临时文件失败: $TEMP_DUMP_FILE"
        }
    fi
    
    log_success "临时文件清理完成"
}

# 显示迁移结果
show_migration_result() {
    echo
    log_success "🎉 数据迁移完成！"
    echo
    echo "📋 迁移信息："
    echo "  📂 备份目录: $BACKUP_DIR"
    echo "  📄 验证报告: $BACKUP_DIR/migration_verification.txt"
    echo
    echo "🔗 目标数据库连接信息："
    echo "  🌐 应用访问: http://localhost:3001"
    echo "  🗄️  数据库: localhost:5434"
    echo "  🛠️  管理工具: http://localhost:8081"
    echo
    echo "📝 后续操作："
    echo "  1. 访问 http://localhost:3001 验证应用功能"
    echo "  2. 使用 http://localhost:8081 管理数据库"
    echo "  3. 检查 $BACKUP_DIR/migration_verification.txt 验证数据完整性"
    echo
}

# 主函数
main() {
    echo "🔄 数据迁移到独立Docker环境"
    echo "============================="
    
    check_dependencies
    check_source_database
    check_target_database
    create_backup_dir
    export_source_database
    backup_target_database
    clear_target_database
    import_to_target_database
    verify_migration
    cleanup_temp_files
    show_migration_result
    
    log_success "迁移流程完成！"
}

# 执行主函数
main "$@"
