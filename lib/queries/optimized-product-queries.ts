/**
 * 优化的产品查询函数
 * 使用缓存、批量查询和索引优化来提升性能
 */

import prisma from "@/lib/db"
import { databaseCache, apiCache } from "@/lib/cache/cache-instances"

export interface ProductQueryOptions {
  page?: number
  limit?: number
  categoryId?: number | null
  searchQuery?: string
  sortBy?: 'name' | 'price' | 'createdAt' | 'inventory'
  sortOrder?: 'asc' | 'desc'
  includeInactive?: boolean
  inStockOnly?: boolean
}

export interface ProductWithRelations {
  id: number
  name: string
  price: number
  commissionRate: number
  type: string
  createdAt: Date
  updatedAt: Date
  description: string | null
  imageUrl: string | null
  imageUrls: string[]
  barcode: string | null
  categoryId: number | null
  inventory: number | null
  material: string | null
  unit: string | null
  productCategory: {
    id: number
    name: string
  } | null
  productTags: {
    tag: {
      id: number
      name: string
    }
  }[]
  _count: {
    orderItems: number
  }
}

/**
 * 获取产品列表（带分页和过滤）
 */
export async function getProductsWithPagination(options: ProductQueryOptions = {}) {
  const {
    page = 1,
    limit = 20,
    categoryId,
    searchQuery,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    includeInactive = false,
    inStockOnly = false
  } = options

  // 生成缓存键
  const cacheKey = `products-paginated-${JSON.stringify(options)}`
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  const skip = (page - 1) * limit

  // 构建where条件
  const where: any = {}
  
  if (!includeInactive) {
    where.type = {
      notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
    }
  }

  if (categoryId !== undefined && categoryId !== null) {
    where.categoryId = categoryId
  }

  if (searchQuery) {
    where.OR = [
      { name: { contains: searchQuery, mode: 'insensitive' } },
      { description: { contains: searchQuery, mode: 'insensitive' } },
      { barcode: { contains: searchQuery, mode: 'insensitive' } }
    ]
  }

  if (inStockOnly) {
    where.OR = [
      { inventory: { gt: 0 } },
      { inventory: null }
    ]
  }

  // 构建orderBy
  const orderBy: any = {}
  orderBy[sortBy] = sortOrder

  try {
    // 并行执行查询和计数
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        include: {
          productCategory: {
            select: { id: true, name: true }
          },
          productTags: {
            include: {
              tag: {
                select: { id: true, name: true }
              }
            }
          },
          _count: {
            select: { orderItems: true }
          }
        },
        orderBy
      }),
      prisma.product.count({ where })
    ])

    const result = {
      products: products.map(product => ({
        ...product,
        categoryName: product.productCategory?.name || null,
        tags: product.productTags?.map(pt => pt.tag) || [],
        tagIds: product.productTags?.map(pt => pt.tag.id) || [],
        orderCount: product._count.orderItems
      })),
      total,
      pages: Math.ceil(total / limit),
      currentPage: page,
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }

    // 缓存结果
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in getProductsWithPagination:', error)
    throw error
  }
}

/**
 * 批量获取产品（避免N+1查询）
 */
export async function batchGetProductsByIds(ids: number[]) {
  if (ids.length === 0) return []

  const cacheKey = `products-batch-${ids.sort().join(',')}`
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const products = await prisma.product.findMany({
      where: { id: { in: ids } },
      include: {
        productCategory: {
          select: { id: true, name: true }
        },
        productTags: {
          include: {
            tag: {
              select: { id: true, name: true }
            }
          }
        }
      }
    })

    const result = products.map(product => ({
      ...product,
      categoryName: product.productCategory?.name || null,
      tags: product.productTags?.map(pt => pt.tag) || [],
      tagIds: product.productTags?.map(pt => pt.tag.id) || []
    }))

    // 缓存结果
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in batchGetProductsByIds:', error)
    throw error
  }
}

/**
 * 获取产品统计信息
 */
export async function getProductStats() {
  const cacheKey = 'product-stats'
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const [
      totalProducts,
      activeProducts,
      categorizedProducts,
      lowStockProducts,
      totalValue
    ] = await Promise.all([
      prisma.product.count(),
      prisma.product.count({
        where: {
          type: {
            notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
          }
        }
      }),
      prisma.product.count({
        where: {
          categoryId: { not: null },
          type: {
            notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
          }
        }
      }),
      prisma.product.count({
        where: {
          inventory: { lte: 10, gt: 0 },
          type: {
            notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
          }
        }
      }),
      prisma.product.aggregate({
        where: {
          type: {
            notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
          }
        },
        _sum: { price: true }
      })
    ])

    const result = {
      totalProducts,
      activeProducts,
      categorizedProducts,
      lowStockProducts,
      totalValue: totalValue._sum.price || 0,
      categorizationRate: activeProducts > 0 ? (categorizedProducts / activeProducts) * 100 : 0
    }

    // 缓存结果（较短的TTL，因为统计数据变化较频繁）
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in getProductStats:', error)
    throw error
  }
}

/**
 * 获取热门产品（基于订单数量）
 */
export async function getPopularProducts(limit = 10) {
  const cacheKey = `popular-products-${limit}`
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const products = await prisma.product.findMany({
      where: {
        type: {
          notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
        }
      },
      include: {
        productCategory: {
          select: { id: true, name: true }
        },
        _count: {
          select: { orderItems: true }
        }
      },
      orderBy: {
        orderItems: {
          _count: 'desc'
        }
      },
      take: limit
    })

    const result = products.map(product => ({
      ...product,
      categoryName: product.productCategory?.name || null,
      orderCount: product._count.orderItems
    }))

    // 缓存结果
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in getPopularProducts:', error)
    throw error
  }
}

/**
 * 搜索产品（使用全文搜索）
 */
export async function searchProducts(query: string, limit = 20) {
  if (!query.trim()) return []

  const cacheKey = `product-search-${query}-${limit}`
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    // 使用PostgreSQL的全文搜索
    const products = await prisma.$queryRaw`
      SELECT p.*, pc.name as category_name,
             ts_rank(to_tsvector('simple', p.name || ' ' || COALESCE(p.description, '')), plainto_tsquery('simple', ${query})) as rank
      FROM "Product" p
      LEFT JOIN "ProductCategory" pc ON p.category_id = pc.id
      WHERE (
        to_tsvector('simple', p.name || ' ' || COALESCE(p.description, '')) @@ plainto_tsquery('simple', ${query})
        OR p.name ILIKE ${`%${query}%`}
        OR p.barcode ILIKE ${`%${query}%`}
      )
      AND p.type NOT IN ('category_placeholder', 'unit_placeholder', 'material_placeholder')
      ORDER BY rank DESC, p.name ASC
      LIMIT ${limit}
    ` as any[]

    const result = products.map(product => ({
      ...product,
      categoryName: product.category_name
    }))

    // 缓存结果
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in searchProducts:', error)
    // 降级到普通搜索
    return await prisma.product.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { barcode: { contains: query, mode: 'insensitive' } }
        ],
        type: {
          notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
        }
      },
      include: {
        productCategory: {
          select: { id: true, name: true }
        }
      },
      take: limit,
      orderBy: { name: 'asc' }
    })
  }
}

/**
 * 获取分类下的产品数量
 */
export async function getProductCountsByCategory() {
  const cacheKey = 'product-counts-by-category'
  
  // 检查缓存
  const cached = databaseCache.get(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const counts = await prisma.productCategory.findMany({
      include: {
        _count: {
          select: {
            products: {
              where: {
                type: {
                  notIn: ["category_placeholder", "unit_placeholder", "material_placeholder"]
                }
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    const result = counts.map(category => ({
      id: category.id,
      name: category.name,
      productCount: category._count.products
    }))

    // 缓存结果
    databaseCache.set(cacheKey, result)

    return result
  } catch (error) {
    console.error('Error in getProductCountsByCategory:', error)
    throw error
  }
}

/**
 * 清除产品相关缓存
 */
export function clearProductCaches() {
  // 清除所有产品相关的缓存
  const cacheKeys = databaseCache.keys()
  const productCacheKeys = cacheKeys.filter(key => 
    key.includes('product') || 
    key.includes('popular') || 
    key.includes('stats') ||
    key.includes('category')
  )
  
  productCacheKeys.forEach(key => {
    databaseCache.delete(key)
  })

  // 也清除API缓存
  apiCache.delete('products-list')
}