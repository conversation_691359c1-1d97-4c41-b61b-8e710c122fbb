import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import { GET as checkPermissions } from '@/app/api/auth/check-permissions/route'
import { GET as getPermissions } from '@/app/api/auth/permissions/route'
import { 
  testPrisma, 
  createMockRequest, 
  mockSession, 
  setupTestDatabase,
  cleanupTestDatabase,
  testUsers
} from './api-test-utils'

// Mock NextAuth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

describe('Authentication API', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
    await testPrisma.$disconnect()
  })

  beforeEach(async () => {
    // 清理权限相关数据
    await testPrisma.userRole.deleteMany()
    await testPrisma.rolePermission.deleteMany()
    await testPrisma.role.deleteMany()
    await testPrisma.permission.deleteMany()

    // 创建测试权限和角色
    await testPrisma.permission.createMany({
      data: [
        { id: 'products.read', name: '查看产品', category: 'products' },
        { id: 'products.write', name: '编辑产品', category: 'products' },
        { id: 'products.delete', name: '删除产品', category: 'products' },
        { id: 'employees.read', name: '查看员工', category: 'employees' },
        { id: 'employees.write', name: '编辑员工', category: 'employees' },
        { id: 'system.admin', name: '系统管理', category: 'system' }
      ]
    })

    await testPrisma.role.createMany({
      data: [
        { id: 'admin', name: '管理员', description: '系统管理员' },
        { id: 'manager', name: '经理', description: '部门经理' },
        { id: 'employee', name: '员工', description: '普通员工' }
      ]
    })

    // 为管理员角色分配所有权限
    await testPrisma.rolePermission.createMany({
      data: [
        { roleId: 'admin', permissionId: 'products.read' },
        { roleId: 'admin', permissionId: 'products.write' },
        { roleId: 'admin', permissionId: 'products.delete' },
        { roleId: 'admin', permissionId: 'employees.read' },
        { roleId: 'admin', permissionId: 'employees.write' },
        { roleId: 'admin', permissionId: 'system.admin' }
      ]
    })

    // 为经理角色分配部分权限
    await testPrisma.rolePermission.createMany({
      data: [
        { roleId: 'manager', permissionId: 'products.read' },
        { roleId: 'manager', permissionId: 'products.write' },
        { roleId: 'manager', permissionId: 'employees.read' }
      ]
    })

    // 为员工角色分配基础权限
    await testPrisma.rolePermission.createMany({
      data: [
        { roleId: 'employee', permissionId: 'products.read' }
      ]
    })

    // 为测试用户分配角色
    await testPrisma.userRole.create({
      data: {
        userId: testUsers.admin.id,
        roleId: 'admin'
      }
    })

    await testPrisma.userRole.create({
      data: {
        userId: testUsers.employee.id,
        roleId: 'employee'
      }
    })
  })

  describe('GET /api/auth/check-permissions', () => {
    it('管理员应该拥有所有权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/auth/check-permissions?permission=products.delete')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.hasPermission).toBe(true)
      expect(data.permission).toBe('products.delete')
    })

    it('普通员工不应该拥有删除权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/check-permissions?permission=products.delete')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.hasPermission).toBe(false)
      expect(data.permission).toBe('products.delete')
    })

    it('普通员工应该拥有查看权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/check-permissions?permission=products.read')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.hasPermission).toBe(true)
      expect(data.permission).toBe('products.read')
    })

    it('未登录用户应该被拒绝', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue(null)

      const request = createMockRequest('GET', '/api/auth/check-permissions?permission=products.read')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(401)
    })

    it('应该处理批量权限检查', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const permissions = ['products.read', 'products.write', 'products.delete']
      const request = createMockRequest('GET', `/api/auth/check-permissions?permissions=${permissions.join(',')}`)
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.permissions).toBeDefined()
      expect(data.permissions['products.read']).toBe(true)
      expect(data.permissions['products.write']).toBe(true)
      expect(data.permissions['products.delete']).toBe(true)
    })
  })

  describe('GET /api/auth/permissions', () => {
    it('应该返回用户的所有权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data.permissions)).toBe(true)
      expect(data.permissions.length).toBeGreaterThan(0)
      
      // 管理员应该拥有所有权限
      const permissionIds = data.permissions.map((p: any) => p.id)
      expect(permissionIds).toContain('products.read')
      expect(permissionIds).toContain('products.write')
      expect(permissionIds).toContain('products.delete')
      expect(permissionIds).toContain('system.admin')
    })

    it('应该返回员工的有限权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data.permissions)).toBe(true)
      
      // 普通员工只应该有基础权限
      const permissionIds = data.permissions.map((p: any) => p.id)
      expect(permissionIds).toContain('products.read')
      expect(permissionIds).not.toContain('products.delete')
      expect(permissionIds).not.toContain('system.admin')
    })

    it('应该按分类返回权限', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/auth/permissions?groupBy=category')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.permissions).toBeDefined()
      expect(data.permissions.products).toBeDefined()
      expect(data.permissions.employees).toBeDefined()
      expect(data.permissions.system).toBeDefined()
    })

    it('未登录用户应该被拒绝', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue(null)

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(401)
    })
  })

  describe('权限继承和角色测试', () => {
    it('应该正确处理多角色用户', async () => {
      // 为测试用户添加额外角色
      await testPrisma.userRole.create({
        data: {
          userId: testUsers.employee.id,
          roleId: 'manager'
        }
      })

      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      
      // 现在应该拥有员工和经理的所有权限
      const permissionIds = data.permissions.map((p: any) => p.id)
      expect(permissionIds).toContain('products.read') // 员工权限
      expect(permissionIds).toContain('products.write') // 经理权限
      expect(permissionIds).toContain('employees.read') // 经理权限
    })

    it('应该处理角色被删除的情况', async () => {
      // 删除员工角色
      await testPrisma.userRole.deleteMany({
        where: { userId: testUsers.employee.id }
      })

      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      
      // 没有角色的用户应该没有权限
      expect(data.permissions).toEqual([])
    })

    it('应该处理权限被撤销的情况', async () => {
      // 撤销员工角色的所有权限
      await testPrisma.rolePermission.deleteMany({
        where: { roleId: 'employee' }
      })

      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.employee
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      
      // 角色没有权限的用户应该没有权限
      expect(data.permissions).toEqual([])
    })
  })

  describe('权限缓存测试', () => {
    it('权限检查结果应该是一致的', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 连续进行相同的权限检查
      const request1 = createMockRequest('GET', '/api/auth/check-permissions?permission=products.write')
      const response1 = await checkPermissions(request1)
      
      const request2 = createMockRequest('GET', '/api/auth/check-permissions?permission=products.write')
      const response2 = await checkPermissions(request2)
      
      expect(response1.status).toBe(200)
      expect(response2.status).toBe(200)
      
      const data1 = await response1.json()
      const data2 = await response2.json()
      
      expect(data1.hasPermission).toBe(data2.hasPermission)
    })
  })

  describe('边界情况测试', () => {
    it('应该处理不存在的权限检查', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/auth/check-permissions?permission=nonexistent.permission')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.hasPermission).toBe(false)
    })

    it('应该处理空权限参数', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/auth/check-permissions')
      const response = await checkPermissions(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('permission')
    })

    it('应该处理无效的用户ID', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: {
          id: 'invalid-user-id',
          email: '<EMAIL>',
          name: 'Invalid User'
        }
      })

      const request = createMockRequest('GET', '/api/auth/permissions')
      const response = await getPermissions(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      // 无效用户应该没有权限
      expect(data.permissions).toEqual([])
    })
  })
})