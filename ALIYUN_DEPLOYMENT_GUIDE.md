# 阿里云部署指南

## 概述

本指南详细说明如何将独立Docker环境部署到阿里云服务器上，确保在云端也能保持完全的环境隔离。

## 🏗️ 阿里云服务选择

### 推荐配置

#### 1. ECS云服务器
- **规格**: ecs.c6.large (2核4GB) 或更高
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8
- **存储**: 系统盘40GB + 数据盘100GB
- **网络**: 专有网络VPC

#### 2. RDS数据库（可选）
- **引擎**: PostgreSQL 15
- **规格**: pg.n2.small.1 (1核2GB) 或更高
- **存储**: 20GB SSD

#### 3. Redis实例（可选）
- **版本**: Redis 7.0
- **规格**: redis.master.micro.default (256MB)

## 🚀 部署方案

### 方案一：完全容器化部署（推荐）

使用Docker Compose在ECS上部署所有服务，保持环境一致性。

#### 优势
- ✅ 环境一致性最佳
- ✅ 部署简单快速
- ✅ 易于维护和扩展
- ✅ 成本相对较低

#### 部署步骤

##### 1. 准备ECS服务器

```bash
# 连接到ECS服务器
ssh root@your-ecs-ip

# 更新系统
apt update && apt upgrade -y  # Ubuntu
# 或
yum update -y  # CentOS

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
systemctl start docker
systemctl enable docker
```

##### 2. 上传项目文件

由于阿里云服务器可能无法直接访问GitHub，我们使用本地上传的方式：

```bash
# 方法一：直接上传项目文件（推荐）
# 在本地打包项目（排除node_modules等）
tar --exclude='node_modules' --exclude='.git' --exclude='*.log' --exclude='uploads' -czf linghua-project.tar.gz .

# 上传到服务器
scp linghua-project.tar.gz root@your-ecs-ip:/opt/

# 在服务器上解压
cd /opt
tar -xzf linghua-project.tar.gz
mv 0607linghua-enamel-gallery linghua-app
cd linghua-app

# 方法二：使用代码托管平台（如果GitHub不可用）
# 可以使用Gitee、阿里云Code等国内平台
# 1. 将代码推送到Gitee
# 2. 在服务器上从Gitee克隆

# 方法三：配置GitHub访问（解决连接问题）
# 参见下面的"GitHub连接问题解决方案"部分
```

##### 3. 修改配置文件

创建生产环境配置：

```bash
# 复制并修改环境变量文件
cp .env.isolated .env.production
```

编辑 `.env.production`：

```env
# 生产环境配置
DATABASE_URL="*************************************************************************/linghua_enamel_gallery_isolated?schema=public"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-super-secure-secret-key-for-production"
AUTH_SECRET="your-super-secure-secret-key-for-production"
AUTH_TRUST_HOST="true"
REDIS_URL="redis://linghua-isolated-redis:6379"
NODE_ENV="production"
ENVIRONMENT="production"

# 邮件配置（使用阿里云邮件推送）
EMAIL_SERVER_HOST="smtpdm.aliyun.com"
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-email-password"
EMAIL_FROM="<EMAIL>"

# 安全配置
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="24h"
SESSION_TIMEOUT="86400"
```

##### 4. 修改Docker Compose配置

创建生产环境Docker配置：

```bash
cp docker-compose.isolated.yml docker-compose.production.yml
```

编辑关键配置：

```yaml
# 修改应用服务配置
linghua-isolated-app:
  image: node:18-alpine
  container_name: linghua-production-app
  restart: always
  working_dir: /app
  command: sh -c "npm install --production && npm run build && npm start"
  ports:
    - "3000:3000"  # 生产环境使用标准端口
  environment:
    - NODE_ENV=production
    # 其他环境变量...
  
# 修改数据库配置
linghua-isolated-postgres:
  container_name: linghua-production-postgres
  restart: always
  environment:
    POSTGRES_PASSWORD: your_secure_password
  volumes:
    - /opt/data/postgres:/var/lib/postgresql/data
    
# 修改Redis配置
linghua-isolated-redis:
  container_name: linghua-production-redis
  restart: always
  volumes:
    - /opt/data/redis:/data
```

##### 5. 配置防火墙和安全组

```bash
# 配置阿里云安全组规则
# 开放端口：22(SSH), 80(HTTP), 443(HTTPS), 8081(Adminer-仅内网)

# 配置服务器防火墙
ufw allow 22
ufw allow 80
ufw allow 443
ufw allow from 10.0.0.0/8 to any port 8081  # 仅内网访问Adminer
ufw enable
```

##### 6. 启动服务

```bash
# 创建数据目录
mkdir -p /opt/data/{postgres,redis}

# 启动服务
docker-compose -f docker-compose.production.yml --env-file .env.production up -d

# 检查服务状态
docker-compose -f docker-compose.production.yml ps

# 查看日志
docker-compose -f docker-compose.production.yml logs -f
```

##### 7. 配置域名和SSL

```bash
# 安装Nginx
apt install nginx -y

# 配置Nginx反向代理
cat > /etc/nginx/sites-available/linghua-app << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# 启用站点
ln -s /etc/nginx/sites-available/linghua-app /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

# 安装SSL证书（使用Let's Encrypt）
apt install certbot python3-certbot-nginx -y
certbot --nginx -d your-domain.com
```

### 方案二：混合云部署

使用阿里云托管服务 + ECS应用服务器。

#### 架构
- **ECS**: 运行Next.js应用
- **RDS**: PostgreSQL数据库
- **Redis**: 阿里云Redis实例
- **SLB**: 负载均衡器
- **CDN**: 静态资源加速

#### 配置示例

```env
# 使用阿里云RDS
DATABASE_URL="postgresql://username:<EMAIL>:5432/linghua_enamel_gallery"

# 使用阿里云Redis
REDIS_URL="redis://r-xxxxxxxx.redis.rds.aliyuncs.com:6379"
```

## 🔧 运维管理

### 自动化部署脚本

创建 `scripts/deploy-aliyun.sh`：

```bash
#!/bin/bash
# 阿里云自动化部署脚本

set -e

SERVER_IP="your-ecs-ip"
SERVER_USER="root"
PROJECT_DIR="/opt/linghua-app"

echo "🚀 开始部署到阿里云..."

# 1. 打包项目
echo "📦 打包项目文件..."
tar --exclude='node_modules' --exclude='.git' --exclude='*.log' -czf linghua-deploy.tar.gz .

# 2. 上传到服务器
echo "📤 上传文件到服务器..."
scp linghua-deploy.tar.gz $SERVER_USER@$SERVER_IP:/tmp/

# 3. 在服务器上部署
echo "🔄 在服务器上执行部署..."
ssh $SERVER_USER@$SERVER_IP << 'EOF'
cd /tmp
tar -xzf linghua-deploy.tar.gz
rsync -av --exclude='node_modules' ./ /opt/linghua-app/
cd /opt/linghua-app

# 重启服务
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d

# 清理
rm -f /tmp/linghua-deploy.tar.gz
rm -rf /tmp/0607linghua-enamel-gallery
EOF

# 4. 清理本地文件
rm -f linghua-deploy.tar.gz

echo "✅ 部署完成！"
echo "🌐 访问地址: https://your-domain.com"
```

### 监控和日志

```bash
# 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 查看应用日志
docker logs linghua-production-app -f

# 查看数据库日志
docker logs linghua-production-postgres -f

# 系统资源监控
htop
df -h
free -h
```

### 备份策略

```bash
# 创建备份脚本
cat > /opt/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec linghua-production-postgres pg_dump -U postgres linghua_enamel_gallery_isolated > $BACKUP_DIR/database.sql

# 备份上传文件
cp -r /opt/linghua-app/uploads $BACKUP_DIR/

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz -C /opt/backups $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

# 保留最近7天的备份
find /opt/backups -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/scripts/backup.sh

# 设置定时备份
crontab -e
# 添加：0 2 * * * /opt/scripts/backup.sh
```

## 🔒 安全最佳实践

### 1. 网络安全
- 使用VPC专有网络
- 配置安全组规则，最小化开放端口
- 启用DDoS防护

### 2. 数据安全
- 定期备份数据库
- 启用数据库加密
- 使用强密码策略

### 3. 应用安全
- 定期更新系统和依赖
- 使用HTTPS加密传输
- 配置Web应用防火墙(WAF)

### 4. 访问控制
- 使用密钥对登录，禁用密码登录
- 配置堡垒机访问
- 启用操作审计

## 💰 成本优化

### 1. 资源优化
- 选择合适的实例规格
- 使用预付费实例节省成本
- 配置弹性伸缩

### 2. 存储优化
- 使用对象存储OSS存放静态文件
- 配置生命周期管理
- 启用数据压缩

### 3. 网络优化
- 使用CDN加速静态资源
- 配置智能DNS解析
- 优化带宽配置

## 🔧 GitHub连接问题解决方案

### 问题描述
阿里云服务器可能无法直接访问GitHub，导致无法克隆代码或安装依赖。

### 解决方案

#### 方案一：使用国内镜像源（推荐）

```bash
# 1. 配置npm使用淘宝镜像
npm config set registry https://registry.npmmirror.com/

# 2. 配置Git使用国内镜像
git config --global url."https://gitee.com/".insteadOf "https://github.com/"
git config --global url."https://hub.fastgit.xyz/".insteadOf "https://github.com/"

# 3. 使用Gitee作为代码托管
# 在Gitee上创建仓库并推送代码
# 然后在服务器上从Gitee克隆
git clone https://gitee.com/your-username/linghua-enamel-gallery.git
```

#### 方案二：配置代理服务器

```bash
# 如果有可用的代理服务器
git config --global http.proxy http://proxy-server:port
git config --global https.proxy https://proxy-server:port

# 或者只为GitHub配置代理
git config --global http.https://github.com.proxy http://proxy-server:port
```

#### 方案三：修改hosts文件

```bash
# 添加GitHub的IP地址到hosts文件
echo "************ github.com" >> /etc/hosts
echo "************ codeload.github.com" >> /etc/hosts
echo "*************** assets-cdn.github.com" >> /etc/hosts

# 刷新DNS
systemctl restart systemd-resolved
```

#### 方案四：使用本地上传（最可靠）

```bash
# 在本地准备完整的项目包
# 1. 安装依赖
npm install

# 2. 构建项目
npm run build

# 3. 打包所有文件（包含node_modules）
tar --exclude='.git' --exclude='*.log' -czf linghua-complete.tar.gz .

# 4. 上传到服务器
scp linghua-complete.tar.gz root@your-ecs-ip:/opt/

# 5. 在服务器上解压即可使用
cd /opt
tar -xzf linghua-complete.tar.gz
```

### Docker镜像加速

```bash
# 配置Docker使用阿里云镜像加速器
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://your-accelerator-id.mirror.aliyuncs.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOF

# 重启Docker服务
systemctl daemon-reload
systemctl restart docker
```

## 📞 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   docker-compose logs service-name
   ```

2. **数据库连接失败**
   ```bash
   docker exec -it linghua-production-postgres psql -U postgres
   ```

3. **应用无法访问**
   ```bash
   curl -I http://localhost:3000
   nginx -t
   ```

4. **磁盘空间不足**
   ```bash
   docker system prune -a
   ```

---

**注意**: 部署到生产环境前，请确保：
1. 修改所有默认密码
2. 配置SSL证书
3. 设置监控和告警
4. 制定备份和恢复计划
5. 进行安全加固
