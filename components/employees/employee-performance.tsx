"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  TrendingUpIcon, 
  StarIcon, 
  TargetIcon,
  AwardIcon,
  CalendarIcon,
  BarChart3Icon
} from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface EmployeePerformanceProps {
  employeeId: number
}

export function EmployeePerformance({ employeeId }: EmployeePerformanceProps) {
  const [performanceData, setPerformanceData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadPerformanceData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/employees/${employeeId}/performance`)
        if (response.ok) {
          const data = await response.json()
          setPerformanceData(data)
        } else {
          // 如果API不存在，使用模拟数据
          setPerformanceData([
            {
              id: 1,
              month: "2024-03",
              rating: "优秀",
              bonus: 1000,
              salesActual: 50000,
              salesTarget: 45000,
              customerSatisfaction: 4.8,
              attendanceRate: 95,
              createdAt: new Date().toISOString()
            }
          ])
        }
      } catch (error) {
        console.error('加载绩效数据失败:', error)
        setPerformanceData([])
      } finally {
        setIsLoading(false)
      }
    }
    
    if (employeeId) {
      loadPerformanceData()
    }
  }, [employeeId])

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case "优秀":
        return "bg-green-100 text-green-800"
      case "良好":
        return "bg-blue-100 text-blue-800"
      case "一般":
        return "bg-yellow-100 text-yellow-800"
      case "待改进":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getSalesProgress = (actual: number, target: number) => {
    return Math.min((actual / target) * 100, 100)
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3Icon className="h-5 w-5" />
            绩效统计
          </CardTitle>
          <CardDescription>
            员工绩效考核记录和统计分析
          </CardDescription>
        </CardHeader>
        <CardContent>
          {performanceData.length === 0 ? (
            <div className="text-center py-8">
              <TrendingUpIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">暂无绩效记录</p>
            </div>
          ) : (
            <div className="space-y-4">
              {performanceData.map((performance) => (
                <Card key={performance.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-medium">
                          {format(new Date(performance.month), "yyyy年MM月", { locale: zhCN })}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          考核时间: {format(new Date(performance.createdAt), "yyyy-MM-dd", { locale: zhCN })}
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <Badge className={getRatingColor(performance.rating)}>
                          {performance.rating}
                        </Badge>
                        <p className="text-sm text-green-600 mt-1">
                          奖金: ¥{performance.bonus}
                        </p>
                      </div>
                    </div>
                    
                    {/* 销售目标进度 */}
                    <div className="space-y-2 mb-3">
                      <div className="flex justify-between text-sm">
                        <span>销售目标完成度</span>
                        <span>{getSalesProgress(performance.salesActual, performance.salesTarget).toFixed(1)}%</span>
                      </div>
                      <Progress 
                        value={getSalesProgress(performance.salesActual, performance.salesTarget)} 
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>实际: ¥{performance.salesActual.toLocaleString()}</span>
                        <span>目标: ¥{performance.salesTarget.toLocaleString()}</span>
                      </div>
                    </div>
                    
                    {/* 其他指标 */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <StarIcon className="h-4 w-4 text-yellow-500" />
                        <span>满意度: {performance.customerSatisfaction}/5.0</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <TargetIcon className="h-4 w-4 text-blue-500" />
                        <span>出勤率: {performance.attendanceRate}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}