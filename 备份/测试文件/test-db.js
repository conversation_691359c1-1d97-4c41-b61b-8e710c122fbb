const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: ['query', 'error', 'warn'],
})

async function testDatabase() {
  try {
    console.log('🔄 测试数据库连接...')
    
    // 测试基本连接
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 数据库连接成功')
    
    // 测试用户表
    const userCount = await prisma.user.count()
    console.log(`✅ 用户表查询成功，共有 ${userCount} 个用户`)
    
    // 测试产品表
    const productCount = await prisma.product.count()
    console.log(`✅ 产品表查询成功，共有 ${productCount} 个产品`)
    
    // 测试库存表
    const inventoryCount = await prisma.inventoryItem.count()
    console.log(`✅ 库存表查询成功，共有 ${inventoryCount} 个库存项`)
    
    console.log('🎉 所有数据库测试通过！')
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
