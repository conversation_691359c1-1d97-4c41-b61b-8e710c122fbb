/** @type {import('next').NextConfig} */
const nextConfig = {
  // 生产环境严格检查
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'development'
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'development'
  },

  // 内存优化配置
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    // 内存优化选项
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },

  // Turbopack配置 (现在已稳定)
  // turbopack: {
  //   // Turbopack选项 (如果需要的话)
  // },

  // 图片优化 - 减少内存占用
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200], // 减少设备尺寸数量
    imageSizes: [16, 32, 48, 64, 96], // 减少图片尺寸数量
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30天缓存
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    domains: ['localhost'],
    unoptimized: process.env.NODE_ENV === 'development',
  },

  // 输出配置 - 优化构建
  output: 'standalone', // 独立输出，减少依赖

  // 压缩配置
  compress: true,

  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },
  
  // 内存优化的webpack配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 生产环境内存优化
    if (!dev) {
      // 优化代码分割
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 200000, // 限制chunk大小
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
            maxSize: 200000, // 限制vendor chunk大小
          },
          // 按功能分组，减少内存占用
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
            name: 'ui',
            priority: 10,
            chunks: 'all',
            maxSize: 150000,
          },
          utils: {
            test: /[\\/]node_modules[\\/](date-fns|lodash|clsx|class-variance-authority)[\\/]/,
            name: 'utils', 
            priority: 5,
            chunks: 'all',
            maxSize: 100000,
          }
        },
      };

      // 移除开发工具，减少内存占用
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      
      // 内存优化
      config.optimization.minimize = true;
      
      // 限制并发处理数量
      config.parallelism = 1; // 在低内存环境下使用单线程
    }

    // 开发环境内存优化
    if (dev) {
      // 减少开发时的内存使用
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: ['**/node_modules', '**/.git', '**/dist', '**/.next'],
      };
    }

    return config;
  },

  // 头部配置 - 缓存优化
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300', // 5分钟缓存
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable', // 1年缓存
          },
        ],
      },
    ];
  },
};

export default nextConfig;
