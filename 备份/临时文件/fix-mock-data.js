/**
 * 批量修复使用模拟数据的组件
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 开始修复模拟数据问题...')

// 需要修复的文件列表
const filesToFix = [
  'components/dashboard/recent-activities.tsx',
  'components/mobile/employees/performance-tab.tsx',
  'components/channel/channel-sales-import-form.tsx',
  'components/personalization/customizable-dashboard.tsx'
]

// 修复最近活动组件
function fixRecentActivities() {
  const filePath = 'components/dashboard/recent-activities.tsx'
  if (!fs.existsSync(filePath)) return

  let content = fs.readFileSync(filePath, 'utf8')
  
  // 替换模拟数据为API调用
  content = content.replace(
    /const mockActivities: Activity\[\] = \[[\s\S]*?\]/,
    `// 从API获取活动数据
    const [activities, setActivities] = useState<Activity[]>([])
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
      const loadActivities = async () => {
        try {
          const response = await fetch('/api/dashboard/activities')
          if (response.ok) {
            const data = await response.json()
            setActivities(data)
          }
        } catch (error) {
          console.error('加载活动数据失败:', error)
          // 设置默认数据
          setActivities([])
        } finally {
          setIsLoading(false)
        }
      }
      loadActivities()
    }, [])`
  )

  content = content.replace(
    /return mockActivities/,
    'return activities'
  )

  fs.writeFileSync(filePath, content)
  console.log(`✅ 修复: ${filePath}`)
}

// 修复员工绩效组件
function fixEmployeePerformance() {
  const filePath = 'components/mobile/employees/performance-tab.tsx'
  if (!fs.existsSync(filePath)) return

  let content = fs.readFileSync(filePath, 'utf8')
  
  // 替换模拟数据
  content = content.replace(
    /const mockPerformanceData = \[[\s\S]*?\]/,
    `const [performanceData, setPerformanceData] = useState([])
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
      const loadPerformanceData = async () => {
        try {
          const response = await fetch('/api/employees/performance')
          if (response.ok) {
            const data = await response.json()
            setPerformanceData(data)
          }
        } catch (error) {
          console.error('加载绩效数据失败:', error)
          setPerformanceData([])
        } finally {
          setIsLoading(false)
        }
      }
      loadPerformanceData()
    }, [])`
  )

  fs.writeFileSync(filePath, content)
  console.log(`✅ 修复: ${filePath}`)
}

// 修复渠道销售导入组件
function fixChannelSalesImport() {
  const filePath = 'components/channel/channel-sales-import-form.tsx'
  if (!fs.existsSync(filePath)) return

  let content = fs.readFileSync(filePath, 'utf8')
  
  // 替换模拟解析数据
  content = content.replace(
    /const mockParsedData = \[[\s\S]*?\]/,
    `// 实际解析CSV数据
    const parsedData = await parseCSVFile(file)`
  )

  content = content.replace(
    /setParsedData\(mockParsedData\)/,
    'setParsedData(parsedData)'
  )

  fs.writeFileSync(filePath, content)
  console.log(`✅ 修复: ${filePath}`)
}

// 修复个性化仪表板
function fixCustomizableDashboard() {
  const filePath = 'components/personalization/customizable-dashboard.tsx'
  if (!fs.existsSync(filePath)) return

  let content = fs.readFileSync(filePath, 'utf8')
  
  // 替换模拟数据
  content = content.replace(
    /const mockData = \{[\s\S]*?\}/,
    `// 从API获取仪表板数据
    const [cardData, setCardData] = useState({})
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
      const loadDashboardData = async () => {
        try {
          const response = await fetch('/api/dashboard/customizable')
          if (response.ok) {
            const data = await response.json()
            setCardData(data)
          }
        } catch (error) {
          console.error('加载仪表板数据失败:', error)
          setCardData({})
        } finally {
          setIsLoading(false)
        }
      }
      loadDashboardData()
    }, [])`
  )

  content = content.replace(
    /setCardData\(mockData\)/,
    '// 数据已在useEffect中设置'
  )

  fs.writeFileSync(filePath, content)
  console.log(`✅ 修复: ${filePath}`)
}

// 执行修复
try {
  fixRecentActivities()
  fixEmployeePerformance()
  fixChannelSalesImport()
  fixCustomizableDashboard()
  
  console.log('🎉 模拟数据修复完成！')
  console.log('📝 注意: 需要创建对应的API端点来提供真实数据')
} catch (error) {
  console.error('❌ 修复过程中发生错误:', error)
}
