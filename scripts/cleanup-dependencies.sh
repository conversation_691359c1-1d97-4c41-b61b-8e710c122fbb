#!/bin/bash

echo "🧹 开始清理未使用的依赖包..."

# 移除未使用的依赖包
echo "📦 移除未使用的依赖包..."
npm uninstall \
  @modelcontextprotocol/server-filesystem \
  @modelcontextprotocol/server-github \
  @modelcontextprotocol/server-postgres \
  @stagewise/toolbar-next \
  browser-image-compression \
  critters \
  next-pwa \
  workbox-window

# 移除未使用的开发依赖
echo "🔧 移除未使用的开发依赖..."
npm uninstall --save-dev \
  @testing-library/user-event \
  depcheck \
  typedoc-plugin-markdown

# 安装缺失的依赖
echo "➕ 安装缺失的依赖..."
npm install \
  isomorphic-dompurify \
  ioredis \
  cookies-next \
  lru-cache

echo "✅ 依赖清理完成!"

# 运行audit修复
echo "🔒 修复安全漏洞..."
npm audit fix

echo "📊 最终依赖统计..."
npm ls --depth=0