# 宝塔面板部署指南

## 概述

本指南详细说明如何在宝塔面板环境中部署独立Docker环境，利用宝塔面板的可视化管理功能简化部署和运维过程。

## 🏗️ 宝塔面板环境准备

### 1. 安装宝塔面板

如果还未安装宝塔面板：

```bash
# CentOS/RHEL
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2. 宝塔面板基础配置

1. 登录宝塔面板：`http://your-server-ip:8888`
2. 完成初始化设置
3. 安装推荐的软件栈（可选）：
   - Nginx 1.20+
   - MySQL 8.0（如果不使用Docker数据库）
   - PHP 8.0+（如果需要）
   - Redis（如果不使用Docker Redis）

## 🐳 在宝塔面板中安装Docker

### 方法一：通过宝塔软件商店安装

1. 登录宝塔面板
2. 进入 **软件商店**
3. 搜索 **Docker管理器**
4. 点击 **安装**
5. 安装完成后，在 **软件商店** > **已安装** 中找到Docker管理器

### 方法二：手动安装Docker

如果软件商店中没有Docker，可以通过终端安装：

1. 在宝塔面板中打开 **终端**
2. 执行以下命令：

```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

### 3. 配置Docker镜像加速

在宝塔终端中执行：

```bash
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOF

systemctl restart docker
```

## 📁 项目文件管理

### 1. 通过宝塔文件管理器上传项目

1. 在宝塔面板中打开 **文件管理**
2. 创建项目目录：`/www/wwwroot/linghua-app`
3. 上传项目文件：
   - 方式一：直接拖拽上传压缩包
   - 方式二：使用在线解压功能
   - 方式三：通过Git克隆（如果网络允许）

### 2. 项目文件结构

```
/www/wwwroot/linghua-app/
├── docker-compose.isolated.yml
├── .env.isolated
├── package.json
├── prisma/
├── app/
├── components/
├── lib/
├── scripts/
└── uploads/
```

### 3. 设置文件权限

在宝塔终端中执行：

```bash
cd /www/wwwroot/linghua-app
chown -R www:www .
chmod -R 755 .
chmod +x scripts/*.sh
```

## 🚀 部署独立Docker环境

### 1. 修改配置文件

#### 编辑环境变量文件

在宝塔文件管理器中编辑 `.env.isolated`：

```env
# 数据库配置
DATABASE_URL="***********************************************************************/linghua_enamel_gallery_isolated?schema=public"

# 认证配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="bt-super-secure-secret-key-2024"
AUTH_SECRET="bt-super-secure-secret-key-2024"
AUTH_TRUST_HOST="true"

# Redis配置
REDIS_URL="redis://linghua-isolated-redis:6379"

# 应用配置
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NODE_ENV="production"
ENVIRONMENT="production"
```

#### 修改Docker Compose配置

编辑 `docker-compose.isolated.yml`，调整为生产环境：

```yaml
services:
  linghua-isolated-postgres:
    image: postgres:15-alpine
    container_name: linghua-bt-postgres
    restart: always
    environment:
      POSTGRES_DB: linghua_enamel_gallery_isolated
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: bt_secure_password
    ports:
      - "5434:5432"
    volumes:
      - /www/server/data/postgres:/var/lib/postgresql/data
    networks:
      - linghua-bt-network

  linghua-isolated-redis:
    image: redis:7-alpine
    container_name: linghua-bt-redis
    restart: always
    ports:
      - "6381:6379"
    volumes:
      - /www/server/data/redis:/data
    networks:
      - linghua-bt-network
    command: redis-server --appendonly yes --requirepass bt_redis_password

  linghua-isolated-app:
    image: node:18-alpine
    container_name: linghua-bt-app
    restart: always
    working_dir: /app
    command: sh -c "npm install --production && npm run build && npm start"
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*****************************************************************/linghua_enamel_gallery_isolated?schema=public
      - NEXTAUTH_URL=https://your-domain.com
      - REDIS_URL=redis://:bt_redis_password@linghua-bt-redis:6379
    volumes:
      - /www/wwwroot/linghua-app:/app
      - /app/node_modules
    networks:
      - linghua-bt-network
    depends_on:
      - linghua-isolated-postgres
      - linghua-isolated-redis

  linghua-isolated-adminer:
    image: adminer:latest
    container_name: linghua-bt-adminer
    restart: always
    ports:
      - "8081:8080"
    networks:
      - linghua-bt-network
    depends_on:
      - linghua-isolated-postgres

volumes:
  bt_postgres_data:
    driver: local
  bt_redis_data:
    driver: local

networks:
  linghua-bt-network:
    driver: bridge
```

### 2. 启动服务

在宝塔终端中执行：

```bash
cd /www/wwwroot/linghua-app

# 启动所有服务
docker-compose -f docker-compose.isolated.yml --env-file .env.isolated up -d

# 查看服务状态
docker-compose -f docker-compose.isolated.yml ps

# 查看日志
docker-compose -f docker-compose.isolated.yml logs -f
```

### 3. 初始化数据库

```bash
# 等待数据库启动
sleep 30

# 初始化Prisma
docker exec linghua-bt-app npx prisma generate
docker exec linghua-bt-app npx prisma db push
```

## 🌐 配置Nginx反向代理

### 1. 在宝塔面板中添加站点

1. 进入 **网站** 管理
2. 点击 **添加站点**
3. 填写域名：`your-domain.com`
4. 选择 **不创建FTP** 和 **不创建数据库**
5. 点击 **提交**

### 2. 配置反向代理

1. 点击站点的 **设置**
2. 进入 **反向代理**
3. 添加反向代理：
   - **代理名称**：linghua-app
   - **目标URL**：`http://127.0.0.1:3002`
   - **发送域名**：`$host`
   - **内容替换**：留空

### 3. 配置SSL证书

1. 在站点设置中进入 **SSL**
2. 选择 **Let's Encrypt** 免费证书
3. 填写邮箱地址
4. 点击 **申请**
5. 开启 **强制HTTPS**

## 📊 数据迁移

### 1. 从本地数据库迁移

#### 导出本地数据

在本地环境执行：

```bash
# 导出数据
pg_dump "postgresql://macmini@localhost:5432/linghua_enamel_gallery" > local_data.sql

# 上传到服务器
scp local_data.sql root@your-server-ip:/www/wwwroot/linghua-app/
```

#### 导入到宝塔环境

在宝塔终端执行：

```bash
cd /www/wwwroot/linghua-app

# 导入数据到Docker数据库
docker exec -i linghua-bt-postgres psql -U postgres -d linghua_enamel_gallery_isolated < local_data.sql
```

### 2. 从其他服务器迁移

#### 使用宝塔数据库同步工具

1. 在宝塔面板中安装 **数据库同步工具**
2. 配置源数据库和目标数据库连接
3. 选择要同步的表
4. 执行同步

#### 手动迁移脚本

创建迁移脚本 `/www/wwwroot/linghua-app/migrate-bt.sh`：

```bash
#!/bin/bash

SOURCE_DB="***************************************/source_db"
TARGET_DB="postgresql://postgres:bt_secure_password@localhost:5434/linghua_enamel_gallery_isolated"

echo "开始数据迁移..."

# 导出源数据库
pg_dump "$SOURCE_DB" --data-only --no-owner --no-privileges > /tmp/migration_data.sql

# 导入到目标数据库
psql "$TARGET_DB" < /tmp/migration_data.sql

# 清理临时文件
rm -f /tmp/migration_data.sql

echo "数据迁移完成！"
```

## 🔧 宝塔面板管理功能

### 1. 容器监控

通过宝塔Docker管理器：

1. 查看容器状态
2. 监控资源使用
3. 查看容器日志
4. 重启容器

### 2. 文件管理

1. **在线编辑**：直接编辑配置文件
2. **文件上传**：拖拽上传新版本
3. **权限管理**：设置文件权限
4. **备份管理**：定时备份项目文件

### 3. 定时任务

设置自动化任务：

1. 进入 **计划任务**
2. 添加任务：
   - **任务类型**：Shell脚本
   - **任务名称**：数据库备份
   - **执行周期**：每天凌晨2点
   - **脚本内容**：
   ```bash
   #!/bin/bash
   BACKUP_DIR="/www/backup/linghua/$(date +%Y%m%d)"
   mkdir -p $BACKUP_DIR
   docker exec linghua-bt-postgres pg_dump -U postgres linghua_enamel_gallery_isolated > $BACKUP_DIR/database.sql
   tar -czf $BACKUP_DIR.tar.gz -C /www/backup/linghua $(basename $BACKUP_DIR)
   rm -rf $BACKUP_DIR
   find /www/backup/linghua -name "*.tar.gz" -mtime +7 -delete
   ```

### 4. 安全管理

1. **防火墙设置**：
   - 开放必要端口：80, 443, 8888
   - 限制数据库端口：5434（仅内网访问）

2. **访问限制**：
   - 设置Adminer访问IP白名单
   - 配置宝塔面板访问限制

## 📈 性能优化

### 1. 服务器优化

在宝塔面板中：

1. **系统优化**：使用宝塔系统优化工具
2. **内存优化**：配置Swap分区
3. **磁盘优化**：定期清理日志文件

### 2. 应用优化

```bash
# 优化Docker配置
cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

systemctl restart docker
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   - 在宝塔Docker管理器中查看容器日志
   - 检查端口占用情况

2. **数据库连接失败**
   - 检查数据库容器状态
   - 验证连接字符串

3. **反向代理不工作**
   - 检查Nginx配置
   - 确认应用端口正确

4. **SSL证书问题**
   - 重新申请证书
   - 检查域名解析

### 监控和日志

1. **系统监控**：使用宝塔系统监控
2. **应用日志**：通过Docker管理器查看
3. **访问日志**：在网站管理中查看Nginx日志

---

**优势总结**：
- ✅ 可视化管理界面
- ✅ 简化的部署流程
- ✅ 集成的监控和日志
- ✅ 自动化备份和任务
- ✅ 安全管理功能
