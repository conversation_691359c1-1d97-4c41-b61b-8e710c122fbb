/**
 * 内存优化测试脚本
 * 测试4GB内存优化配置是否正常工作
 */

const { PrismaClient } = require('@prisma/client')

// 测试内存监控系统
async function testMemoryMonitoring() {
  console.log('🔍 测试内存监控系统...')
  
  try {
    // 动态导入内存监控模块
    const MemoryMonitor = require('./lib/memory-monitor.ts')
    
    console.log('✅ 内存监控模块加载成功')
    
    // 获取当前内存使用情况
    const memoryStats = process.memoryUsage()
    const heapUsedMB = memoryStats.heapUsed / 1024 / 1024
    const rssMB = memoryStats.rss / 1024 / 1024
    
    console.log(`📊 当前内存使用:`)
    console.log(`   RSS: ${rssMB.toFixed(2)}MB`)
    console.log(`   Heap Used: ${heapUsedMB.toFixed(2)}MB`)
    console.log(`   Heap Total: ${(memoryStats.heapTotal / 1024 / 1024).toFixed(2)}MB`)
    
    return true
  } catch (error) {
    console.error('❌ 内存监控测试失败:', error.message)
    return false
  }
}

// 测试优化的Prisma客户端
async function testOptimizedPrisma() {
  console.log('🔍 测试优化的Prisma客户端...')
  
  try {
    // 使用标准Prisma客户端进行基础测试
    const prisma = new PrismaClient({
      log: ['error'],
      errorFormat: 'minimal'
    })
    
    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 数据库连接正常')
    
    // 测试基本查询
    const userCount = await prisma.user.count()
    const productCount = await prisma.product.count()
    
    console.log(`📊 数据统计:`)
    console.log(`   用户数量: ${userCount}`)
    console.log(`   产品数量: ${productCount}`)
    
    await prisma.$disconnect()
    return true
  } catch (error) {
    console.error('❌ Prisma客户端测试失败:', error.message)
    return false
  }
}

// 测试内存使用模拟
async function testMemoryUsage() {
  console.log('🔍 测试内存使用模拟...')
  
  const initialMemory = process.memoryUsage()
  console.log(`📊 初始内存: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`)
  
  // 创建一些内存使用来测试监控
  const testData = []
  for (let i = 0; i < 10000; i++) {
    testData.push({
      id: i,
      name: `测试数据${i}`,
      description: `这是第${i}个测试数据项，用于测试内存使用情况`,
      timestamp: new Date(),
      data: new Array(100).fill(Math.random())
    })
  }
  
  const afterMemory = process.memoryUsage()
  console.log(`📊 使用后内存: ${(afterMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`)
  console.log(`📈 内存增长: ${((afterMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024).toFixed(2)}MB`)
  
  // 清理测试数据
  testData.length = 0
  
  // 尝试触发垃圾回收
  if (global.gc) {
    global.gc()
    const afterGC = process.memoryUsage()
    console.log(`🧹 垃圾回收后: ${(afterGC.heapUsed / 1024 / 1024).toFixed(2)}MB`)
  } else {
    console.log('⚠️  垃圾回收不可用 (需要 --expose-gc 参数)')
  }
  
  return true
}

// 测试配置文件
async function testConfigFiles() {
  console.log('🔍 测试配置文件...')
  
  const fs = require('fs')
  const path = require('path')
  
  const configFiles = [
    'next.config.optimized.mjs',
    'postgresql-4gb.conf',
    'nginx-4gb.conf',
    'docker-compose.4gb.yml',
    'Dockerfile.4gb'
  ]
  
  let allFilesExist = true
  
  for (const file of configFiles) {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath)
      console.log(`✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`)
    } else {
      console.log(`❌ ${file} - 文件不存在`)
      allFilesExist = false
    }
  }
  
  return allFilesExist
}

// 主测试函数
async function runOptimizationTests() {
  console.log('🚀 开始4GB内存优化测试')
  console.log('=' .repeat(50))
  
  const results = {
    memoryMonitoring: false,
    optimizedPrisma: false,
    memoryUsage: false,
    configFiles: false
  }
  
  try {
    // 1. 测试配置文件
    console.log('\n1️⃣ 配置文件检查')
    results.configFiles = await testConfigFiles()
    
    // 2. 测试内存使用
    console.log('\n2️⃣ 内存使用测试')
    results.memoryUsage = await testMemoryUsage()
    
    // 3. 测试Prisma客户端
    console.log('\n3️⃣ Prisma客户端测试')
    results.optimizedPrisma = await testOptimizedPrisma()
    
    // 4. 测试内存监控 (可能失败，因为是TypeScript)
    console.log('\n4️⃣ 内存监控测试')
    results.memoryMonitoring = await testMemoryMonitoring()
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
  
  // 输出测试结果
  console.log('\n📋 测试结果总结')
  console.log('=' .repeat(50))
  
  const totalTests = Object.keys(results).length
  const passedTests = Object.values(results).filter(Boolean).length
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败'
    const testName = {
      configFiles: '配置文件检查',
      memoryUsage: '内存使用测试',
      optimizedPrisma: 'Prisma客户端测试',
      memoryMonitoring: '内存监控测试'
    }[test]
    
    console.log(`${status} ${testName}`)
  })
  
  console.log(`\n📊 测试通过率: ${passedTests}/${totalTests} (${((passedTests/totalTests)*100).toFixed(1)}%)`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！4GB内存优化配置正常')
  } else if (passedTests >= totalTests * 0.75) {
    console.log('⚠️  大部分测试通过，系统基本可用')
  } else {
    console.log('❌ 多项测试失败，需要检查配置')
  }
  
  // 显示系统信息
  console.log('\n💻 系统信息')
  console.log('=' .repeat(50))
  console.log(`Node.js版本: ${process.version}`)
  console.log(`平台: ${process.platform}`)
  console.log(`架构: ${process.arch}`)
  console.log(`当前内存使用: ${(process.memoryUsage().rss / 1024 / 1024).toFixed(2)}MB`)
  
  return passedTests === totalTests
}

// 运行测试
if (require.main === module) {
  runOptimizationTests()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('测试运行失败:', error)
      process.exit(1)
    })
}

module.exports = { runOptimizationTests }
