import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

// 业务规则引擎
export class BusinessRulesEngine {
  constructor(private prisma: PrismaClient) {}

  // 通用业务规则验证
  async validateBusinessRules(
    entity: string,
    operation: 'create' | 'update' | 'delete',
    data: any,
    context?: any
  ): Promise<{
    isValid: boolean
    violations: Array<{
      rule: string
      severity: 'error' | 'warning' | 'info'
      message: string
      field?: string
    }>
    suggestions: string[]
  }> {
    const violations = []
    const suggestions = []

    switch (entity) {
      case 'product':
        await this.validateProductRules(operation, data, violations, suggestions)
        break
      case 'order':
        await this.validateOrderRules(operation, data, violations, suggestions, context)
        break
      case 'employee':
        await this.validateEmployeeRules(operation, data, violations, suggestions)
        break
      case 'customer':
        await this.validateCustomerRules(operation, data, violations, suggestions)
        break
      case 'inventory':
        await this.validateInventoryRules(operation, data, violations, suggestions)
        break
      case 'financial':
        await this.validateFinancialRules(operation, data, violations, suggestions)
        break
    }

    const hasErrors = violations.some(v => v.severity === 'error')

    return {
      isValid: !hasErrors,
      violations,
      suggestions
    }
  }

  // 产品业务规则
  private async validateProductRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[]
  ): Promise<void> {
    // 价格合理性检查
    if (data.price && data.cost) {
      const profit = data.price - data.cost
      const profitMargin = (profit / data.price) * 100

      if (profitMargin < 10) {
        violations.push({
          rule: 'MIN_PROFIT_MARGIN',
          severity: 'warning' as const,
          message: `利润率 ${profitMargin.toFixed(2)}% 低于建议的10%`,
          field: 'price'
        })
        suggestions.push('考虑调整价格或降低成本以提高利润率')
      }

      if (data.price < data.cost) {
        violations.push({
          rule: 'PRICE_BELOW_COST',
          severity: 'error' as const,
          message: '售价不能低于成本价',
          field: 'price'
        })
      }
    }

    // 库存水平检查
    if (data.minStockLevel && data.maxStockLevel) {
      if (data.minStockLevel >= data.maxStockLevel) {
        violations.push({
          rule: 'INVALID_STOCK_LEVELS',
          severity: 'error' as const,
          message: '最小库存量不能大于或等于最大库存量',
          field: 'minStockLevel'
        })
      }

      if (data.minStockLevel < 1) {
        violations.push({
          rule: 'MIN_STOCK_TOO_LOW',
          severity: 'warning' as const,
          message: '最小库存量建议设置为至少1件',
          field: 'minStockLevel'
        })
      }
    }

    // 产品名称唯一性
    if (operation === 'create' || (operation === 'update' && data.name)) {
      const existingProduct = await this.prisma.product.findFirst({
        where: {
          name: data.name,
          id: operation === 'update' ? { not: data.id } : undefined
        }
      })

      if (existingProduct) {
        violations.push({
          rule: 'PRODUCT_NAME_DUPLICATE',
          severity: 'error' as const,
          message: '产品名称已存在',
          field: 'name'
        })
      }
    }

    // SKU唯一性
    if (data.sku) {
      const existingSku = await this.prisma.product.findFirst({
        where: {
          sku: data.sku,
          id: operation === 'update' ? { not: data.id } : undefined
        }
      })

      if (existingSku) {
        violations.push({
          rule: 'SKU_DUPLICATE',
          severity: 'error' as const,
          message: 'SKU已存在',
          field: 'sku'
        })
      }
    }
  }

  // 订单业务规则
  private async validateOrderRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[],
    context?: any
  ): Promise<void> {
    // 客户信用额度检查
    if (data.customerId && data.totalAmount) {
      const customer = await this.prisma.customer.findUnique({
        where: { id: data.customerId },
        include: {
          orders: {
            where: {
              paymentStatus: { in: ['PENDING', 'PARTIALLY_PAID'] }
            }
          }
        }
      })

      if (customer) {
        const outstandingAmount = customer.orders.reduce(
          (sum, order) => sum + order.totalAmount.toNumber(), 0
        )
        const totalExposure = outstandingAmount + data.totalAmount

        if (customer.creditLimit && totalExposure > customer.creditLimit.toNumber()) {
          violations.push({
            rule: 'CREDIT_LIMIT_EXCEEDED',
            severity: 'error' as const,
            message: `订单金额超过客户信用额度 (当前: ${totalExposure}, 额度: ${customer.creditLimit})`,
            field: 'totalAmount'
          })
        }

        if (customer.creditLimit && totalExposure > customer.creditLimit.toNumber() * 0.8) {
          violations.push({
            rule: 'CREDIT_LIMIT_WARNING',
            severity: 'warning' as const,
            message: `订单金额接近客户信用额度的80%`,
            field: 'totalAmount'
          })
          suggestions.push('建议要求客户先付款或调整订单金额')
        }
      }
    }

    // 库存可用性检查
    if (data.items && Array.isArray(data.items)) {
      for (const item of data.items) {
        const inventory = await this.prisma.inventoryItem.aggregate({
          where: { productId: item.productId },
          _sum: {
            quantity: true,
            reservedQuantity: true
          }
        })

        const availableStock = (inventory._sum.quantity || 0) - (inventory._sum.reservedQuantity || 0)

        if (availableStock < item.quantity) {
          violations.push({
            rule: 'INSUFFICIENT_INVENTORY',
            severity: 'error' as const,
            message: `产品库存不足 (需要: ${item.quantity}, 可用: ${availableStock})`,
            field: `items.${item.productId}.quantity`
          })
        } else if (availableStock < item.quantity * 1.2) {
          violations.push({
            rule: 'LOW_INVENTORY_WARNING',
            severity: 'warning' as const,
            message: `产品库存较低，建议及时补货`,
            field: `items.${item.productId}.quantity`
          })
        }
      }
    }

    // 订单金额合理性检查
    if (data.totalAmount) {
      if (data.totalAmount > 1000000) {
        violations.push({
          rule: 'HIGH_VALUE_ORDER',
          severity: 'warning' as const,
          message: '高价值订单，建议额外审批',
          field: 'totalAmount'
        })
        suggestions.push('对于高价值订单，建议分批发货或要求预付款')
      }

      if (data.totalAmount < 10) {
        violations.push({
          rule: 'LOW_VALUE_ORDER',
          severity: 'info' as const,
          message: '小额订单，请确认客户需求',
          field: 'totalAmount'
        })
      }
    }

    // 交付日期合理性
    if (data.expectedDeliveryDate) {
      const today = new Date()
      const deliveryDate = new Date(data.expectedDeliveryDate)
      const daysDiff = Math.ceil((deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      if (daysDiff < 1) {
        violations.push({
          rule: 'DELIVERY_DATE_TOO_EARLY',
          severity: 'error' as const,
          message: '交付日期不能早于明天',
          field: 'expectedDeliveryDate'
        })
      } else if (daysDiff > 365) {
        violations.push({
          rule: 'DELIVERY_DATE_TOO_FAR',
          severity: 'warning' as const,
          message: '交付日期超过一年，请确认',
          field: 'expectedDeliveryDate'
        })
      } else if (daysDiff < 3) {
        violations.push({
          rule: 'TIGHT_DELIVERY_SCHEDULE',
          severity: 'warning' as const,
          message: '交付时间紧迫，请确认生产能力',
          field: 'expectedDeliveryDate'
        })
        suggestions.push('建议与生产部门确认是否能按时完成')
      }
    }
  }

  // 员工业务规则
  private async validateEmployeeRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[]
  ): Promise<void> {
    // 员工编号唯一性
    if (data.employeeNumber) {
      const existingEmployee = await this.prisma.employee.findFirst({
        where: {
          employeeNumber: data.employeeNumber,
          id: operation === 'update' ? { not: data.id } : undefined
        }
      })

      if (existingEmployee) {
        violations.push({
          rule: 'EMPLOYEE_NUMBER_DUPLICATE',
          severity: 'error' as const,
          message: '员工编号已存在',
          field: 'employeeNumber'
        })
      }
    }

    // 薪资范围检查
    if (data.salary) {
      if (data.salary < 3000) {
        violations.push({
          rule: 'SALARY_BELOW_MINIMUM',
          severity: 'warning' as const,
          message: '薪资低于建议最低标准',
          field: 'salary'
        })
      }

      if (data.salary > 50000) {
        violations.push({
          rule: 'HIGH_SALARY_APPROVAL',
          severity: 'warning' as const,
          message: '高薪资需要额外审批',
          field: 'salary'
        })
        suggestions.push('高薪资员工建议获得HR总监或CEO审批')
      }
    }

    // 年龄和入职日期合理性
    if (data.birthDate) {
      const age = Math.floor((Date.now() - new Date(data.birthDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000))
      
      if (age < 18) {
        violations.push({
          rule: 'EMPLOYEE_TOO_YOUNG',
          severity: 'error' as const,
          message: '员工年龄不能小于18岁',
          field: 'birthDate'
        })
      }

      if (age > 65) {
        violations.push({
          rule: 'EMPLOYEE_RETIREMENT_AGE',
          severity: 'warning' as const,
          message: '员工已达到退休年龄',
          field: 'birthDate'
        })
      }
    }

    if (data.hireDate) {
      const hireDate = new Date(data.hireDate)
      const today = new Date()

      if (hireDate > today) {
        violations.push({
          rule: 'FUTURE_HIRE_DATE',
          severity: 'error' as const,
          message: '入职日期不能是未来日期',
          field: 'hireDate'
        })
      }
    }
  }

  // 客户业务规则
  private async validateCustomerRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[]
  ): Promise<void> {
    // 联系方式验证
    if (data.phone) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(data.phone)) {
        violations.push({
          rule: 'INVALID_PHONE_FORMAT',
          severity: 'error' as const,
          message: '手机号码格式不正确',
          field: 'phone'
        })
      }
    }

    if (data.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        violations.push({
          rule: 'INVALID_EMAIL_FORMAT',
          severity: 'error' as const,
          message: '邮箱格式不正确',
          field: 'email'
        })
      }
    }

    // 信用额度合理性
    if (data.creditLimit) {
      if (data.creditLimit > 1000000) {
        violations.push({
          rule: 'HIGH_CREDIT_LIMIT',
          severity: 'warning' as const,
          message: '信用额度过高，建议风控审批',
          field: 'creditLimit'
        })
        suggestions.push('高信用额度客户建议提供担保或保证金')
      }

      if (data.creditLimit < 0) {
        violations.push({
          rule: 'NEGATIVE_CREDIT_LIMIT',
          severity: 'error' as const,
          message: '信用额度不能为负数',
          field: 'creditLimit'
        })
      }
    }

    // 客户类型一致性
    if (data.type === 'CORPORATE' && !data.companyName) {
      violations.push({
        rule: 'MISSING_COMPANY_NAME',
        severity: 'error' as const,
        message: '企业客户必须填写公司名称',
        field: 'companyName'
      })
    }

    if (data.type === 'INDIVIDUAL' && data.companyName) {
      violations.push({
        rule: 'INDIVIDUAL_WITH_COMPANY',
        severity: 'warning' as const,
        message: '个人客户不需要填写公司名称',
        field: 'companyName'
      })
    }
  }

  // 库存业务规则
  private async validateInventoryRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[]
  ): Promise<void> {
    // 库存数量合理性
    if (data.quantity !== undefined) {
      if (data.quantity < 0) {
        violations.push({
          rule: 'NEGATIVE_INVENTORY',
          severity: 'error' as const,
          message: '库存数量不能为负数',
          field: 'quantity'
        })
      }

      if (data.quantity > 10000) {
        violations.push({
          rule: 'EXCESSIVE_INVENTORY',
          severity: 'warning' as const,
          message: '库存数量异常高，请确认',
          field: 'quantity'
        })
        suggestions.push('大量库存可能占用过多资金，建议评估库存周转率')
      }
    }

    // 预留数量合理性
    if (data.reservedQuantity !== undefined && data.quantity !== undefined) {
      if (data.reservedQuantity > data.quantity) {
        violations.push({
          rule: 'RESERVED_EXCEEDS_TOTAL',
          severity: 'error' as const,
          message: '预留数量不能超过总库存',
          field: 'reservedQuantity'
        })
      }
    }

    // 调拨数量检查
    if (operation === 'transfer' && data.transferQuantity) {
      if (data.transferQuantity <= 0) {
        violations.push({
          rule: 'INVALID_TRANSFER_QUANTITY',
          severity: 'error' as const,
          message: '调拨数量必须大于0',
          field: 'transferQuantity'
        })
      }

      if (data.fromWarehouse === data.toWarehouse) {
        violations.push({
          rule: 'SAME_WAREHOUSE_TRANSFER',
          severity: 'error' as const,
          message: '源仓库和目标仓库不能相同',
          field: 'toWarehouse'
        })
      }
    }
  }

  // 财务业务规则
  private async validateFinancialRules(
    operation: string,
    data: any,
    violations: any[],
    suggestions: string[]
  ): Promise<void> {
    // 交易金额合理性
    if (data.amount !== undefined) {
      if (data.amount === 0) {
        violations.push({
          rule: 'ZERO_AMOUNT_TRANSACTION',
          severity: 'warning' as const,
          message: '零金额交易，请确认',
          field: 'amount'
        })
      }

      if (Math.abs(data.amount) > 1000000) {
        violations.push({
          rule: 'HIGH_VALUE_TRANSACTION',
          severity: 'warning' as const,
          message: '大额交易，建议额外审批',
          field: 'amount'
        })
        suggestions.push('大额交易建议获得财务总监审批')
      }
    }

    // 账户余额检查
    if (operation === 'withdrawal' && data.accountId && data.amount) {
      const account = await this.prisma.financialAccount.findUnique({
        where: { id: data.accountId }
      })

      if (account && account.balance.toNumber() < data.amount) {
        violations.push({
          rule: 'INSUFFICIENT_BALANCE',
          severity: 'error' as const,
          message: '账户余额不足',
          field: 'amount'
        })
      }
    }

    // 预算检查
    if (data.categoryId && data.amount && data.amount > 0) {
      // 检查当月预算使用情况
      const currentMonth = new Date()
      const monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
      const monthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0)

      const monthlySpending = await this.prisma.financialTransaction.aggregate({
        where: {
          categoryId: data.categoryId,
          transactionDate: {
            gte: monthStart,
            lte: monthEnd
          },
          type: 'EXPENSE'
        },
        _sum: { amount: true }
      })

      const currentSpending = monthlySpending._sum.amount?.toNumber() || 0
      const totalSpending = currentSpending + data.amount

      // 假设预算限制（实际应从预算表获取）
      const budgetLimit = 100000 // 简化处理

      if (totalSpending > budgetLimit) {
        violations.push({
          rule: 'BUDGET_EXCEEDED',
          severity: 'error' as const,
          message: `交易将超出月度预算 (当前: ${currentSpending}, 限额: ${budgetLimit})`,
          field: 'amount'
        })
      } else if (totalSpending > budgetLimit * 0.8) {
        violations.push({
          rule: 'BUDGET_WARNING',
          severity: 'warning' as const,
          message: '即将达到月度预算限制',
          field: 'amount'
        })
      }
    }
  }

  // 数据完整性检查
  async validateDataIntegrity(): Promise<{
    issues: Array<{
      type: 'orphan' | 'duplicate' | 'inconsistent' | 'missing'
      entity: string
      description: string
      count: number
      severity: 'high' | 'medium' | 'low'
    }>
    summary: {
      totalIssues: number
      criticalIssues: number
      recommendations: string[]
    }
  }> {
    const issues = []

    // 检查孤立记录
    const orphanProducts = await this.prisma.product.count({
      where: { categoryId: { not: null }, category: null }
    })
    if (orphanProducts > 0) {
      issues.push({
        type: 'orphan' as const,
        entity: 'product',
        description: '产品引用了不存在的分类',
        count: orphanProducts,
        severity: 'high' as const
      })
    }

    // 检查重复数据
    const duplicateSkus = await this.prisma.product.groupBy({
      by: ['sku'],
      having: { sku: { _count: { gt: 1 } } },
      _count: { sku: true }
    })
    if (duplicateSkus.length > 0) {
      issues.push({
        type: 'duplicate' as const,
        entity: 'product',
        description: '存在重复的SKU',
        count: duplicateSkus.length,
        severity: 'high' as const
      })
    }

    // 检查数据不一致
    const negativeInventory = await this.prisma.inventoryItem.count({
      where: { quantity: { lt: 0 } }
    })
    if (negativeInventory > 0) {
      issues.push({
        type: 'inconsistent' as const,
        entity: 'inventory',
        description: '存在负数库存',
        count: negativeInventory,
        severity: 'high' as const
      })
    }

    // 检查缺失关键数据
    const productsWithoutPrice = await this.prisma.product.count({
      where: { price: null }
    })
    if (productsWithoutPrice > 0) {
      issues.push({
        type: 'missing' as const,
        entity: 'product',
        description: '产品缺少价格信息',
        count: productsWithoutPrice,
        severity: 'medium' as const
      })
    }

    const criticalIssues = issues.filter(issue => issue.severity === 'high').length
    const recommendations = [
      '定期运行数据完整性检查',
      '建立数据清理程序',
      '加强数据录入验证',
      '实施数据质量监控'
    ]

    return {
      issues,
      summary: {
        totalIssues: issues.length,
        criticalIssues,
        recommendations
      }
    }
  }
}

// 数据约束验证schemas
export const dataConstraintSchemas = {
  product: z.object({
    name: z.string().min(1, '产品名称不能为空').max(200, '产品名称不能超过200字符'),
    sku: z.string().min(1, 'SKU不能为空').max(50, 'SKU不能超过50字符'),
    price: z.number().min(0, '价格不能为负数').max(999999, '价格不能超过999999'),
    cost: z.number().min(0, '成本不能为负数').max(999999, '成本不能超过999999'),
    minStockLevel: z.number().min(0, '最小库存不能为负数').max(9999, '最小库存不能超过9999'),
    maxStockLevel: z.number().min(1, '最大库存必须大于0').max(99999, '最大库存不能超过99999'),
    weight: z.number().min(0, '重量不能为负数').max(9999, '重量不能超过9999kg').optional(),
    description: z.string().max(1000, '描述不能超过1000字符').optional()
  }),

  customer: z.object({
    name: z.string().min(1, '客户名称不能为空').max(100, '客户名称不能超过100字符'),
    phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号码格式不正确').optional(),
    email: z.string().email('邮箱格式不正确').max(100, '邮箱不能超过100字符').optional(),
    creditLimit: z.number().min(0, '信用额度不能为负数').max(10000000, '信用额度不能超过1000万').optional(),
    address: z.string().max(200, '地址不能超过200字符').optional(),
    companyName: z.string().max(100, '公司名称不能超过100字符').optional()
  }),

  employee: z.object({
    name: z.string().min(1, '员工姓名不能为空').max(50, '员工姓名不能超过50字符'),
    employeeNumber: z.string().min(1, '员工编号不能为空').max(20, '员工编号不能超过20字符'),
    phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号码格式不正确').optional(),
    email: z.string().email('邮箱格式不正确').max(100, '邮箱不能超过100字符').optional(),
    salary: z.number().min(0, '薪资不能为负数').max(999999, '薪资不能超过999999'),
    department: z.string().min(1, '部门不能为空').max(50, '部门不能超过50字符'),
    position: z.string().min(1, '职位不能为空').max(50, '职位不能超过50字符')
  }),

  order: z.object({
    customerId: z.string().uuid('客户ID格式不正确'),
    totalAmount: z.number().min(0.01, '订单金额必须大于0').max(9999999, '订单金额不能超过999万'),
    paymentMethod: z.enum(['CASH', 'CARD', 'TRANSFER', 'WECHAT', 'ALIPAY']),
    deliveryAddress: z.string().max(200, '配送地址不能超过200字符').optional(),
    notes: z.string().max(500, '备注不能超过500字符').optional()
  }),

  inventory: z.object({
    productId: z.string().uuid('产品ID格式不正确'),
    warehouseId: z.string().uuid('仓库ID格式不正确'),
    quantity: z.number().min(0, '库存数量不能为负数').max(999999, '库存数量不能超过999999'),
    reservedQuantity: z.number().min(0, '预留数量不能为负数').max(999999, '预留数量不能超过999999'),
    minStockLevel: z.number().min(0, '最小库存不能为负数').max(9999, '最小库存不能超过9999').optional()
  }),

  financialTransaction: z.object({
    amount: z.number().min(0.01, '交易金额必须大于0').max(********, '交易金额不能超过9999万'),
    description: z.string().min(1, '交易描述不能为空').max(200, '交易描述不能超过200字符'),
    categoryId: z.string().uuid('分类ID格式不正确').optional(),
    fromAccountId: z.string().uuid('源账户ID格式不正确').optional(),
    toAccountId: z.string().uuid('目标账户ID格式不正确').optional()
  })
}