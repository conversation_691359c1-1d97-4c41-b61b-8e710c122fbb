#!/bin/bash

# ERP系统生产环境一键安装脚本
# 用途: 自动化部署ERP系统到生产环境
# 作者: ERP开发团队
# 版本: 1.0.0

set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统版本"
        exit 1
    fi
    
    . /etc/os-release
    log_info "操作系统: $NAME $VERSION"
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$MEMORY_GB" -lt 4 ]; then
        log_warn "内存少于4GB，建议至少8GB内存"
    fi
    log_info "系统内存: ${MEMORY_GB}GB"
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$DISK_SPACE" -lt 20 ]; then
        log_error "磁盘空间不足，至少需要20GB可用空间"
        exit 1
    fi
    log_info "可用磁盘空间: ${DISK_SPACE}GB"
    
    # 检查网络连接
    if ! ping -c 1 google.com &> /dev/null; then
        log_error "无法连接到互联网"
        exit 1
    fi
    log_info "网络连接正常"
}

# 安装系统依赖
install_system_dependencies() {
    log_step "安装系统依赖..."
    
    # 更新软件包列表
    apt-get update
    
    # 安装基础工具
    apt-get install -y \
        curl \
        wget \
        unzip \
        gnupg \
        lsb-release \
        ca-certificates \
        software-properties-common \
        apt-transport-https \
        ufw \
        htop \
        tree \
        git
    
    log_info "系统依赖安装完成"
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    
    # 检查Docker是否已安装
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，版本: $(docker --version)"
        return
    fi
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新软件包列表
    apt-get update
    
    # 安装Docker
    apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 添加当前用户到docker组
    usermod -aG docker $SUDO_USER 2>/dev/null || true
    
    log_info "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_step "安装Docker Compose..."
    
    # 检查Docker Compose是否已安装
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，版本: $(docker-compose --version)"
        return
    fi
    
    # 下载Docker Compose
    DOCKER_COMPOSE_VERSION="2.20.2"
    curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_info "Docker Compose安装完成"
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js..."
    
    # 检查Node.js是否已安装
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装，版本: $NODE_VERSION"
        
        # 检查版本是否符合要求 (>=18)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            log_warn "Node.js版本低于18，建议升级"
        else
            return
        fi
    fi
    
    # 安装NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    
    # 安装Node.js
    apt-get install -y nodejs
    
    log_info "Node.js安装完成，版本: $(node --version)"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    # 重置防火墙规则
    ufw --force reset
    
    # 默认策略
    ufw default deny incoming
    ufw default allow outgoing
    
    # 允许SSH
    ufw allow ssh
    
    # 允许HTTP/HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # 允许内部网络
    ufw allow from 10.0.0.0/8
    ufw allow from **********/12
    ufw allow from ***********/16
    
    # 启用防火墙
    ufw --force enable
    
    log_info "防火墙配置完成"
}

# 创建项目目录结构
create_project_structure() {
    log_step "创建项目目录结构..."
    
    PROJECT_ROOT="/opt/erp-system"
    
    # 创建主目录
    mkdir -p "$PROJECT_ROOT"
    mkdir -p "$PROJECT_ROOT/app"
    mkdir -p "$PROJECT_ROOT/data/postgres"
    mkdir -p "$PROJECT_ROOT/data/redis"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/backups"
    mkdir -p "$PROJECT_ROOT/ssl"
    mkdir -p "$PROJECT_ROOT/scripts"
    mkdir -p "$PROJECT_ROOT/nginx"
    
    # 设置权限
    chown -R $SUDO_USER:$SUDO_USER "$PROJECT_ROOT"
    chmod -R 755 "$PROJECT_ROOT"
    
    log_info "项目目录结构创建完成: $PROJECT_ROOT"
}

# 生成SSL证书
generate_ssl_certificates() {
    log_step "生成SSL证书..."
    
    PROJECT_ROOT="/opt/erp-system"
    SSL_DIR="$PROJECT_ROOT/ssl"
    
    # 检查是否已有证书
    if [[ -f "$SSL_DIR/server.crt" && -f "$SSL_DIR/server.key" ]]; then
        log_info "SSL证书已存在"
        return
    fi
    
    # 生成自签名证书 (生产环境应使用Let's Encrypt)
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$SSL_DIR/server.key" \
        -out "$SSL_DIR/server.crt" \
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
    
    # 设置权限
    chmod 600 "$SSL_DIR/server.key"
    chmod 644 "$SSL_DIR/server.crt"
    
    log_info "SSL证书生成完成 (自签名)"
    log_warn "生产环境建议使用Let's Encrypt获取有效证书"
}

# 创建环境配置文件
create_environment_config() {
    log_step "创建环境配置文件..."
    
    PROJECT_ROOT="/opt/erp-system"
    
    # 生成随机密钥
    DB_PASSWORD=$(openssl rand -base64 32)
    REDIS_PASSWORD=$(openssl rand -base64 32)
    NEXTAUTH_SECRET=$(openssl rand -base64 64)
    CSRF_SECRET=$(openssl rand -base64 32)
    ADMIN_CSRF_SECRET=$(openssl rand -base64 32)
    
    # 创建.env.production文件
    cat > "$PROJECT_ROOT/.env.production" <<EOF
# 生产环境配置
NODE_ENV=production
PORT=3000

# 数据库配置
DATABASE_URL=postgresql://erp_user:${DB_PASSWORD}@postgres:5432/erp_prod
DATABASE_CONNECTION_POOL_SIZE=20

# Redis配置
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# 认证配置
NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
NEXTAUTH_URL=https://localhost
CSRF_SECRET=${CSRF_SECRET}
ADMIN_CSRF_SECRET=${ADMIN_CSRF_SECRET}

# 安全配置
TRUSTED_IPS=10.0.0.0/8,**********/12,***********/16

# 监控配置
MONITORING_ENABLED=true
LOG_LEVEL=info

# 文件上传配置
UPLOAD_MAX_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# 备份配置
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=90
EOF

    # 创建.env.secrets文件
    cat > "$PROJECT_ROOT/.env.secrets" <<EOF
# 敏感信息 - 请妥善保管
DB_PASSWORD=${DB_PASSWORD}
REDIS_PASSWORD=${REDIS_PASSWORD}
NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
CSRF_SECRET=${CSRF_SECRET}
ADMIN_CSRF_SECRET=${ADMIN_CSRF_SECRET}
EOF

    # 设置文件权限
    chmod 600 "$PROJECT_ROOT/.env.secrets"
    chmod 644 "$PROJECT_ROOT/.env.production"
    chown $SUDO_USER:$SUDO_USER "$PROJECT_ROOT/.env.production"
    chown $SUDO_USER:$SUDO_USER "$PROJECT_ROOT/.env.secrets"
    
    log_info "环境配置文件创建完成"
    log_warn "请妥善保管 .env.secrets 文件"
}

# 创建Docker Compose配置
create_docker_compose_config() {
    log_step "创建Docker Compose配置..."
    
    PROJECT_ROOT="/opt/erp-system"
    
    cat > "$PROJECT_ROOT/docker-compose.yml" <<'EOF'
version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: ./app
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
      - .env.secrets
    depends_on:
      - postgres
      - redis
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=erp_prod
      - POSTGRES_USER=erp_user
    env_file:
      - .env.secrets
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_user -d erp_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    env_file:
      - .env.secrets
    volumes:
      - ./data/redis:/data
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
EOF

    chown $SUDO_USER:$SUDO_USER "$PROJECT_ROOT/docker-compose.yml"
    
    log_info "Docker Compose配置创建完成"
}

# 创建Nginx配置
create_nginx_config() {
    log_step "创建Nginx配置..."
    
    PROJECT_ROOT="/opt/erp-system"
    NGINX_DIR="$PROJECT_ROOT/nginx"
    
    cat > "$NGINX_DIR/nginx.conf" <<'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss;

    # 上游服务器
    upstream app_backend {
        least_conn;
        server app:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

    # HTTP服务器 (重定向到HTTPS)
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS服务器
    server {
        listen 443 ssl http2;
        server_name _;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头部
        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header Referrer-Policy strict-origin-when-cross-origin always;

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API限流
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 登录限流
        location /api/auth/ {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            access_log off;
            proxy_pass http://app_backend/api/health;
        }

        # 主要代理
        location / {
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
EOF

    chown $SUDO_USER:$SUDO_USER "$NGINX_DIR/nginx.conf"
    
    log_info "Nginx配置创建完成"
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."
    
    PROJECT_ROOT="/opt/erp-system"
    SCRIPTS_DIR="$PROJECT_ROOT/scripts"
    
    # 启动脚本
    cat > "$SCRIPTS_DIR/start.sh" <<'EOF'
#!/bin/bash
set -e

echo "启动ERP系统..."
cd /opt/erp-system
docker-compose up -d

echo "等待服务启动..."
sleep 30

# 健康检查
if curl -f -k https://localhost/health; then
    echo "系统启动成功!"
else
    echo "系统启动失败，请检查日志"
    exit 1
fi
EOF

    # 停止脚本
    cat > "$SCRIPTS_DIR/stop.sh" <<'EOF'
#!/bin/bash
set -e

echo "停止ERP系统..."
cd /opt/erp-system
docker-compose down

echo "系统已停止"
EOF

    # 备份脚本
    cat > "$SCRIPTS_DIR/backup.sh" <<'EOF'
#!/bin/bash
set -e

BACKUP_DIR="/opt/erp-system/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "开始数据库备份..."

# 创建备份
docker-compose exec -T postgres pg_dump -U erp_user -d erp_prod --format=custom --compress=9 > "$BACKUP_DIR/backup_$DATE.dump"

# 压缩备份
gzip "$BACKUP_DIR/backup_$DATE.dump"

# 清理旧备份 (保留7天)
find "$BACKUP_DIR" -name "*.gz" -mtime +7 -delete

echo "备份完成: backup_$DATE.dump.gz"
EOF

    # 日志查看脚本
    cat > "$SCRIPTS_DIR/logs.sh" <<'EOF'
#!/bin/bash

cd /opt/erp-system

case ${1:-all} in
    app)
        docker-compose logs -f app
        ;;
    db|postgres)
        docker-compose logs -f postgres
        ;;
    redis)
        docker-compose logs -f redis
        ;;
    nginx)
        docker-compose logs -f nginx
        ;;
    *)
        docker-compose logs -f
        ;;
esac
EOF

    # 设置执行权限
    chmod +x "$SCRIPTS_DIR"/*.sh
    chown -R $SUDO_USER:$SUDO_USER "$SCRIPTS_DIR"
    
    log_info "管理脚本创建完成"
}

# 安装系统服务
install_system_service() {
    log_step "安装系统服务..."
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/erp-system.service <<EOF
[Unit]
Description=ERP System
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/erp-system
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable erp-system.service
    
    log_info "系统服务安装完成"
}

# 创建定时任务
create_cron_jobs() {
    log_step "创建定时任务..."
    
    # 创建备份定时任务 (每天凌晨2点)
    echo "0 2 * * * /opt/erp-system/scripts/backup.sh >> /opt/erp-system/logs/backup.log 2>&1" | crontab -u $SUDO_USER -
    
    # 创建日志清理任务 (每周日凌晨3点)
    echo "0 3 * * 0 find /opt/erp-system/logs -name '*.log' -mtime +30 -delete" | crontab -u $SUDO_USER -
    
    log_info "定时任务创建完成"
}

# 显示安装完成信息
show_completion_info() {
    log_step "安装完成!"
    
    echo
    echo "=================================="
    echo "  ERP系统安装完成"
    echo "=================================="
    echo
    echo "项目路径: /opt/erp-system"
    echo "访问地址: https://localhost"
    echo
    echo "管理命令:"
    echo "  启动系统: /opt/erp-system/scripts/start.sh"
    echo "  停止系统: /opt/erp-system/scripts/stop.sh"
    echo "  查看日志: /opt/erp-system/scripts/logs.sh"
    echo "  数据备份: /opt/erp-system/scripts/backup.sh"
    echo
    echo "系统服务:"
    echo "  启动: systemctl start erp-system"
    echo "  停止: systemctl stop erp-system"
    echo "  状态: systemctl status erp-system"
    echo
    echo "重要文件:"
    echo "  配置文件: /opt/erp-system/.env.production"
    echo "  密钥文件: /opt/erp-system/.env.secrets (请妥善保管)"
    echo "  SSL证书: /opt/erp-system/ssl/"
    echo
    echo "下一步:"
    echo "1. 将应用代码部署到 /opt/erp-system/app/"
    echo "2. 运行数据库迁移"
    echo "3. 启动系统: /opt/erp-system/scripts/start.sh"
    echo "4. 访问 https://localhost 验证安装"
    echo
    log_warn "请妥善保管 /opt/erp-system/.env.secrets 文件中的密钥信息"
}

# 主函数
main() {
    echo "========================================"
    echo "  ERP系统生产环境自动化安装脚本"
    echo "========================================"
    echo
    
    # 检查权限
    check_root
    
    # 检查系统要求
    check_system_requirements
    
    # 安装依赖
    install_system_dependencies
    install_docker
    install_docker_compose
    install_nodejs
    
    # 配置系统
    configure_firewall
    create_project_structure
    generate_ssl_certificates
    create_environment_config
    create_docker_compose_config
    create_nginx_config
    create_management_scripts
    install_system_service
    create_cron_jobs
    
    # 显示完成信息
    show_completion_info
}

# 执行主函数
main "$@"