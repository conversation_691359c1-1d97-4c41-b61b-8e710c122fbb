import { NextRequest, NextResponse } from 'next/server'
import { Redis } from 'ioredis'

// 速率限制配置
interface RateLimitConfig {
  windowMs: number  // 时间窗口 (毫秒)
  max: number      // 最大请求数
  message?: string // 自定义错误信息
  keyGenerator?: (req: NextRequest) => string // 自定义key生成器
  skipSuccessfulRequests?: boolean // 是否跳过成功请求
  skipFailedRequests?: boolean    // 是否跳过失败请求
}

// 默认配置
const defaultConfig: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100,                 // 100请求
  message: '请求过于频繁，请稍后再试'
}

// Redis客户端 (生产环境使用)
let redis: Redis | null = null
if (process.env.REDIS_URL) {
  redis = new Redis(process.env.REDIS_URL)
}

// 内存存储 (开发环境使用)
const memoryStore = new Map<string, { count: number; resetTime: number }>()

// 清理过期的内存记录
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of memoryStore.entries()) {
    if (now > value.resetTime) {
      memoryStore.delete(key)
    }
  }
}, 60000) // 每分钟清理一次

export class RateLimiter {
  private config: RateLimitConfig

  constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
  }

  // 生成限制key
  private generateKey(req: NextRequest): string {
    if (this.config.keyGenerator) {
      return this.config.keyGenerator(req)
    }

    // 默认使用IP地址
    const forwarded = req.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : req.headers.get('x-real-ip') || 'unknown'
    
    // 包含路径信息以便对不同端点分别限制
    const path = new URL(req.url).pathname
    return `rate_limit:${ip}:${path}`
  }

  // 使用Redis进行速率限制
  private async checkRateLimitRedis(key: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    if (!redis) {
      throw new Error('Redis not configured')
    }

    const now = Date.now()
    const window = Math.floor(now / this.config.windowMs)
    const redisKey = `${key}:${window}`

    try {
      const current = await redis.incr(redisKey)
      
      if (current === 1) {
        // 设置过期时间
        await redis.expire(redisKey, Math.ceil(this.config.windowMs / 1000))
      }

      const remaining = Math.max(0, this.config.max - current)
      const resetTime = (window + 1) * this.config.windowMs

      return {
        allowed: current <= this.config.max,
        remaining,
        resetTime
      }
    } catch (error) {
      console.error('Redis rate limit error:', error)
      // Redis失败时允许请求通过
      return { allowed: true, remaining: this.config.max, resetTime: now + this.config.windowMs }
    }
  }

  // 使用内存进行速率限制
  private checkRateLimitMemory(key: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now()
    const record = memoryStore.get(key)

    if (!record || now > record.resetTime) {
      // 创建新记录或重置过期记录
      const resetTime = now + this.config.windowMs
      memoryStore.set(key, { count: 1, resetTime })
      return {
        allowed: true,
        remaining: this.config.max - 1,
        resetTime
      }
    }

    record.count++
    const remaining = Math.max(0, this.config.max - record.count)

    return {
      allowed: record.count <= this.config.max,
      remaining,
      resetTime: record.resetTime
    }
  }

  // 检查速率限制
  async checkRateLimit(req: NextRequest): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = this.generateKey(req)

    if (redis) {
      return this.checkRateLimitRedis(key)
    } else {
      return this.checkRateLimitMemory(key)
    }
  }

  // 中间件函数
  middleware() {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      try {
        const { allowed, remaining, resetTime } = await this.checkRateLimit(req)

        // 添加速率限制头部
        const headers = new Headers()
        headers.set('X-RateLimit-Limit', this.config.max.toString())
        headers.set('X-RateLimit-Remaining', remaining.toString())
        headers.set('X-RateLimit-Reset', new Date(resetTime).toISOString())

        if (!allowed) {
          // 请求被限制
          return new NextResponse(
            JSON.stringify({
              error: this.config.message,
              retryAfter: Math.ceil((resetTime - Date.now()) / 1000)
            }),
            {
              status: 429,
              headers: {
                'Content-Type': 'application/json',
                ...Object.fromEntries(headers.entries())
              }
            }
          )
        }

        // 请求被允许，添加头部信息
        const response = NextResponse.next()
        headers.forEach((value, key) => {
          response.headers.set(key, value)
        })

        return response
      } catch (error) {
        console.error('Rate limit middleware error:', error)
        // 出错时允许请求通过
        return NextResponse.next()
      }
    }
  }
}

// 预定义的速率限制器
export const apiRateLimit = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100,                 // 100请求
  message: 'API请求过于频繁，请稍后再试'
})

export const authRateLimit = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5,                   // 5次登录尝试
  message: '登录尝试过于频繁，请15分钟后再试',
  keyGenerator: (req) => {
    const forwarded = req.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : req.headers.get('x-real-ip') || 'unknown'
    return `auth_rate_limit:${ip}`
  }
})

export const uploadRateLimit = new RateLimiter({
  windowMs: 60 * 1000,      // 1分钟
  max: 10,                  // 10次上传
  message: '文件上传过于频繁，请稍后再试'
})

// 严格限制的端点
export const strictRateLimit = new RateLimiter({
  windowMs: 60 * 1000,      // 1分钟
  max: 20,                  // 20请求
  message: '请求过于频繁，请稍后再试'
})

// 高频操作的宽松限制
export const permissiveRateLimit = new RateLimiter({
  windowMs: 60 * 1000,      // 1分钟
  max: 300,                 // 300请求
  message: '请求过于频繁，请稍后再试'
})

// 工具函数：应用速率限制到API路由
export function withRateLimit(rateLimiter: RateLimiter, handler: Function) {
  return async (req: NextRequest, ...args: any[]) => {
    const limitResult = await rateLimiter.middleware()(req)
    
    if (limitResult && limitResult.status === 429) {
      return limitResult
    }

    return handler(req, ...args)
  }
}

// 工具函数：获取客户端IP
export function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : req.headers.get('x-real-ip') || 'unknown'
  return ip
}

// 工具函数：检查是否为可信IP
export function isTrustedIP(ip: string): boolean {
  const trustedIPs = (process.env.TRUSTED_IPS || '').split(',').filter(Boolean)
  return trustedIPs.includes(ip)
}

// 工具函数：基于用户角色的动态限制
export function createRoleBasedRateLimit(baseConfig: Partial<RateLimitConfig> = {}) {
  return new RateLimiter({
    ...defaultConfig,
    ...baseConfig,
    keyGenerator: (req) => {
      // 这里可以根据用户身份调整限制
      // 从请求中获取用户信息（需要在认证中间件后使用）
      const userRole = req.headers.get('x-user-role') || 'guest'
      const ip = getClientIP(req)
      const path = new URL(req.url).pathname
      
      // 根据角色调整key，管理员可以有更高的限制
      return `rate_limit:${userRole}:${ip}:${path}`
    }
  })
}