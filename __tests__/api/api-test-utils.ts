import { NextRequest } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { beforeAll, afterAll, beforeEach, vi, expect } from 'vitest'

// 测试数据库实例
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
    }
  }
})

// 测试用户数据
export const testUsers = {
  admin: {
    id: 'test-admin-id',
    email: '<EMAIL>',
    name: 'Test Admin',
    password: 'hashed-password',
    role: 'ADMIN'
  },
  employee: {
    id: 'test-employee-id',
    email: '<EMAIL>',
    name: 'Test Employee',
    password: 'hashed-password',
    role: 'EMPLOYEE'
  }
}

// 模拟请求创建器
export function createMockRequest(
  method: string,
  url: string,
  body?: any,
  headers?: Record<string, string>
): NextRequest {
  const mockHeaders = new Headers({
    'content-type': 'application/json',
    ...headers
  })

  const mockRequest = {
    method,
    url,
    headers: mockHeaders,
    json: async () => body,
    text: async () => body ? JSON.stringify(body) : '',
    body: body ? JSON.stringify(body) : null
  } as NextRequest

  return mockRequest
}

// 模拟认证会话
export function mockSession(user: any) {
  const mockAuth = {
    getServerSession: vi.fn().mockResolvedValue({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    })
  }
  return mockAuth
}

// 数据库清理和设置
export async function setupTestDatabase() {
  // 清理测试数据
  await testPrisma.auditLog.deleteMany()
  await testPrisma.user.deleteMany()
  await testPrisma.employee.deleteMany()
  await testPrisma.product.deleteMany()
  await testPrisma.productCategory.deleteMany()

  // 创建测试用户
  await testPrisma.user.create({
    data: testUsers.admin
  })
  
  await testPrisma.user.create({
    data: testUsers.employee
  })
}

export async function cleanupTestDatabase() {
  await testPrisma.auditLog.deleteMany()
  await testPrisma.user.deleteMany()
  await testPrisma.employee.deleteMany()
  await testPrisma.product.deleteMany()
  await testPrisma.productCategory.deleteMany()
}

// API响应验证工具
export function expectSuccessResponse(response: any, expectedData?: any) {
  expect(response.status).toBe(200)
  if (expectedData) {
    expect(response.data).toEqual(expect.objectContaining(expectedData))
  }
}

export function expectErrorResponse(response: any, expectedStatus: number, expectedMessage?: string) {
  expect(response.status).toBe(expectedStatus)
  if (expectedMessage) {
    expect(response.error).toContain(expectedMessage)
  }
}

// 权限测试工具
export async function testUnauthorizedAccess(apiHandler: Function, request: NextRequest) {
  const response = await apiHandler(request)
  expect(response.status).toBe(401)
}

export async function testForbiddenAccess(apiHandler: Function, request: NextRequest) {
  const response = await apiHandler(request)
  expect(response.status).toBe(403)
}

// 数据验证工具
export function validateProductData(product: any) {
  expect(product).toHaveProperty('id')
  expect(product).toHaveProperty('name')
  expect(product).toHaveProperty('price')
  expect(product).toHaveProperty('createdAt')
  expect(product).toHaveProperty('updatedAt')
}

export function validateEmployeeData(employee: any) {
  expect(employee).toHaveProperty('id')
  expect(employee).toHaveProperty('name')
  expect(employee).toHaveProperty('email')
  expect(employee).toHaveProperty('department')
}

export function validateFinancialData(transaction: any) {
  expect(transaction).toHaveProperty('id')
  expect(transaction).toHaveProperty('amount')
  expect(transaction).toHaveProperty('type')
  expect(transaction).toHaveProperty('accountId')
}