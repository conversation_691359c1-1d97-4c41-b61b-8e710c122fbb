import '@testing-library/jest-dom'
import { expect, afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// 扩展Vitest的expect方法
expect.extend(matchers)

// 在每个测试后运行清理
afterEach(() => {
  cleanup()
})

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟IntersectionObserver
class MockIntersectionObserver {
  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback
  }
  
  callback: IntersectionObserverCallback
  elements = new Set()
  
  observe(element: Element) {
    this.elements.add(element)
  }
  
  unobserve(element: Element) {
    this.elements.delete(element)
  }
  
  disconnect() {
    this.elements.clear()
  }
  
  trigger(isIntersecting: boolean) {
    const entries: IntersectionObserverEntry[] = []
    
    this.elements.forEach(element => {
      entries.push({
        isIntersecting,
        target: element,
        boundingClientRect: element.getBoundingClientRect(),
        intersectionRatio: isIntersecting ? 1 : 0,
        intersectionRect: isIntersecting ? element.getBoundingClientRect() : new DOMRectReadOnly(),
        rootBounds: null,
        time: Date.now(),
      } as IntersectionObserverEntry)
    })
    
    this.callback(entries, this)
  }
}

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: MockIntersectionObserver,
})

// 模拟ResizeObserver
class MockResizeObserver {
  constructor(callback: ResizeObserverCallback) {
    this.callback = callback
  }
  
  callback: ResizeObserverCallback
  elements = new Set<Element>()
  
  observe(element: Element) {
    this.elements.add(element)
  }
  
  unobserve(element: Element) {
    this.elements.delete(element)
  }
  
  disconnect() {
    this.elements.clear()
  }
}

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: MockResizeObserver,
})

// Mock environment variables for testing
process.env.NEXTAUTH_SECRET = 'test-secret'
process.env.NEXTAUTH_URL = 'http://localhost:3000'
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL

// Global mocks for authentication
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

vi.mock('@/lib/auth', () => ({
  auth: vi.fn(),
  getSession: vi.fn(),
  getServerSession: vi.fn()
}))

vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    refresh: vi.fn()
  })),
  useSearchParams: vi.fn(() => new URLSearchParams()),
  usePathname: vi.fn(() => '/test'),
  notFound: vi.fn()
}))

// Mock toast notifications
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
  useToast: vi.fn(() => ({
    toast: vi.fn(),
    dismiss: vi.fn(),
    toasts: []
  }))
}))

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: vi.fn(({ src, alt, ...props }) => {
    const img = document.createElement('img')
    img.src = src
    img.alt = alt
    Object.assign(img, props)
    return img
  })
}))

// Mock Next.js Link component
vi.mock('next/link', () => ({
  default: vi.fn(({ children, href, ...props }) => {
    const link = document.createElement('a')
    link.href = href
    if (typeof children === 'string') {
      link.textContent = children
    }
    Object.assign(link, props)
    return link
  })
}))
