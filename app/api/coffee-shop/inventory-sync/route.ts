import { NextRequest, NextResponse } from "next/server"
import prisma from "@/lib/db"

// 咖啡店库存同步API
export async function POST(request: NextRequest) {
  try {
    console.log("🔍 咖啡店库存同步API被调用")
    
    const body = await request.json()
    console.log("📝 接收到的库存同步数据:", body)

    const { action, items, reason } = body

    if (!action || !items || !Array.isArray(items)) {
      return NextResponse.json(
        { error: "缺少必填字段：action, items" },
        { status: 400 }
      )
    }

    const result = await prisma.$transaction(async (tx) => {
      const syncResults = []

      for (const item of items) {
        if (!item.productId || item.quantity === undefined) {
          console.warn("⚠️ 跳过无效项目:", item)
          continue
        }

        try {
          // 查找对应的库存记录
          const inventoryItem = await tx.inventoryItem.findFirst({
            where: {
              productId: parseInt(item.productId)
            },
            include: {
              product: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          })

          if (!inventoryItem) {
            syncResults.push({
              productId: item.productId,
              status: 'error',
              message: '未找到产品库存记录'
            })
            continue
          }

          const quantity = parseInt(item.quantity)
          let newQuantity = inventoryItem.quantity

          switch (action) {
            case 'sale':
              // 销售：减少库存
              if (inventoryItem.quantity < quantity) {
                syncResults.push({
                  productId: item.productId,
                  productName: inventoryItem.product.name,
                  status: 'warning',
                  message: `库存不足，当前库存: ${inventoryItem.quantity}，需要: ${quantity}`,
                  currentQuantity: inventoryItem.quantity,
                  requestedQuantity: quantity
                })
                continue
              }
              newQuantity = inventoryItem.quantity - quantity
              break

            case 'restock':
              // 补货：增加库存
              newQuantity = inventoryItem.quantity + quantity
              break

            case 'adjustment':
              // 库存调整：直接设置为指定数量
              newQuantity = quantity
              break

            case 'return':
              // 退货：增加库存
              newQuantity = inventoryItem.quantity + quantity
              break

            default:
              syncResults.push({
                productId: item.productId,
                status: 'error',
                message: `不支持的操作类型: ${action}`
              })
              continue
          }

          // 更新库存
          await tx.inventoryItem.update({
            where: { id: inventoryItem.id },
            data: {
              quantity: newQuantity,
              lastUpdated: new Date()
            }
          })

          // 记录库存变更历史
          await tx.inventoryTransaction.create({
            data: {
              inventoryItemId: inventoryItem.id,
              type: action === 'sale' ? 'OUT' : 'IN',
              quantity: action === 'sale' ? -quantity : quantity,
              reason: reason || `咖啡店${action}`,
              createdAt: new Date()
            }
          })

          syncResults.push({
            productId: item.productId,
            productName: inventoryItem.product.name,
            status: 'success',
            message: `库存同步成功`,
            previousQuantity: inventoryItem.quantity,
            newQuantity: newQuantity,
            changeQuantity: action === 'sale' ? -quantity : quantity
          })

          console.log(`✅ 产品 ${inventoryItem.product.name} 库存同步成功: ${inventoryItem.quantity} -> ${newQuantity}`)

        } catch (itemError) {
          console.error(`❌ 同步产品 ${item.productId} 失败:`, itemError)
          syncResults.push({
            productId: item.productId,
            status: 'error',
            message: `同步失败: ${itemError.message}`
          })
        }
      }

      return syncResults
    })

    const successCount = result.filter(r => r.status === 'success').length
    const errorCount = result.filter(r => r.status === 'error').length
    const warningCount = result.filter(r => r.status === 'warning').length

    console.log(`✅ 库存同步完成: ${successCount} 成功, ${errorCount} 失败, ${warningCount} 警告`)

    return NextResponse.json({
      success: true,
      message: `库存同步完成: ${successCount} 成功, ${errorCount} 失败, ${warningCount} 警告`,
      results: result,
      summary: {
        total: result.length,
        success: successCount,
        error: errorCount,
        warning: warningCount
      }
    })

  } catch (error) {
    console.error("❌ 咖啡店库存同步失败:", error)
    return NextResponse.json(
      { error: "库存同步失败", details: error.message },
      { status: 500 }
    )
  }
}

// 获取咖啡店相关产品的库存状态
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 获取咖啡店库存状态API被调用")
    
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category") || "咖啡相关"

    // 查询咖啡店相关产品的库存
    const inventoryItems = await prisma.inventoryItem.findMany({
      where: {
        product: {
          OR: [
            { category: { contains: category, mode: 'insensitive' } },
            { name: { contains: "咖啡", mode: 'insensitive' } },
            { name: { contains: "饮料", mode: 'insensitive' } },
            { name: { contains: "茶", mode: 'insensitive' } }
          ]
        }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            category: true,
            image: true
          }
        },
        warehouse: {
          select: {
            id: true,
            name: true,
            location: true
          }
        }
      },
      orderBy: {
        product: {
          name: 'asc'
        }
      }
    })

    // 统计库存状态
    const stats = {
      totalProducts: inventoryItems.length,
      totalQuantity: inventoryItems.reduce((sum, item) => sum + item.quantity, 0),
      lowStockItems: inventoryItems.filter(item => 
        item.minStockLevel && item.quantity <= item.minStockLevel
      ).length,
      outOfStockItems: inventoryItems.filter(item => item.quantity === 0).length
    }

    console.log(`✅ 成功获取 ${inventoryItems.length} 个产品的库存状态`)

    return NextResponse.json({
      data: inventoryItems,
      stats: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ 获取咖啡店库存状态失败:", error)
    return NextResponse.json(
      { error: "获取库存状态失败" },
      { status: 500 }
    )
  }
}