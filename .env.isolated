# 独立环境配置文件
# 用于完全独立的Docker环境，避免与其他ERP测试项目冲突

# 数据库配置 - 独立PostgreSQL实例
DATABASE_URL="postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated?schema=public"

# 认证配置 - 独立密钥
NEXTAUTH_URL="http://localhost:3002"
NEXTAUTH_SECRET="linghua-isolated-secret-key-2024"
AUTH_SECRET="linghua-isolated-secret-key-2024"
AUTH_TRUST_HOST="true"

# Redis配置 - 独立Redis实例
REDIS_URL="redis://localhost:6381"

# 应用配置 - 独立端口
NEXT_PUBLIC_APP_URL="http://localhost:3002"
NEXT_PUBLIC_APP_NAME="灵华珐琅馆管理系统 (独立环境)"
NEXT_PUBLIC_APP_VERSION="1.0.0-isolated"

# 环境标识
NODE_ENV="development"
ENVIRONMENT="isolated"

# 邮件配置 (可选，使用测试配置)
EMAIL_SERVER_HOST="smtp.example.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="test-password"
EMAIL_FROM="<EMAIL>"

# Sentry 配置 (可选，独立项目)
NEXT_PUBLIC_SENTRY_DSN=""
NEXT_PUBLIC_SENTRY_ENABLED="false"
SENTRY_AUTH_TOKEN=""
SENTRY_PROJECT="linghua-isolated"
SENTRY_ORG=""

# 数据库管理工具配置
ADMINER_URL="http://localhost:8081"
ADMINER_DEFAULT_SERVER="linghua-isolated-postgres"

# Docker配置
COMPOSE_PROJECT_NAME="linghua-isolated"
COMPOSE_FILE="docker-compose.isolated.yml"

# 开发配置
DEBUG="true"
LOG_LEVEL="debug"

# 文件上传配置
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="10485760"  # 10MB

# 会话配置
SESSION_TIMEOUT="86400"  # 24小时

# 安全配置
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="24h"

# 性能配置
CACHE_TTL="3600"  # 1小时
MAX_CONNECTIONS="50"

# 备份配置
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
BACKUP_RETENTION_DAYS="7"
