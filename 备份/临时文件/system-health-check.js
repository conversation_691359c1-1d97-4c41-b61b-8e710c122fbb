/**
 * 系统健康检查脚本
 */

const http = require('http')

console.log('🔍 开始系统健康检查...\n')

// API端点列表
const apiEndpoints = [
  { name: '系统测试', path: '/api/test' },
  { name: '仪表板统计', path: '/api/dashboard/stats' },
  { name: '财务仪表板', path: '/api/finance/dashboard' },
  { name: '制作管理', path: '/api/production/dashboard' },
  { name: '活动记录', path: '/api/dashboard/activities' },
  { name: '员工绩效', path: '/api/employees/performance' },
  { name: '咖啡店采购', path: '/api/coffee-shop/purchases' }
]

// 页面端点列表
const pageEndpoints = [
  { name: '主页', path: '/' },
  { name: '仪表板', path: '/dashboard' },
  { name: '产品管理', path: '/products' },
  { name: '销售管理', path: '/sales' },
  { name: '财务管理', path: '/finance' },
  { name: '制作管理', path: '/production' },
  { name: '员工管理', path: '/employees' }
]

function checkEndpoint(endpoint, isApi = false) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: endpoint.path,
      method: 'GET',
      timeout: 5000
    }

    const req = http.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        const status = res.statusCode
        const isHealthy = isApi ? (status === 200) : (status === 200 || status === 307)
        
        console.log(`${isHealthy ? '✅' : '❌'} ${endpoint.name}: ${status} ${isHealthy ? '正常' : '异常'}`)
        
        if (isApi && isHealthy && data) {
          try {
            const jsonData = JSON.parse(data)
            if (jsonData.error) {
              console.log(`   ⚠️  API返回错误: ${jsonData.error}`)
            } else {
              console.log(`   📊 数据正常`)
            }
          } catch (e) {
            console.log(`   ⚠️  响应格式异常`)
          }
        }
        
        resolve({ endpoint: endpoint.name, status, healthy: isHealthy })
      })
    })

    req.on('error', (err) => {
      console.log(`❌ ${endpoint.name}: 连接失败 - ${err.message}`)
      resolve({ endpoint: endpoint.name, status: 0, healthy: false })
    })

    req.on('timeout', () => {
      console.log(`❌ ${endpoint.name}: 请求超时`)
      req.destroy()
      resolve({ endpoint: endpoint.name, status: 0, healthy: false })
    })

    req.end()
  })
}

async function runHealthCheck() {
  console.log('📡 检查API端点...')
  const apiResults = []
  for (const endpoint of apiEndpoints) {
    const result = await checkEndpoint(endpoint, true)
    apiResults.push(result)
    await new Promise(resolve => setTimeout(resolve, 500)) // 避免请求过快
  }

  console.log('\n🌐 检查页面端点...')
  const pageResults = []
  for (const endpoint of pageEndpoints) {
    const result = await checkEndpoint(endpoint, false)
    pageResults.push(result)
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // 生成报告
  console.log('\n📋 健康检查报告')
  console.log('=' * 50)
  
  const healthyApis = apiResults.filter(r => r.healthy).length
  const healthyPages = pageResults.filter(r => r.healthy).length
  
  console.log(`API端点: ${healthyApis}/${apiResults.length} 正常`)
  console.log(`页面端点: ${healthyPages}/${pageResults.length} 正常`)
  
  const overallHealth = (healthyApis + healthyPages) / (apiResults.length + pageResults.length)
  console.log(`\n🎯 系统整体健康度: ${(overallHealth * 100).toFixed(1)}%`)
  
  if (overallHealth >= 0.9) {
    console.log('🎉 系统运行状态优秀！')
  } else if (overallHealth >= 0.7) {
    console.log('⚠️  系统运行状态良好，有部分问题需要关注')
  } else {
    console.log('🚨 系统存在较多问题，需要立即处理')
  }
}

runHealthCheck().catch(console.error)
