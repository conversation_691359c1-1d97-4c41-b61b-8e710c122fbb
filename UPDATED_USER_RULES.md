# 更新后的用户规则 - MCP 服务器使用部分

> 此部分可直接替换您现有用户规则中的 "MCP 服务器使用" 章节

## MCP 服务器使用

> 优先使用 MCP 工具以提高效率，确保所有可用服务器都被充分利用。

### **反馈 (Feedback)**
1. 使用 `interactive_feedback` MCP 服务器。
2. 调用 `interactive_feedback` MCP 服务器来完成用户请求。
3. 贯穿所有模式，确保用户参与和确认。

### **文件 (File)**
1. 使用 `filesystem` MCP 服务器。
2. 对于读取文件：必须使用 MCP 服务的 `read_file` 功能。
3. 对于编辑文件：请优先使用 Cursor 的文件编辑工具 (Edit & Reapply)。仅在以下情况发生时才使用 MCP 服务的 `edit_file` 和 `write_file` 功能：
   * 内容过长。
   * 创建新文件。
   * 在单个文件内有许多相似的修改。
   * Cursor 的文件编辑工具失败后。

### **时间 (Time)**
1. 使用 `time` MCP 服务器获取当前时间或日期，时区为 'Asia/Shanghai'。
2. 用于时间转换和计算。

### **官方文档审查 (Official document review)**
1. 使用 `context7` MCP 服务器查找官方文档。
2. 使用 `context7` MCP 服务器查询项目中组件所使用版本的文档，以避免生成的代码与实际使用版本不一致。

### **代码仓库管理 (GitHub Integration)**
1. 使用 `github` MCP 服务器进行代码仓库操作。
2. 优先用于：查看项目 issue 和 PR、创建分支和提交、搜索代码片段、管理项目文档。
3. 在 RESEARCH 模式：查看相关 issue 和 PR 历史。
4. 在 EXECUTE 模式：提交代码更改和创建 PR。

### **知识管理 (Memory Management)**
1. 使用 `memory` MCP 服务器维护项目知识图谱。
2. 优先用于：存储项目架构知识、记录重要设计决策、维护组件关系图、保存调试经验。
3. 在 RESEARCH 模式：查询历史知识和经验。
4. 在 REVIEW 模式：更新知识库。

### **文档协作 (Notion Integration)**
1. 使用 `notion` MCP 服务器管理项目文档。
2. 优先用于：维护需求文档、更新开发进度、管理任务清单、记录会议纪要。
3. 仅在用户明确要求操作 Notion 时使用。

### **浏览器自动化 (Browser Automation)**
1. 使用 `playwright-browser` MCP 服务器进行 Web 测试。
2. 优先用于：UI 功能测试、表单提交测试、响应式设计验证、性能测试。
3. 在 EXECUTE 模式：执行自动化测试。
4. 在 REVIEW 模式：验证功能实现。

### **高级思维 (Sequential Thinking)**
1. 使用 `sequential-thinking` MCP 服务器处理复杂问题。
2. 优先用于：复杂架构设计、性能优化策略、调试复杂问题、多步骤任务规划。
3. 在 INNOVATE 模式：深度分析和方案对比。
4. 在 PLAN 模式：复杂计划的验证。

### **MCP 服务器使用策略**

#### 各模式工具组合：
- **RESEARCH 模式**：`filesystem`（必用）+ `interactive_feedback`（必用）+ `memory`（推荐）+ `github`（推荐）+ `context7`（按需）
- **INNOVATE 模式**：`interactive_feedback`（必用）+ `sequential-thinking`（推荐）+ `memory`（推荐）+ `context7`（按需）
- **PLAN 模式**：`interactive_feedback`（必用）+ `sequential-thinking`（推荐）+ `notion`（按需）+ `memory`（按需）
- **EXECUTE 模式**：`filesystem`（必用）+ `interactive_feedback`（必用）+ `github`（推荐）+ `playwright-browser`（推荐）+ `notion`（按需）
- **REVIEW 模式**：`filesystem`（必用）+ `interactive_feedback`（必用）+ `memory`（推荐）+ `playwright-browser`（推荐）+ `github`（按需）

#### 资源管理原则（针对4GB环境）：
1. 同时活跃 MCP 服务器不超过 3 个。
2. 优先级顺序：`interactive_feedback` > `filesystem` > 其他。
3. 重型工具（`playwright-browser`、`sequential-thinking`）延迟使用。

#### 故障回退机制：
1. `filesystem` MCP 故障 → 回退到 Cursor 原生文件操作
2. `interactive_feedback` MCP 故障 → 直接询问用户
3. 其他 MCP 故障 → 跳过该工具，继续执行

---

## 更新的核心指令

在您现有的核心指令基础上，补充第4条：

4. **MCP 工具最大化利用**：
   * 根据任务类型智能选择 MCP 服务器组合。
   * 优先使用 MCP 工具而非传统方法。
   * 遵循资源管理和性能优化原则。
   * 建立故障回退机制确保任务连续性。