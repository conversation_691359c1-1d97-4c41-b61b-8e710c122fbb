import { NextRequest, NextResponse } from "next/server"
import { staticCache } from "@/lib/cache/cache-instances"

interface WebVitalsMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType: string
  url: string
  userAgent: string
  timestamp: number
}

// 简单的内存存储（生产环境应使用数据库）
const metricsStore = new Map<string, WebVitalsMetric[]>()

export async function POST(request: NextRequest) {
  try {
    const metric: WebVitalsMetric = await request.json()
    
    // 验证数据
    if (!metric.name || typeof metric.value !== 'number') {
      return NextResponse.json({ error: 'Invalid metric data' }, { status: 400 })
    }

    // 存储指标
    const key = `metrics-${new Date().toISOString().split('T')[0]}` // 按日期分组
    const existingMetrics = metricsStore.get(key) || []
    existingMetrics.push(metric)
    metricsStore.set(key, existingMetrics)

    // 也缓存到静态缓存中
    staticCache.set(`web-vitals-${metric.id}`, metric)

    // 记录到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Web Vitals] ${metric.name}: ${metric.value} (${metric.rating})`)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error storing web vitals metric:', error)
    return NextResponse.json({ error: 'Failed to store metric' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const date = url.searchParams.get('date') || new Date().toISOString().split('T')[0]
    const metric = url.searchParams.get('metric')
    
    const key = `metrics-${date}`
    const metrics = metricsStore.get(key) || []
    
    // 过滤特定指标
    const filteredMetrics = metric 
      ? metrics.filter(m => m.name === metric)
      : metrics

    // 计算统计信息
    const stats = calculateStats(filteredMetrics)

    return NextResponse.json({
      date,
      totalMetrics: filteredMetrics.length,
      metrics: filteredMetrics,
      stats
    })
  } catch (error) {
    console.error('Error retrieving web vitals metrics:', error)
    return NextResponse.json({ error: 'Failed to retrieve metrics' }, { status: 500 })
  }
}

function calculateStats(metrics: WebVitalsMetric[]) {
  if (metrics.length === 0) return null

  const grouped = metrics.reduce((acc, metric) => {
    if (!acc[metric.name]) {
      acc[metric.name] = []
    }
    acc[metric.name].push(metric.value)
    return acc
  }, {} as Record<string, number[]>)

  const stats: Record<string, any> = {}

  Object.entries(grouped).forEach(([name, values]) => {
    const sorted = values.sort((a, b) => a - b)
    const sum = values.reduce((a, b) => a + b, 0)
    
    stats[name] = {
      count: values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      mean: sum / values.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p75: sorted[Math.floor(sorted.length * 0.75)],
      p90: sorted[Math.floor(sorted.length * 0.9)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    }
  })

  return stats
}