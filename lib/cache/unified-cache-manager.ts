/**
 * 统一缓存管理器
 * 整合项目中分散的缓存系统，提供统一的缓存接口
 */

interface CacheOptions {
  ttl?: number // 生存时间 (毫秒)
  maxSize?: number // 最大缓存条目数
  strategy?: 'LRU' | 'LFU' | 'FIFO'
  persistent?: boolean // 是否持久化
  compression?: boolean // 是否压缩
}

interface CacheItem<T> {
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

interface CacheStats {
  size: number
  maxSize: number
  hitCount: number
  missCount: number
  hitRate: number
  memoryUsage: number
}

export class UnifiedCacheManager {
  private caches = new Map<string, Map<string, CacheItem<any>>>()
  private cacheOptions = new Map<string, Required<CacheOptions>>()
  private stats = new Map<string, CacheStats>()

  /**
   * 获取或创建缓存实例
   */
  getCache(name: string, options: CacheOptions = {}) {
    if (!this.caches.has(name)) {
      this.createCache(name, options)
    }
    return {
      get: <T>(key: string): T | null => this.get<T>(name, key),
      set: <T>(key: string, value: T, ttl?: number): void => this.set(name, key, value, ttl),
      delete: (key: string): boolean => this.delete(name, key),
      clear: (): void => this.clear(name),
      has: (key: string): boolean => this.has(name, key),
      getStats: (): CacheStats => this.getStats(name),
      keys: (): string[] => this.keys(name),
      size: (): number => this.size(name)
    }
  }

  /**
   * 创建新的缓存实例
   */
  private createCache(name: string, options: CacheOptions) {
    const defaultOptions: Required<CacheOptions> = {
      ttl: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      strategy: 'LRU',
      persistent: false,
      compression: false
    }

    this.caches.set(name, new Map())
    this.cacheOptions.set(name, { ...defaultOptions, ...options })
    this.stats.set(name, {
      size: 0,
      maxSize: options.maxSize || defaultOptions.maxSize,
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      memoryUsage: 0
    })
  }

  /**
   * 获取缓存值
   */
  private get<T>(cacheName: string, key: string): T | null {
    const cache = this.caches.get(cacheName)
    const stats = this.stats.get(cacheName)!
    
    if (!cache || !cache.has(key)) {
      stats.missCount++
      this.updateHitRate(cacheName)
      return null
    }

    const item = cache.get(key)!
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      cache.delete(key)
      stats.size--
      stats.missCount++
      this.updateHitRate(cacheName)
      return null
    }

    // 更新访问统计
    item.accessCount++
    item.lastAccessed = Date.now()
    stats.hitCount++
    this.updateHitRate(cacheName)

    return item.value
  }

  /**
   * 设置缓存值
   */
  private set<T>(cacheName: string, key: string, value: T, customTtl?: number): void {
    const cache = this.caches.get(cacheName)!
    const options = this.cacheOptions.get(cacheName)!
    const stats = this.stats.get(cacheName)!

    // 检查是否需要清理空间
    if (cache.size >= options.maxSize && !cache.has(key)) {
      this.evict(cacheName)
    }

    const ttl = customTtl || options.ttl
    const item: CacheItem<T> = {
      value,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      lastAccessed: Date.now()
    }

    const isNewKey = !cache.has(key)
    cache.set(key, item)
    
    if (isNewKey) {
      stats.size++
    }

    this.updateMemoryUsage(cacheName)
  }

  /**
   * 删除缓存项
   */
  private delete(cacheName: string, key: string): boolean {
    const cache = this.caches.get(cacheName)!
    const stats = this.stats.get(cacheName)!
    
    const deleted = cache.delete(key)
    if (deleted) {
      stats.size--
      this.updateMemoryUsage(cacheName)
    }
    
    return deleted
  }

  /**
   * 清空缓存
   */
  private clear(cacheName: string): void {
    const cache = this.caches.get(cacheName)!
    const stats = this.stats.get(cacheName)!
    
    cache.clear()
    stats.size = 0
    stats.hitCount = 0
    stats.missCount = 0
    stats.hitRate = 0
    stats.memoryUsage = 0
  }

  /**
   * 检查缓存项是否存在
   */
  private has(cacheName: string, key: string): boolean {
    const cache = this.caches.get(cacheName)!
    if (!cache.has(key)) return false

    const item = cache.get(key)!
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      cache.delete(key)
      this.stats.get(cacheName)!.size--
      return false
    }

    return true
  }

  /**
   * 获取缓存统计
   */
  private getStats(cacheName: string): CacheStats {
    return { ...this.stats.get(cacheName)! }
  }

  /**
   * 获取所有缓存键
   */
  private keys(cacheName: string): string[] {
    const cache = this.caches.get(cacheName)!
    return Array.from(cache.keys())
  }

  /**
   * 获取缓存大小
   */
  private size(cacheName: string): number {
    return this.caches.get(cacheName)!.size
  }

  /**
   * 缓存淘汰策略
   */
  private evict(cacheName: string): void {
    const cache = this.caches.get(cacheName)!
    const options = this.cacheOptions.get(cacheName)!
    
    if (cache.size === 0) return

    let keyToEvict: string | null = null

    switch (options.strategy) {
      case 'LRU': // 最近最少使用
        keyToEvict = this.findLRUKey(cache)
        break
      case 'LFU': // 最少使用频率
        keyToEvict = this.findLFUKey(cache)
        break
      case 'FIFO': // 先进先出
        keyToEvict = this.findFIFOKey(cache)
        break
    }

    if (keyToEvict) {
      this.delete(cacheName, keyToEvict)
    }
  }

  /**
   * 查找LRU键
   */
  private findLRUKey(cache: Map<string, CacheItem<any>>): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, item] of cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * 查找LFU键
   */
  private findLFUKey(cache: Map<string, CacheItem<any>>): string | null {
    let leastUsedKey: string | null = null
    let leastCount = Infinity

    for (const [key, item] of cache.entries()) {
      if (item.accessCount < leastCount) {
        leastCount = item.accessCount
        leastUsedKey = key
      }
    }

    return leastUsedKey
  }

  /**
   * 查找FIFO键
   */
  private findFIFOKey(cache: Map<string, CacheItem<any>>): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, item] of cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * 更新命中率
   */
  private updateHitRate(cacheName: string): void {
    const stats = this.stats.get(cacheName)!
    const total = stats.hitCount + stats.missCount
    stats.hitRate = total > 0 ? stats.hitCount / total : 0
  }

  /**
   * 更新内存使用统计
   */
  private updateMemoryUsage(cacheName: string): void {
    const cache = this.caches.get(cacheName)!
    const stats = this.stats.get(cacheName)!
    
    // 简单估算内存使用 (实际应该更精确)
    let memoryUsage = 0
    for (const [key, item] of cache.entries()) {
      memoryUsage += key.length * 2 // 字符串大小估算
      memoryUsage += JSON.stringify(item.value).length * 2 // 值大小估算
    }
    
    stats.memoryUsage = memoryUsage
  }

  /**
   * 清理过期项
   */
  cleanupExpired(): void {
    for (const [cacheName, cache] of this.caches.entries()) {
      const keysToDelete: string[] = []
      
      for (const [key, item] of cache.entries()) {
        if (Date.now() - item.timestamp > item.ttl) {
          keysToDelete.push(key)
        }
      }
      
      keysToDelete.forEach(key => this.delete(cacheName, key))
    }
  }

  /**
   * 获取所有缓存统计
   */
  getAllStats(): Record<string, CacheStats> {
    const allStats: Record<string, CacheStats> = {}
    for (const [name, stats] of this.stats.entries()) {
      allStats[name] = { ...stats }
    }
    return allStats
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    for (const cacheName of this.caches.keys()) {
      this.clear(cacheName)
    }
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new UnifiedCacheManager()

// 定期清理过期项
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheManager.cleanupExpired()
  }, 60000) // 每分钟清理一次
}