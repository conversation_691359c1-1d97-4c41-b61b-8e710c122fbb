{"summary": {"timestamp": "2025-06-06T00:06:33.400Z", "totalIssues": 74, "breakdown": {"critical": 0, "high": 1, "medium": 73}, "riskLevel": "MEDIUM", "estimatedFixTime": {"hours": 38.5, "days": 5, "breakdown": {"critical": "0小时", "high": "2小时", "medium": "36.5小时"}}}, "prioritizedIssues": {"byType": {"frontend": [{"type": "frontend", "issue": "对象显示问题", "description": "组件中可能存在对象直接显示的问题", "file": "./components/product/product-list.tsx", "fix": "检查对象属性访问，确保正确提取显示值", "priority": "high"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-employee-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-production-base-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/artwork-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/unit-material-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/backup-restore.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-invoice-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-settlement-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-inventory.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-sales.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop-import-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/customers/new-customer-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/dashboard/quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/data-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/employees/mobile-employees-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/examples/error-handling-example.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/export-import-buttons.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/accounts-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/categories-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-accounts-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-categories-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/transaction-filter-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/header/help-center.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-help-guide.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-transfer-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile/mobile-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-dashboard-card.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-nav.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mode-toggle.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-header.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/dashboard-layout-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/favorites-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-category-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/create-production-order-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/production-filters.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase/excel-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase-receive-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/quick-action-modals.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/salary-detail-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-stats.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-template-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-filter.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/company-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/create-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/general-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/mobile-settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/notification-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/permission-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/restore-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/system-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/users-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sidebar.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sortable-image-grid.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/todos/new-todo-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/ui/date-picker.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/role-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/user-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-instance-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-step-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}]}, "byFile": {"./components/product": [{"type": "frontend", "issue": "对象显示问题", "description": "组件中可能存在对象直接显示的问题", "file": "./components/product/product-list.tsx", "fix": "检查对象属性访问，确保正确提取显示值", "priority": "high"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-category-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-employee-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-production-base-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/backup-restore.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop-import-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/data-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/export-import-buttons.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-dashboard-card.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-nav.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mode-toggle.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-header.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase-receive-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/quick-action-modals.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/salary-detail-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-stats.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-template-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sidebar.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sortable-image-grid.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/artwork": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/artwork-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/unit-material-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/channel": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-invoice-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-settlement-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/coffee-shop": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-inventory.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-sales.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/customers": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/customers/new-customer-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/dashboard": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/dashboard/quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/employees": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/employees/mobile-employees-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/examples": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/examples/error-handling-example.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/finance": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/accounts-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/categories-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-accounts-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-categories-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/transaction-filter-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/header": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/header/help-center.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/inventory": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-help-guide.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-transfer-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/mobile": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile/mobile-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/personalization": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/dashboard-layout-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/favorites-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/production": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/create-production-order-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/production-filters.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/purchase": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase/excel-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/settings": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-filter.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/company-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/create-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/general-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/mobile-settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/notification-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/permission-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/restore-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/system-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/users-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/todos": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/todos/new-todo-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/ui": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/ui/date-picker.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/user": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/role-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/user-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}], "./components/workflows": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-instance-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-step-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步", "priority": "medium"}]}, "topPriority": [{"type": "frontend", "issue": "对象显示问题", "description": "组件中可能存在对象直接显示的问题", "file": "./components/product/product-list.tsx", "fix": "检查对象属性访问，确保正确提取显示值", "priority": "high"}]}, "fixPlan": {"phase1": {"name": "紧急修复阶段", "duration": "立即执行", "issues": [{"type": "frontend", "issue": "对象显示问题", "description": "组件中可能存在对象直接显示的问题", "file": "./components/product/product-list.tsx", "fix": "检查对象属性访问，确保正确提取显示值"}], "actions": ["修复对象显示问题", "解决关键组件状态同步", "添加必要的错误处理"]}, "phase2": {"name": "系统优化阶段", "duration": "1-2周", "issues": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-employee-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-production-base-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/artwork-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork/unit-material-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/artwork-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/backup-restore.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-invoice-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/channel/channel-settlement-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-inventory.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop/coffee-shop-sales.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/coffee-shop-import-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/customers/new-customer-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/dashboard/quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/data-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/employees/mobile-employees-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/examples/error-handling-example.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/export-import-buttons.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/account-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}], "actions": ["完善组件状态管理", "优化API错误处理", "改进用户体验"]}, "phase3": {"name": "质量提升阶段", "duration": "2-4周", "issues": [{"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/accounts-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/categories-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/category-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-accounts-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/finance-categories-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/finance/transaction-filter-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/header/help-center.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-help-guide.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/inventory/inventory-transfer-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile/mobile-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-dashboard-card.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mobile-nav.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/mode-toggle.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-header.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-quick-actions.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/modern-table.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/dashboard-layout-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/personalization/favorites-manager.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-category-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product/product-import-export.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/product-management.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/create-production-order-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/production/production-filters.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase/excel-import-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/purchase-receive-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/quick-action-modals.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/salary-detail-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-stats.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/schedule-template-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/add-user-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/audit-log-filter.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/company-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/create-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/general-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/mobile-settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/notification-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/permission-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/restore-backup-dialog.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/settings-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/system-settings.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/settings/users-page.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sidebar.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/sortable-image-grid.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/todos/new-todo-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/ui/date-picker.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/role-form.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/user/user-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-instance-detail.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}, {"type": "frontend", "issue": "可能的状态同步问题", "description": "组件使用了useState但没有useEffect，可能存在状态同步问题", "file": "./components/workflows/workflow-step-list.tsx", "fix": "检查是否需要添加useEffect来处理状态同步"}], "actions": ["代码重构和优化", "增加测试覆盖", "性能优化"]}}, "recommendations": [{"category": "前端架构", "priority": "high", "suggestion": "考虑重构前端状态管理架构，使用Context API或Redux统一管理状态", "impact": "减少70%的状态同步问题"}, {"category": "开发流程", "priority": "high", "suggestion": "集成BMAD持续监控，建立自动化质量检查流程", "impact": "预防90%的常见问题，提高开发效率"}, {"category": "测试策略", "priority": "medium", "suggestion": "增加组件单元测试和API集成测试", "impact": "提前发现问题，减少生产环境BUG"}]}