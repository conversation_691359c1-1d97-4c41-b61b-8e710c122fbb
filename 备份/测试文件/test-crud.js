const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testCRUD() {
  try {
    console.log('🔄 测试数据库CRUD操作...\n')

    // 1. 创建测试数据
    console.log('1. 创建测试产品分类...')
    const category = await prisma.productCategory.create({
      data: {
        name: '测试分类',
        description: '这是一个测试分类'
      }
    })
    console.log(`✅ 创建成功，ID: ${category.id}`)

    // 2. 创建测试产品
    console.log('\n2. 创建测试产品...')
    const product = await prisma.product.create({
      data: {
        name: '测试珐琅产品',
        price: 299.99,
        commissionRate: 0.15,
        type: 'product',
        description: '这是一个测试产品',
        categoryId: category.id,
        material: '珐琅',
        unit: '件'
      }
    })
    console.log(`✅ 创建成功，ID: ${product.id}`)

    // 3. 查询数据
    console.log('\n3. 查询所有产品...')
    const products = await prisma.product.findMany({
      include: {
        productCategory: true
      }
    })
    console.log(`✅ 查询成功，共找到 ${products.length} 个产品`)
    products.forEach(p => {
      console.log(`   - ${p.name} (分类: ${p.productCategory?.name || '无'})`)
    })

    // 4. 更新数据
    console.log('\n4. 更新产品价格...')
    const updatedProduct = await prisma.product.update({
      where: { id: product.id },
      data: { price: 399.99 }
    })
    console.log(`✅ 更新成功，新价格: ${updatedProduct.price}`)

    // 5. 删除测试数据
    console.log('\n5. 清理测试数据...')
    await prisma.product.delete({ where: { id: product.id } })
    await prisma.productCategory.delete({ where: { id: category.id } })
    console.log('✅ 清理完成')

    console.log('\n🎉 所有CRUD操作测试通过！')

  } catch (error) {
    console.error('❌ 测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testCRUD()
