"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  DollarSignIcon, 
  CalendarIcon, 
  TrendingUpIcon,
  FileTextIcon
} from "lucide-react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

interface EmployeeSalaryProps {
  employeeId: number
}

export function EmployeeSalary({ employeeId }: EmployeeSalaryProps) {
  const [salaryRecords, setSalaryRecords] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadSalaryRecords = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/employees/${employeeId}/salary`)
        if (response.ok) {
          const data = await response.json()
          setSalaryRecords(data)
        } else {
          // 如果API不存在，使用模拟数据
          setSalaryRecords([
            {
              id: 1,
              month: "2024-03",
              baseSalary: 5000,
              performanceSalary: 1000,
              bonus: 500,
              deductions: 200,
              totalSalary: 6300,
              workDays: 22,
              status: "paid",
              paidAt: new Date().toISOString(),
              createdAt: new Date().toISOString()
            }
          ])
        }
      } catch (error) {
        console.error('加载薪资数据失败:', error)
        setSalaryRecords([])
      } finally {
        setIsLoading(false)
      }
    }
    
    if (employeeId) {
      loadSalaryRecords()
    }
  }, [employeeId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "draft":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "paid":
        return "已发放"
      case "pending":
        return "待发放"
      case "draft":
        return "草稿"
      default:
        return "未知"
    }
  }

  const totalSalary = salaryRecords.reduce((sum, record) => sum + record.totalSalary, 0)
  const avgSalary = salaryRecords.length > 0 ? totalSalary / salaryRecords.length : 0

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 薪资统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSignIcon className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">累计薪资</p>
                <p className="text-lg font-bold">¥{totalSalary.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUpIcon className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">平均月薪</p>
                <p className="text-lg font-bold">¥{avgSalary.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileTextIcon className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">记录数</p>
                <p className="text-lg font-bold">{salaryRecords.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 薪资记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSignIcon className="h-5 w-5" />
            薪资记录
          </CardTitle>
          <CardDescription>
            员工历史薪资发放记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {salaryRecords.length === 0 ? (
            <div className="text-center py-8">
              <DollarSignIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">暂无薪资记录</p>
            </div>
          ) : (
            <div className="space-y-4">
              {salaryRecords.map((record) => (
                <Card key={record.id} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-medium">
                          {format(new Date(record.month), "yyyy年MM月", { locale: zhCN })}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          工作天数: {record.workDays}天
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <p className="text-lg font-bold text-green-600">
                          ¥{record.totalSalary.toFixed(2)}
                        </p>
                        <Badge className={getStatusColor(record.status)}>
                          {getStatusText(record.status)}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">基本工资: </span>
                        <span className="font-medium">¥{record.baseSalary.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">绩效工资: </span>
                        <span className="font-medium">¥{record.performanceSalary.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">奖金: </span>
                        <span className="font-medium">¥{record.bonus.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">扣除: </span>
                        <span className="font-medium">¥{record.deductions.toFixed(2)}</span>
                      </div>
                    </div>
                    
                    {record.status === "paid" && record.paidAt && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        发放时间: {format(new Date(record.paidAt), "yyyy-MM-dd HH:mm", { locale: zhCN })}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}