import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import { GET, POST } from '@/app/api/finance/transactions/route'
import { GET as getAccounts, POST as createAccount } from '@/app/api/finance/accounts/route'
import { 
  testPrisma, 
  createMockRequest, 
  mockSession, 
  setupTestDatabase,
  cleanupTestDatabase,
  validateFinancialData,
  testUsers
} from './api-test-utils'

// Mock NextAuth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

describe('Finance API', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
    await testPrisma.$disconnect()
  })

  beforeEach(async () => {
    // 清理财务数据
    await testPrisma.financialTransaction.deleteMany()
    await testPrisma.financialAccount.deleteMany()
    await testPrisma.financialCategory.deleteMany()

    // 创建测试账户分类
    await testPrisma.financialCategory.createMany({
      data: [
        { id: 'income', name: '收入', type: 'INCOME' },
        { id: 'expense', name: '支出', type: 'EXPENSE' },
        { id: 'asset', name: '资产', type: 'ASSET' }
      ]
    })

    // 创建测试账户
    await testPrisma.financialAccount.createMany({
      data: [
        {
          id: 'cash-account',
          name: '现金账户',
          type: 'CASH',
          balance: 10000,
          categoryId: 'asset'
        },
        {
          id: 'bank-account',
          name: '银行账户',
          type: 'BANK',
          balance: 50000,
          categoryId: 'asset'
        },
        {
          id: 'income-account',
          name: '销售收入',
          type: 'INCOME',
          balance: 0,
          categoryId: 'income'
        }
      ]
    })
  })

  describe('GET /api/finance/accounts', () => {
    it('应该返回财务账户列表', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/accounts')
      const response = await getAccounts(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data).toHaveLength(3)
      
      const cashAccount = data.find((account: any) => account.id === 'cash-account')
      expect(cashAccount).toBeTruthy()
      expect(cashAccount.name).toBe('现金账户')
      expect(cashAccount.balance).toBe(10000)
    })

    it('应该支持按类型筛选账户', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/accounts?type=CASH')
      const response = await getAccounts(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveLength(1)
      expect(data[0].type).toBe('CASH')
    })

    it('未授权用户不应该访问财务账户', async () => {
      const request = createMockRequest('GET', '/api/finance/accounts')
      const response = await getAccounts(request)
      
      expect(response.status).toBe(401)
    })
  })

  describe('POST /api/finance/accounts', () => {
    it('应该创建新的财务账户', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const accountData = {
        name: '新账户',
        type: 'BANK',
        balance: 20000,
        categoryId: 'asset',
        description: '新建银行账户'
      }

      const request = createMockRequest('POST', '/api/finance/accounts', accountData)
      const response = await createAccount(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.name).toBe(accountData.name)
      expect(data.type).toBe(accountData.type)
      expect(data.balance).toBe(accountData.balance)

      // 验证数据库中是否真的创建了账户
      const dbAccount = await testPrisma.financialAccount.findUnique({
        where: { id: data.id }
      })
      expect(dbAccount).toBeTruthy()
    })

    it('应该验证账户名称唯一性', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const duplicateData = {
        name: '现金账户', // 已存在的账户名
        type: 'CASH',
        balance: 5000,
        categoryId: 'asset'
      }

      const request = createMockRequest('POST', '/api/finance/accounts', duplicateData)
      const response = await createAccount(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('账户名称')
    })

    it('应该验证余额不能为负数', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidData = {
        name: '负余额账户',
        type: 'BANK',
        balance: -1000, // 负余额
        categoryId: 'asset'
      }

      const request = createMockRequest('POST', '/api/finance/accounts', invalidData)
      const response = await createAccount(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('余额')
    })
  })

  describe('GET /api/finance/transactions', () => {
    beforeEach(async () => {
      // 创建测试交易记录
      await testPrisma.financialTransaction.createMany({
        data: [
          {
            amount: 1000,
            type: 'INCOME',
            description: '产品销售收入',
            fromAccountId: null,
            toAccountId: 'income-account',
            transactionDate: new Date('2023-06-01')
          },
          {
            amount: 500,
            type: 'EXPENSE',
            description: '办公用品采购',
            fromAccountId: 'cash-account',
            toAccountId: null,
            transactionDate: new Date('2023-06-02')
          },
          {
            amount: 2000,
            type: 'TRANSFER',
            description: '资金转账',
            fromAccountId: 'bank-account',
            toAccountId: 'cash-account',
            transactionDate: new Date('2023-06-03')
          }
        ]
      })
    })

    it('应该返回财务交易列表', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/transactions')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data).toHaveLength(3)
      
      data.forEach((transaction: any) => {
        validateFinancialData(transaction)
      })
    })

    it('应该支持按类型筛选交易', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/transactions?type=INCOME')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveLength(1)
      expect(data[0].type).toBe('INCOME')
    })

    it('应该支持日期范围筛选', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/transactions?startDate=2023-06-01&endDate=2023-06-02')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveLength(2) // 6月1日和6月2日的交易
    })

    it('应该支持分页', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('GET', '/api/finance/transactions?page=1&limit=2')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.transactions).toHaveLength(2)
      expect(data.pagination.total).toBe(3)
      expect(data.pagination.page).toBe(1)
    })
  })

  describe('POST /api/finance/transactions', () => {
    it('应该创建收入交易', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const transactionData = {
        amount: 1500,
        type: 'INCOME',
        description: '新订单收入',
        toAccountId: 'income-account',
        transactionDate: '2023-06-15'
      }

      const request = createMockRequest('POST', '/api/finance/transactions', transactionData)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      validateFinancialData(data)
      expect(data.amount).toBe(transactionData.amount)
      expect(data.type).toBe(transactionData.type)

      // 验证账户余额是否更新
      const account = await testPrisma.financialAccount.findUnique({
        where: { id: 'income-account' }
      })
      expect(account?.balance).toBe(1500)
    })

    it('应该创建支出交易', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const transactionData = {
        amount: 800,
        type: 'EXPENSE',
        description: '材料采购',
        fromAccountId: 'cash-account',
        transactionDate: '2023-06-15'
      }

      const request = createMockRequest('POST', '/api/finance/transactions', transactionData)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.amount).toBe(transactionData.amount)
      expect(data.type).toBe(transactionData.type)

      // 验证现金账户余额是否减少
      const account = await testPrisma.financialAccount.findUnique({
        where: { id: 'cash-account' }
      })
      expect(account?.balance).toBe(10000 - 800) // 原始余额10000减去支出800
    })

    it('应该创建转账交易', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const transactionData = {
        amount: 3000,
        type: 'TRANSFER',
        description: '银行转现金',
        fromAccountId: 'bank-account',
        toAccountId: 'cash-account',
        transactionDate: '2023-06-15'
      }

      const request = createMockRequest('POST', '/api/finance/transactions', transactionData)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.amount).toBe(transactionData.amount)
      expect(data.type).toBe(transactionData.type)

      // 验证两个账户余额都更新了
      const fromAccount = await testPrisma.financialAccount.findUnique({
        where: { id: 'bank-account' }
      })
      const toAccount = await testPrisma.financialAccount.findUnique({
        where: { id: 'cash-account' }
      })
      
      expect(fromAccount?.balance).toBe(50000 - 3000) // 银行账户减少
      expect(toAccount?.balance).toBe(10000 + 3000) // 现金账户增加
    })

    it('应该验证转账的余额充足', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const insufficientData = {
        amount: 100000, // 超过现金账户余额
        type: 'EXPENSE',
        description: '超额支出',
        fromAccountId: 'cash-account',
        transactionDate: '2023-06-15'
      }

      const request = createMockRequest('POST', '/api/finance/transactions', insufficientData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('余额不足')
    })

    it('应该验证转账必须有源账户和目标账户', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidTransferData = {
        amount: 1000,
        type: 'TRANSFER',
        description: '无效转账',
        fromAccountId: 'bank-account'
        // 缺少 toAccountId
      }

      const request = createMockRequest('POST', '/api/finance/transactions', invalidTransferData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('转账')
    })

    it('应该验证金额必须为正数', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const negativeAmountData = {
        amount: -500, // 负金额
        type: 'INCOME',
        description: '无效收入',
        toAccountId: 'income-account',
        transactionDate: '2023-06-15'
      }

      const request = createMockRequest('POST', '/api/finance/transactions', negativeAmountData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('金额')
    })
  })

  describe('财务业务逻辑测试', () => {
    it('应该正确计算账户余额', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 进行一系列交易
      const transactions = [
        { amount: 1000, type: 'INCOME', toAccountId: 'cash-account' },
        { amount: 500, type: 'EXPENSE', fromAccountId: 'cash-account' },
        { amount: 2000, type: 'TRANSFER', fromAccountId: 'bank-account', toAccountId: 'cash-account' }
      ]

      for (const transaction of transactions) {
        const request = createMockRequest('POST', '/api/finance/transactions', {
          ...transaction,
          description: '测试交易',
          transactionDate: '2023-06-15'
        })
        await POST(request)
      }

      // 检查最终余额
      const cashAccount = await testPrisma.financialAccount.findUnique({
        where: { id: 'cash-account' }
      })
      const bankAccount = await testPrisma.financialAccount.findUnique({
        where: { id: 'bank-account' }
      })

      // 现金账户: 10000 + 1000 - 500 + 2000 = 12500
      expect(cashAccount?.balance).toBe(12500)
      // 银行账户: 50000 - 2000 = 48000
      expect(bankAccount?.balance).toBe(48000)
    })

    it('应该支持交易冲正', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      // 创建原始交易
      const originalTransaction = {
        amount: 1000,
        type: 'EXPENSE',
        description: '错误支出',
        fromAccountId: 'cash-account',
        transactionDate: '2023-06-15'
      }

      const request1 = createMockRequest('POST', '/api/finance/transactions', originalTransaction)
      const response1 = await POST(request1)
      const transaction = await response1.json()

      // 创建冲正交易
      const reversalTransaction = {
        amount: 1000,
        type: 'INCOME',
        description: '冲正：错误支出',
        toAccountId: 'cash-account',
        transactionDate: '2023-06-15',
        reversalOfId: transaction.id
      }

      const request2 = createMockRequest('POST', '/api/finance/transactions', reversalTransaction)
      const response2 = await POST(request2)
      
      expect(response2.status).toBe(201)

      // 验证账户余额回到原始状态
      const account = await testPrisma.financialAccount.findUnique({
        where: { id: 'cash-account' }
      })
      expect(account?.balance).toBe(10000) // 回到原始余额
    })
  })
})