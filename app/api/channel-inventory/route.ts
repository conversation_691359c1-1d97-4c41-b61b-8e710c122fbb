import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('channelId');
    const productId = searchParams.get('productId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const skip = (page - 1) * limit;

    const where: any = {};

    if (channelId) {
      where.channelId = parseInt(channelId);
    }

    if (productId) {
      where.productId = parseInt(productId);
    }

    const [inventory, total] = await Promise.all([
      prisma.channelInventory.findMany({
        where,
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              image: true,
              category: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.channelInventory.count({ where }),
    ]);

    return NextResponse.json({
      data: inventory,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching channel inventory:', error);
    return NextResponse.json(
      { error: 'Failed to fetch channel inventory' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // 验证必填字段
    if (!data.channelId || !data.productId || data.quantity === undefined) {
      return NextResponse.json(
        { error: '渠道、产品和数量为必填项' },
        { status: 400 }
      );
    }

    // 检查渠道和产品是否存在
    const [channel, product] = await Promise.all([
      prisma.channel.findUnique({
        where: { id: parseInt(data.channelId) },
      }),
      prisma.product.findUnique({
        where: { id: parseInt(data.productId) },
      }),
    ]);

    if (!channel) {
      return NextResponse.json(
        { error: '渠道不存在' },
        { status: 404 }
      );
    }

    if (!product) {
      return NextResponse.json(
        { error: '产品不存在' },
        { status: 404 }
      );
    }

    // 检查是否已存在该渠道的该产品库存
    const existingInventory = await prisma.channelInventory.findUnique({
      where: {
        channelId_productId: {
          channelId: parseInt(data.channelId),
          productId: parseInt(data.productId),
        },
      },
    });

    let inventory;

    if (existingInventory) {
      // 更新现有库存
      inventory = await prisma.channelInventory.update({
        where: {
          channelId_productId: {
            channelId: parseInt(data.channelId),
            productId: parseInt(data.productId),
          },
        },
        data: {
          quantity: parseInt(data.quantity),
          minStockLevel: data.minStockLevel ? parseInt(data.minStockLevel) : undefined,
          maxStockLevel: data.maxStockLevel ? parseInt(data.maxStockLevel) : undefined,
          lastRestockDate: data.lastRestockDate ? new Date(data.lastRestockDate) : undefined,
        },
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              image: true,
              category: true,
            },
          },
        },
      });
    } else {
      // 创建新库存记录
      inventory = await prisma.channelInventory.create({
        data: {
          channelId: parseInt(data.channelId),
          productId: parseInt(data.productId),
          quantity: parseInt(data.quantity),
          minStockLevel: data.minStockLevel ? parseInt(data.minStockLevel) : undefined,
          maxStockLevel: data.maxStockLevel ? parseInt(data.maxStockLevel) : undefined,
          lastRestockDate: data.lastRestockDate ? new Date(data.lastRestockDate) : undefined,
        },
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              image: true,
              category: true,
            },
          },
        },
      });
    }

    return NextResponse.json(inventory, { status: existingInventory ? 200 : 201 });
  } catch (error) {
    console.error('Error creating/updating channel inventory:', error);
    return NextResponse.json(
      { error: 'Failed to create/update channel inventory' },
      { status: 500 }
    );
  }
}

// 批量更新库存
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    if (!Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: '请提供有效的库存更新数据' },
        { status: 400 }
      );
    }

    const updates = await Promise.all(
      data.map(async (item) => {
        if (!item.channelId || !item.productId || item.quantity === undefined) {
          throw new Error('每个项目都必须包含渠道ID、产品ID和数量');
        }

        return prisma.channelInventory.upsert({
          where: {
            channelId_productId: {
              channelId: parseInt(item.channelId),
              productId: parseInt(item.productId),
            },
          },
          update: {
            quantity: parseInt(item.quantity),
            minStockLevel: item.minStockLevel ? parseInt(item.minStockLevel) : undefined,
            maxStockLevel: item.maxStockLevel ? parseInt(item.maxStockLevel) : undefined,
            lastRestockDate: item.lastRestockDate ? new Date(item.lastRestockDate) : undefined,
          },
          create: {
            channelId: parseInt(item.channelId),
            productId: parseInt(item.productId),
            quantity: parseInt(item.quantity),
            minStockLevel: item.minStockLevel ? parseInt(item.minStockLevel) : undefined,
            maxStockLevel: item.maxStockLevel ? parseInt(item.maxStockLevel) : undefined,
            lastRestockDate: item.lastRestockDate ? new Date(item.lastRestockDate) : undefined,
          },
          include: {
            channel: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
            product: {
              select: {
                id: true,
                name: true,
                image: true,
                category: true,
              },
            },
          },
        });
      })
    );

    return NextResponse.json({
      message: '库存批量更新成功',
      data: updates,
    });
  } catch (error) {
    console.error('Error batch updating channel inventory:', error);
    return NextResponse.json(
      { error: 'Failed to batch update channel inventory' },
      { status: 500 }
    );
  }
}