# Docker Compose 配置
# 用于本地开发和生产部署

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    ports:
      - "13000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/enamel_gallery
      - NEXTAUTH_URL=http://localhost:13000
      - NEXTAUTH_SECRET=your-secret-key-here
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 数据库服务
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: enamel_gallery
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "16382:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "18003:80"
      - "18444:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_cache:
    driver: local

networks:
  app-network:
    driver: bridge
