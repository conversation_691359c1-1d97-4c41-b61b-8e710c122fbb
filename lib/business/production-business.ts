import { PrismaClient, Prisma, ProductionOrderStatus, QualityStatus } from '@prisma/client'
import { z } from 'zod'

// 生产管理业务逻辑核心类
export class ProductionBusiness {
  constructor(private prisma: PrismaClient) {}

  // 智能生产计划调度
  async createProductionSchedule(data: {
    orders: Array<{
      productId: string
      quantity: number
      priority: 'low' | 'medium' | 'high' | 'urgent'
      requiredDate: Date
      customerId?: string
    }>
    schedulingStrategy: 'earliest_due_date' | 'priority_first' | 'shortest_processing_time'
    operatorId: string
  }): Promise<{
    success: boolean
    scheduleId?: string
    productionOrders: Array<{
      orderId: string
      productId: string
      productName: string
      quantity: number
      estimatedStartDate: Date
      estimatedEndDate: Date
      requiredMaterials: Array<{
        materialId: string
        materialName: string
        requiredQuantity: number
        availableQuantity: number
        shortage: number
      }>
      estimatedCost: number
      priority: number
    }>
    resourceAllocation: {
      workstations: Array<{
        workstationId: string
        workstationName: string
        utilization: number
        assignedOrders: string[]
      }>
      workers: Array<{
        workerId: string
        workerName: string
        skillLevel: number
        assignedHours: number
      }>
    }
    warnings: string[]
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 创建生产计划记录
        const schedule = await tx.productionSchedule.create({
          data: {
            strategy: data.schedulingStrategy,
            createdBy: data.operatorId,
            status: 'ACTIVE'
          }
        })

        // 获取产品信息和生产时间
        const products = await tx.product.findMany({
          where: {
            id: { in: data.orders.map(o => o.productId) }
          },
          include: {
            category: true,
            productionBases: {
              include: {
                materials: {
                  include: {
                    material: true
                  }
                }
              }
            }
          }
        })

        // 获取可用工作站和工人
        const workstations = await tx.workstation.findMany({
          where: { isActive: true },
          include: {
            productionOrders: {
              where: {
                status: { in: ['PENDING', 'IN_PROGRESS'] }
              }
            }
          }
        })

        const workers = await tx.employee.findMany({
          where: {
            department: '生产部',
            status: 'ACTIVE'
          }
        })

        // 排序策略
        let sortedOrders = [...data.orders]
        switch (data.schedulingStrategy) {
          case 'earliest_due_date':
            sortedOrders.sort((a, b) => a.requiredDate.getTime() - b.requiredDate.getTime())
            break
          case 'priority_first':
            const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
            sortedOrders.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])
            break
          case 'shortest_processing_time':
            // 需要根据产品复杂度排序，这里简化处理
            sortedOrders.sort((a, b) => a.quantity - b.quantity)
            break
        }

        const productionOrders = []
        const warnings = []
        let currentDate = new Date()

        // 资源分配追踪
        const workstationUtilization = new Map<string, number>()
        const workerHours = new Map<string, number>()

        workstations.forEach(ws => workstationUtilization.set(ws.id, 0))
        workers.forEach(w => workerHours.set(w.id, 0))

        for (const orderData of sortedOrders) {
          const product = products.find(p => p.id === orderData.productId)
          if (!product) {
            warnings.push(`产品 ${orderData.productId} 不存在`)
            continue
          }

          // 计算所需材料
          const requiredMaterials = []
          let materialShortageFound = false

          if (product.productionBases.length > 0) {
            const productionBase = product.productionBases[0]
            
            for (const materialReq of productionBase.materials) {
              const requiredQty = materialReq.quantity * orderData.quantity
              
              // 检查库存
              const inventory = await tx.inventoryItem.findFirst({
                where: {
                  productId: materialReq.materialId
                }
              })

              const availableQty = inventory ? inventory.quantity - inventory.reservedQuantity : 0
              const shortage = Math.max(0, requiredQty - availableQty)

              if (shortage > 0) {
                materialShortageFound = true
                warnings.push(`材料 ${materialReq.material.name} 库存不足，缺少 ${shortage} 单位`)
              }

              requiredMaterials.push({
                materialId: materialReq.materialId,
                materialName: materialReq.material.name,
                requiredQuantity: requiredQty,
                availableQuantity: availableQty,
                shortage
              })
            }
          }

          // 估算生产时间和成本
          const estimatedHours = this.calculateProductionTime(product, orderData.quantity)
          const estimatedCost = this.calculateProductionCost(product, orderData.quantity, requiredMaterials)

          // 分配工作站
          const availableWorkstation = workstations.find(ws => 
            (workstationUtilization.get(ws.id) || 0) < 8 * 5 // 假设每周40小时
          )

          if (!availableWorkstation) {
            warnings.push(`没有可用的工作站安排生产订单`)
          }

          const estimatedStartDate = new Date(currentDate)
          const estimatedEndDate = new Date(currentDate.getTime() + estimatedHours * 60 * 60 * 1000)

          // 创建生产订单
          const productionOrder = await tx.productionOrder.create({
            data: {
              productId: orderData.productId,
              quantity: orderData.quantity,
              priority: this.getPriorityNumber(orderData.priority),
              requiredDate: orderData.requiredDate,
              estimatedStartDate,
              estimatedEndDate,
              customerId: orderData.customerId,
              status: materialShortageFound ? ProductionOrderStatus.PENDING : ProductionOrderStatus.SCHEDULED,
              scheduleId: schedule.id,
              workstationId: availableWorkstation?.id,
              createdBy: data.operatorId
            }
          })

          productionOrders.push({
            orderId: productionOrder.id,
            productId: orderData.productId,
            productName: product.name,
            quantity: orderData.quantity,
            estimatedStartDate,
            estimatedEndDate,
            requiredMaterials,
            estimatedCost,
            priority: this.getPriorityNumber(orderData.priority)
          })

          // 更新资源利用率
          if (availableWorkstation) {
            workstationUtilization.set(availableWorkstation.id, 
              (workstationUtilization.get(availableWorkstation.id) || 0) + estimatedHours
            )
          }

          currentDate = estimatedEndDate
        }

        // 构建资源分配报告
        const resourceAllocation = {
          workstations: workstations.map(ws => ({
            workstationId: ws.id,
            workstationName: ws.name,
            utilization: (workstationUtilization.get(ws.id) || 0) / (8 * 5) * 100,
            assignedOrders: productionOrders
              .filter(po => po.orderId === ws.id)
              .map(po => po.orderId)
          })),
          workers: workers.map(w => ({
            workerId: w.id,
            workerName: w.name,
            skillLevel: 5, // 简化处理
            assignedHours: workerHours.get(w.id) || 0
          }))
        }

        return {
          success: true,
          scheduleId: schedule.id,
          productionOrders,
          resourceAllocation,
          warnings
        }
      } catch (error) {
        console.error('生产计划创建失败:', error)
        return {
          success: false,
          productionOrders: [],
          resourceAllocation: { workstations: [], workers: [] },
          warnings: ['生产计划创建过程中发生错误']
        }
      }
    })
  }

  // 质量控制检查
  async performQualityCheck(data: {
    productionOrderId: string
    checkpoints: Array<{
      checkpointName: string
      checkType: 'visual' | 'measurement' | 'test'
      expectedValue?: string
      actualValue: string
      passed: boolean
      notes?: string
    }>
    inspectorId: string
    overallResult: 'passed' | 'failed' | 'conditional'
  }): Promise<{
    success: boolean
    qualityRecordId?: string
    recommendations: Array<{
      issue: string
      severity: 'low' | 'medium' | 'high' | 'critical'
      recommendation: string
      estimatedCost: number
    }>
    nextActions: string[]
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 获取生产订单信息
        const productionOrder = await tx.productionOrder.findUnique({
          where: { id: data.productionOrderId },
          include: { product: true }
        })

        if (!productionOrder) {
          return {
            success: false,
            recommendations: [],
            nextActions: ['生产订单不存在']
          }
        }

        // 创建质量检查记录
        const qualityRecord = await tx.qualityRecord.create({
          data: {
            productionOrderId: data.productionOrderId,
            inspectorId: data.inspectorId,
            checkDate: new Date(),
            overallResult: data.overallResult,
            status: data.overallResult === 'passed' ? QualityStatus.APPROVED : QualityStatus.REJECTED
          }
        })

        // 创建检查点记录
        for (const checkpoint of data.checkpoints) {
          await tx.qualityCheckpoint.create({
            data: {
              qualityRecordId: qualityRecord.id,
              checkpointName: checkpoint.checkpointName,
              checkType: checkpoint.checkType,
              expectedValue: checkpoint.expectedValue,
              actualValue: checkpoint.actualValue,
              passed: checkpoint.passed,
              notes: checkpoint.notes
            }
          })
        }

        // 分析质量问题并生成建议
        const recommendations = []
        const nextActions = []

        const failedChecks = data.checkpoints.filter(cp => !cp.passed)
        
        for (const failedCheck of failedChecks) {
          let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
          let estimatedCost = 0

          // 根据检查类型和失败情况确定严重程度
          switch (failedCheck.checkType) {
            case 'visual':
              severity = 'low'
              estimatedCost = 50
              break
            case 'measurement':
              severity = 'high'
              estimatedCost = 200
              break
            case 'test':
              severity = 'critical'
              estimatedCost = 500
              break
          }

          recommendations.push({
            issue: `${failedCheck.checkpointName} 检查失败`,
            severity,
            recommendation: this.getQualityRecommendation(failedCheck.checkType, failedCheck.checkpointName),
            estimatedCost
          })
        }

        // 确定下一步行动
        if (data.overallResult === 'passed') {
          nextActions.push('质量检查通过，可以继续生产流程')
          
          // 更新生产订单状态
          await tx.productionOrder.update({
            where: { id: data.productionOrderId },
            data: { status: ProductionOrderStatus.QUALITY_CHECKED }
          })
        } else if (data.overallResult === 'failed') {
          nextActions.push('质量检查失败，需要返工或废料处理')
          
          // 更新生产订单状态
          await tx.productionOrder.update({
            where: { id: data.productionOrderId },
            data: { status: ProductionOrderStatus.QUALITY_FAILED }
          })
        } else {
          nextActions.push('质量检查有条件通过，需要进一步处理')
        }

        // 如果有严重质量问题，创建质量问题记录
        const criticalIssues = recommendations.filter(r => r.severity === 'critical')
        if (criticalIssues.length > 0) {
          await tx.qualityIssue.create({
            data: {
              productionOrderId: data.productionOrderId,
              description: criticalIssues.map(i => i.issue).join('; '),
              severity: 'CRITICAL',
              status: 'OPEN',
              reportedBy: data.inspectorId
            }
          })
          
          nextActions.push('发现严重质量问题，已创建问题跟踪记录')
        }

        return {
          success: true,
          qualityRecordId: qualityRecord.id,
          recommendations,
          nextActions
        }
      } catch (error) {
        console.error('质量检查失败:', error)
        return {
          success: false,
          recommendations: [],
          nextActions: ['质量检查过程中发生错误']
        }
      }
    })
  }

  // 生产成本跟踪
  async trackProductionCosts(productionOrderId: string): Promise<{
    success: boolean
    costBreakdown: {
      materialCosts: Array<{
        materialName: string
        quantity: number
        unitCost: number
        totalCost: number
      }>
      laborCosts: Array<{
        workerName: string
        hours: number
        hourlyRate: number
        totalCost: number
      }>
      overheadCosts: Array<{
        costType: string
        amount: number
        description: string
      }>
      totalMaterialCost: number
      totalLaborCost: number
      totalOverheadCost: number
      totalProductionCost: number
      unitCost: number
    }
    variances: Array<{
      type: 'material' | 'labor' | 'overhead'
      budgeted: number
      actual: number
      variance: number
      variancePercent: number
      explanation: string
    }>
  }> {
    try {
      const productionOrder = await this.prisma.productionOrder.findUnique({
        where: { id: productionOrderId },
        include: {
          product: true,
          costRecords: true,
          materialUsages: {
            include: {
              material: true
            }
          },
          workLogs: {
            include: {
              worker: true
            }
          }
        }
      })

      if (!productionOrder) {
        return {
          success: false,
          costBreakdown: {
            materialCosts: [],
            laborCosts: [],
            overheadCosts: [],
            totalMaterialCost: 0,
            totalLaborCost: 0,
            totalOverheadCost: 0,
            totalProductionCost: 0,
            unitCost: 0
          },
          variances: []
        }
      }

      // 计算材料成本
      const materialCosts = productionOrder.materialUsages.map(usage => ({
        materialName: usage.material.name,
        quantity: usage.quantity,
        unitCost: usage.unitCost?.toNumber() || 0,
        totalCost: usage.quantity * (usage.unitCost?.toNumber() || 0)
      }))

      // 计算人工成本
      const laborCosts = productionOrder.workLogs.map(log => ({
        workerName: log.worker.name,
        hours: log.hours,
        hourlyRate: log.hourlyRate?.toNumber() || 0,
        totalCost: log.hours * (log.hourlyRate?.toNumber() || 0)
      }))

      // 计算间接成本
      const overheadCosts = productionOrder.costRecords
        .filter(record => record.costType === 'OVERHEAD')
        .map(record => ({
          costType: record.costType,
          amount: record.amount.toNumber(),
          description: record.description || '间接成本'
        }))

      const totalMaterialCost = materialCosts.reduce((sum, cost) => sum + cost.totalCost, 0)
      const totalLaborCost = laborCosts.reduce((sum, cost) => sum + cost.totalCost, 0)
      const totalOverheadCost = overheadCosts.reduce((sum, cost) => sum + cost.amount, 0)
      const totalProductionCost = totalMaterialCost + totalLaborCost + totalOverheadCost
      const unitCost = productionOrder.quantity > 0 ? totalProductionCost / productionOrder.quantity : 0

      // 计算成本差异 (简化版本)
      const standardCost = productionOrder.product.cost?.toNumber() || 0
      const variances = []

      if (standardCost > 0) {
        const totalVariance = totalProductionCost - (standardCost * productionOrder.quantity)
        const variancePercent = (totalVariance / (standardCost * productionOrder.quantity)) * 100

        variances.push({
          type: 'material' as const,
          budgeted: standardCost * productionOrder.quantity * 0.6, // 假设材料占60%
          actual: totalMaterialCost,
          variance: totalMaterialCost - (standardCost * productionOrder.quantity * 0.6),
          variancePercent: variancePercent * 0.6,
          explanation: totalMaterialCost > standardCost * productionOrder.quantity * 0.6 ? '材料成本超预算' : '材料成本节约'
        })
      }

      return {
        success: true,
        costBreakdown: {
          materialCosts,
          laborCosts,
          overheadCosts,
          totalMaterialCost,
          totalLaborCost,
          totalOverheadCost,
          totalProductionCost,
          unitCost
        },
        variances
      }
    } catch (error) {
      console.error('成本跟踪失败:', error)
      return {
        success: false,
        costBreakdown: {
          materialCosts: [],
          laborCosts: [],
          overheadCosts: [],
          totalMaterialCost: 0,
          totalLaborCost: 0,
          totalOverheadCost: 0,
          totalProductionCost: 0,
          unitCost: 0
        },
        variances: []
      }
    }
  }

  // 生产进度跟踪
  async trackProductionProgress(productionOrderId: string): Promise<{
    success: boolean
    progress: {
      currentStage: string
      completionPercentage: number
      estimatedCompletion: Date
      actualStartDate?: Date
      delays: Array<{
        reason: string
        delayHours: number
        impact: 'low' | 'medium' | 'high'
      }>
    }
    stageHistory: Array<{
      stageName: string
      startDate: Date
      endDate?: Date
      status: 'completed' | 'in_progress' | 'delayed' | 'blocked'
      notes?: string
    }>
    bottlenecks: Array<{
      resource: string
      utilizationPercent: number
      queueLength: number
      recommendedAction: string
    }>
  }> {
    try {
      const productionOrder = await this.prisma.productionOrder.findUnique({
        where: { id: productionOrderId },
        include: {
          stageHistories: {
            orderBy: { startDate: 'asc' }
          },
          workLogs: {
            orderBy: { startTime: 'desc' }
          }
        }
      })

      if (!productionOrder) {
        return {
          success: false,
          progress: {
            currentStage: '未知',
            completionPercentage: 0,
            estimatedCompletion: new Date()
          },
          stageHistory: [],
          bottlenecks: []
        }
      }

      // 计算进度百分比
      const totalStages = 5 // 简化：假设有5个标准阶段
      const completedStages = productionOrder.stageHistories.filter(s => s.endDate).length
      const completionPercentage = (completedStages / totalStages) * 100

      // 获取当前阶段
      const currentStageRecord = productionOrder.stageHistories.find(s => !s.endDate)
      const currentStage = currentStageRecord?.stageName || '已完成'

      // 计算延误
      const delays = []
      for (const stage of productionOrder.stageHistories) {
        if (stage.plannedEndDate && stage.endDate && stage.endDate > stage.plannedEndDate) {
          const delayHours = (stage.endDate.getTime() - stage.plannedEndDate.getTime()) / (1000 * 60 * 60)
          delays.push({
            reason: `${stage.stageName} 阶段延误`,
            delayHours,
            impact: delayHours > 24 ? 'high' : delayHours > 8 ? 'medium' : 'low'
          })
        }
      }

      // 估算完成时间
      const estimatedCompletion = new Date(productionOrder.estimatedEndDate)
      if (delays.length > 0) {
        const totalDelayHours = delays.reduce((sum, delay) => sum + delay.delayHours, 0)
        estimatedCompletion.setHours(estimatedCompletion.getHours() + totalDelayHours)
      }

      // 构建阶段历史
      const stageHistory = productionOrder.stageHistories.map(stage => ({
        stageName: stage.stageName,
        startDate: stage.startDate,
        endDate: stage.endDate || undefined,
        status: stage.endDate ? 'completed' as const : 
                currentStageRecord?.id === stage.id ? 'in_progress' as const : 'blocked' as const,
        notes: stage.notes || undefined
      }))

      // 识别瓶颈 (简化版本)
      const bottlenecks = []
      if (delays.length > 2) {
        bottlenecks.push({
          resource: '生产线',
          utilizationPercent: 95,
          queueLength: 3,
          recommendedAction: '考虑增加生产线或重新调度'
        })
      }

      return {
        success: true,
        progress: {
          currentStage,
          completionPercentage,
          estimatedCompletion,
          actualStartDate: productionOrder.actualStartDate || undefined,
          delays
        },
        stageHistory,
        bottlenecks
      }
    } catch (error) {
      console.error('进度跟踪失败:', error)
      return {
        success: false,
        progress: {
          currentStage: '错误',
          completionPercentage: 0,
          estimatedCompletion: new Date()
        },
        stageHistory: [],
        bottlenecks: []
      }
    }
  }

  // 辅助方法
  private calculateProductionTime(product: any, quantity: number): number {
    // 简化计算：基于产品复杂度和数量
    const baseHours = 2 // 基础生产时间
    const complexityMultiplier = product.category?.name === '高端定制' ? 2 : 1
    return baseHours * quantity * complexityMultiplier
  }

  private calculateProductionCost(product: any, quantity: number, materials: any[]): number {
    const materialCost = materials.reduce((sum, m) => sum + (m.requiredQuantity * (product.cost?.toNumber() || 0) * 0.6), 0)
    const laborCost = quantity * (product.cost?.toNumber() || 0) * 0.3
    const overheadCost = quantity * (product.cost?.toNumber() || 0) * 0.1
    return materialCost + laborCost + overheadCost
  }

  private getPriorityNumber(priority: string): number {
    const priorityMap = { urgent: 4, high: 3, medium: 2, low: 1 }
    return priorityMap[priority as keyof typeof priorityMap] || 2
  }

  private getQualityRecommendation(checkType: string, checkpointName: string): string {
    const recommendations = {
      visual: '重新检查外观质量，必要时进行抛光或修复',
      measurement: '重新测量尺寸，如超出公差范围需要返工',
      test: '重新进行功能测试，如仍失败需要分析根本原因'
    }
    return recommendations[checkType as keyof typeof recommendations] || '需要进一步分析和处理'
  }
}

// 生产业务逻辑验证规则
export const productionValidationSchemas = {
  productionSchedule: z.object({
    orders: z.array(z.object({
      productId: z.string().min(1, '产品ID不能为空'),
      quantity: z.number().min(1, '生产数量必须大于0'),
      priority: z.enum(['low', 'medium', 'high', 'urgent']),
      requiredDate: z.date(),
      customerId: z.string().optional()
    })).min(1, '生产订单不能为空'),
    schedulingStrategy: z.enum(['earliest_due_date', 'priority_first', 'shortest_processing_time']),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  qualityCheck: z.object({
    productionOrderId: z.string().min(1, '生产订单ID不能为空'),
    checkpoints: z.array(z.object({
      checkpointName: z.string().min(1, '检查点名称不能为空'),
      checkType: z.enum(['visual', 'measurement', 'test']),
      expectedValue: z.string().optional(),
      actualValue: z.string().min(1, '实际值不能为空'),
      passed: z.boolean(),
      notes: z.string().optional()
    })).min(1, '检查点不能为空'),
    inspectorId: z.string().min(1, '检查员ID不能为空'),
    overallResult: z.enum(['passed', 'failed', 'conditional'])
  })
}