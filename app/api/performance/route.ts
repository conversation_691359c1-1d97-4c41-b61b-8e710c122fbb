import { NextRequest, NextResponse } from "next/server"
import { ApiPerformanceMonitor } from "@/lib/middleware/api-optimization"
import { cacheUtils } from "@/lib/cache/cache-instances"

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const action = url.searchParams.get('action')

    switch (action) {
      case 'metrics':
        // 获取API性能指标
        const apiMetrics = ApiPerformanceMonitor.getMetrics()
        
        return NextResponse.json({
          api: apiMetrics,
          cache: {
            stats: cacheUtils.getAllStats(),
            totalMemoryUsage: cacheUtils.getTotalMemoryUsage(),
            overallHitRate: cacheUtils.getOverallHitRate()
          },
          timestamp: new Date().toISOString()
        })

      case 'reset':
        // 重置性能指标
        ApiPerformanceMonitor.reset()
        return NextResponse.json({ message: 'Performance metrics reset' })

      case 'cache-clear':
        // 清空所有缓存
        cacheUtils.clearAll()
        return NextResponse.json({ message: 'All caches cleared' })

      case 'cache-cleanup':
        // 清理过期缓存
        cacheUtils.cleanup()
        return NextResponse.json({ message: 'Expired caches cleaned up' })

      default:
        // 默认返回概览信息
        const overview = {
          api: {
            totalEndpoints: Object.keys(ApiPerformanceMonitor.getMetrics()).length,
            ...ApiPerformanceMonitor.getMetrics()
          },
          cache: {
            stats: cacheUtils.getAllStats(),
            totalMemoryUsage: cacheUtils.getTotalMemoryUsage(),
            overallHitRate: cacheUtils.getOverallHitRate()
          },
          system: {
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            nodeVersion: process.version
          },
          timestamp: new Date().toISOString()
        }

        return NextResponse.json(overview)
    }
  } catch (error) {
    console.error("Error in performance API:", error)
    return NextResponse.json(
      { error: "Failed to get performance data" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const { action } = data

    switch (action) {
      case 'record-metric':
        // 手动记录性能指标
        const { endpoint, responseTime, isError, cacheHit } = data
        ApiPerformanceMonitor.recordRequest(endpoint, responseTime, isError, cacheHit)
        return NextResponse.json({ message: 'Metric recorded' })

      default:
        return NextResponse.json(
          { error: 'Unknown action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error("Error in performance API POST:", error)
    return NextResponse.json(
      { error: "Failed to process performance action" },
      { status: 500 }
    )
  }
}