"use client"

import React, { memo, useCallback } from 'react'
import { TableCell, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { LazyImage } from "@/components/ui/lazy-image"
import { Product } from "@/types/product"
import {
  EditIcon,
  TrashIcon,
  ImageIcon,
  MoreHorizontalIcon,
  TagIcon
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface OptimizedProductTableRowProps {
  product: Product
  isSelected: boolean
  onSelect: (productId: number, checked: boolean) => void
  onEdit: (product: Product) => void
  onDelete: (productId: number) => void
  onImagePreview: (imageUrl: string) => void
  onDoubleClickEdit: (productId: number, field: string, value: string | number) => void
}

export const OptimizedProductTableRow = memo<OptimizedProductTableRowProps>(({
  product,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onImagePreview,
  onDoubleClickEdit
}) => {
  // 使用useCallback优化事件处理函数
  const handleSelect = useCallback((checked: boolean) => {
    onSelect(product.id!, checked)
  }, [product.id, onSelect])

  const handleEdit = useCallback(() => {
    onEdit(product)
  }, [product, onEdit])

  const handleDelete = useCallback(() => {
    onDelete(product.id!)
  }, [product.id, onDelete])

  const handleImageClick = useCallback(() => {
    if (product.imageUrl) {
      onImagePreview(product.imageUrl)
    }
  }, [product.imageUrl, onImagePreview])

  const handleNameDoubleClick = useCallback(() => {
    onDoubleClickEdit(product.id!, 'name', product.name)
  }, [product.id, product.name, onDoubleClickEdit])

  const handlePriceDoubleClick = useCallback(() => {
    onDoubleClickEdit(product.id!, 'price', product.price)
  }, [product.id, product.price, onDoubleClickEdit])

  const handleInventoryDoubleClick = useCallback(() => {
    onDoubleClickEdit(product.id!, 'inventory', product.inventory || 0)
  }, [product.id, product.inventory, onDoubleClickEdit])

  return (
    <TableRow className={isSelected ? 'bg-primary/5' : ''}>
      {/* 选择框 */}
      <TableCell className="w-12">
        <Checkbox
          checked={isSelected}
          onCheckedChange={handleSelect}
        />
      </TableCell>

      {/* 产品图片 */}
      <TableCell className="w-16">
        <div 
          className="w-12 h-12 rounded-md overflow-hidden bg-muted cursor-pointer"
          onClick={handleImageClick}
        >
          {product.imageUrl ? (
            <LazyImage
              src={product.imageUrl}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
        </div>
      </TableCell>

      {/* 产品名称 */}
      <TableCell>
        <div 
          className="font-medium cursor-pointer hover:text-primary"
          onDoubleClick={handleNameDoubleClick}
        >
          {product.name}
        </div>
        {product.description && (
          <div className="text-sm text-muted-foreground line-clamp-1">
            {product.description}
          </div>
        )}
      </TableCell>

      {/* 分类 */}
      <TableCell>
        {product.categoryName ? (
          <Badge variant="secondary">{product.categoryName}</Badge>
        ) : (
          <span className="text-muted-foreground">未分类</span>
        )}
      </TableCell>

      {/* 价格 */}
      <TableCell>
        <span 
          className="font-semibold cursor-pointer hover:text-primary"
          onDoubleClick={handlePriceDoubleClick}
        >
          ¥{product.price.toFixed(2)}
        </span>
      </TableCell>

      {/* 库存 */}
      <TableCell>
        <span 
          className="cursor-pointer hover:text-primary"
          onDoubleClick={handleInventoryDoubleClick}
        >
          {product.inventory !== null ? product.inventory : '-'}
        </span>
      </TableCell>

      {/* 标签 */}
      <TableCell>
        {product.tags && product.tags.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {product.tags.slice(0, 2).map((tag) => (
              <Badge key={tag.id} variant="outline" className="text-xs">
                <TagIcon className="h-3 w-3 mr-1" />
                {tag.name}
              </Badge>
            ))}
            {product.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{product.tags.length - 2}
              </Badge>
            )}
          </div>
        ) : (
          <span className="text-muted-foreground">-</span>
        )}
      </TableCell>

      {/* 操作 */}
      <TableCell className="w-16">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <EditIcon className="h-4 w-4 mr-2" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} className="text-destructive">
              <TrashIcon className="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )
})

OptimizedProductTableRow.displayName = 'OptimizedProductTableRow'