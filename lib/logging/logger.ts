import { PrismaClient } from '@prisma/client'
import fs from 'fs/promises'
import path from 'path'
import { createWriteStream, WriteStream } from 'fs'

// 企业级日志系统
export class Logger {
  private fileStreams: Map<string, WriteStream> = new Map()
  private logQueue: LogEntry[] = []
  private flushInterval: NodeJS.Timeout | null = null
  private isInitialized = false

  constructor(
    private config: LoggerConfig = DEFAULT_LOGGER_CONFIG,
    private prisma?: PrismaClient
  ) {
    this.init()
  }

  // 初始化日志系统
  private async init(): Promise<void> {
    try {
      // 确保日志目录存在
      await this.ensureLogDirectories()

      // 启动文件流
      await this.initializeFileStreams()

      // 启动批量刷新定时器
      this.startFlushTimer()

      this.isInitialized = true
      console.log('日志系统初始化完成')
    } catch (error) {
      console.error('日志系统初始化失败:', error)
    }
  }

  // 记录调试日志
  debug(message: string, meta?: any, context?: LogContext): void {
    this.log('debug', message, meta, context)
  }

  // 记录信息日志
  info(message: string, meta?: any, context?: LogContext): void {
    this.log('info', message, meta, context)
  }

  // 记录警告日志
  warn(message: string, meta?: any, context?: LogContext): void {
    this.log('warn', message, meta, context)
  }

  // 记录错误日志
  error(message: string, error?: Error | any, context?: LogContext): void {
    const meta = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...error
    } : error

    this.log('error', message, meta, context)
  }

  // 记录致命错误日志
  fatal(message: string, error?: Error | any, context?: LogContext): void {
    const meta = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...error
    } : error

    this.log('fatal', message, meta, context)
  }

  // 记录业务操作日志
  business(operation: string, details: any, context?: LogContext): void {
    this.log('info', `业务操作: ${operation}`, details, {
      ...context,
      category: 'business',
      operation
    })
  }

  // 记录安全事件日志
  security(event: string, details: any, context?: LogContext): void {
    this.log('warn', `安全事件: ${event}`, details, {
      ...context,
      category: 'security',
      event
    })
  }

  // 记录性能日志
  performance(metric: string, value: number, unit: string, context?: LogContext): void {
    this.log('info', `性能指标: ${metric}`, {
      metric,
      value,
      unit
    }, {
      ...context,
      category: 'performance'
    })
  }

  // 记录审计日志
  audit(action: string, resource: string, userId?: string, details?: any): void {
    this.log('info', `审计: ${action}`, {
      action,
      resource,
      userId,
      ...details
    }, {
      category: 'audit',
      userId
    })
  }

  // 核心日志记录方法
  private log(level: LogLevel, message: string, meta?: any, context?: LogContext): void {
    if (!this.shouldLog(level)) {
      return
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      meta: meta || {},
      context: {
        ...this.getDefaultContext(),
        ...context
      }
    }

    // 添加到队列
    this.logQueue.push(entry)

    // 立即输出到控制台（如果启用）
    if (this.config.console.enabled) {
      this.logToConsole(entry)
    }

    // 如果是错误或致命错误，立即刷新
    if (level === 'error' || level === 'fatal') {
      this.flush()
    }
  }

  // 批量刷新日志
  private async flush(): Promise<void> {
    if (this.logQueue.length === 0) {
      return
    }

    const entries = [...this.logQueue]
    this.logQueue = []

    // 并行写入不同的目标
    const promises = []

    // 写入文件
    if (this.config.file.enabled) {
      promises.push(this.writeToFiles(entries))
    }

    // 写入数据库
    if (this.config.database.enabled && this.prisma) {
      promises.push(this.writeToDatabase(entries))
    }

    // 发送到远程日志服务
    if (this.config.remote.enabled) {
      promises.push(this.sendToRemote(entries))
    }

    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.error('日志刷新失败:', error)
    }
  }

  // 写入文件
  private async writeToFiles(entries: LogEntry[]): Promise<void> {
    const groupedEntries = this.groupEntriesByFile(entries)

    for (const [filename, fileEntries] of groupedEntries) {
      try {
        const stream = this.fileStreams.get(filename)
        if (stream) {
          for (const entry of fileEntries) {
            const logLine = this.formatLogEntry(entry) + '\n'
            stream.write(logLine)
          }
        }
      } catch (error) {
        console.error(`写入日志文件 ${filename} 失败:`, error)
      }
    }
  }

  // 写入数据库
  private async writeToDatabase(entries: LogEntry[]): Promise<void> {
    if (!this.prisma) return

    try {
      const logData = entries.map(entry => ({
        timestamp: entry.timestamp,
        level: entry.level.toUpperCase(),
        message: entry.message,
        meta: JSON.stringify(entry.meta),
        context: JSON.stringify(entry.context),
        category: entry.context.category || 'general',
        userId: entry.context.userId,
        sessionId: entry.context.sessionId,
        requestId: entry.context.requestId,
        traceId: entry.context.traceId
      }))

      await this.prisma.systemLog.createMany({
        data: logData
      })
    } catch (error) {
      console.error('写入数据库日志失败:', error)
    }
  }

  // 发送到远程日志服务
  private async sendToRemote(entries: LogEntry[]): Promise<void> {
    if (!this.config.remote.endpoint) return

    try {
      const payload = {
        logs: entries.map(entry => ({
          '@timestamp': entry.timestamp.toISOString(),
          level: entry.level,
          message: entry.message,
          meta: entry.meta,
          context: entry.context
        }))
      }

      const response = await fetch(this.config.remote.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.remote.apiKey && {
            'Authorization': `Bearer ${this.config.remote.apiKey}`
          })
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        throw new Error(`远程日志服务响应错误: ${response.status}`)
      }
    } catch (error) {
      console.error('发送远程日志失败:', error)
    }
  }

  // 输出到控制台
  private logToConsole(entry: LogEntry): void {
    const formatted = this.formatLogEntry(entry)
    const colorized = this.colorizeLogLevel(entry.level, formatted)

    switch (entry.level) {
      case 'debug':
        console.debug(colorized)
        break
      case 'info':
        console.log(colorized)
        break
      case 'warn':
        console.warn(colorized)
        break
      case 'error':
      case 'fatal':
        console.error(colorized)
        break
    }
  }

  // 格式化日志条目
  private formatLogEntry(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString()
    const level = entry.level.toUpperCase().padEnd(5)
    const context = entry.context.requestId ? `[${entry.context.requestId}]` : ''
    
    let formatted = `${timestamp} ${level} ${context} ${entry.message}`

    // 添加元数据
    if (entry.meta && Object.keys(entry.meta).length > 0) {
      formatted += ` | ${JSON.stringify(entry.meta)}`
    }

    // 添加上下文
    if (entry.context && Object.keys(entry.context).length > 0) {
      const contextStr = Object.entries(entry.context)
        .filter(([key]) => key !== 'requestId') // requestId已经显示了
        .map(([key, value]) => `${key}=${value}`)
        .join(' ')
      
      if (contextStr) {
        formatted += ` | ${contextStr}`
      }
    }

    return formatted
  }

  // 为日志级别添加颜色
  private colorizeLogLevel(level: LogLevel, message: string): string {
    if (!this.config.console.colors) return message

    const colors = {
      debug: '\x1b[36m', // 青色
      info: '\x1b[32m',  // 绿色
      warn: '\x1b[33m',  // 黄色
      error: '\x1b[31m', // 红色
      fatal: '\x1b[35m'  // 紫色
    }

    const reset = '\x1b[0m'
    return `${colors[level]}${message}${reset}`
  }

  // 检查是否应该记录此级别的日志
  private shouldLog(level: LogLevel): boolean {
    const levels = ['debug', 'info', 'warn', 'error', 'fatal']
    const currentLevelIndex = levels.indexOf(this.config.level)
    const logLevelIndex = levels.indexOf(level)
    
    return logLevelIndex >= currentLevelIndex
  }

  // 获取默认上下文
  private getDefaultContext(): LogContext {
    return {
      hostname: process.env.HOSTNAME || 'localhost',
      pid: process.pid,
      service: this.config.service,
      environment: process.env.NODE_ENV || 'development'
    }
  }

  // 确保日志目录存在
  private async ensureLogDirectories(): Promise<void> {
    const dirs = [
      this.config.file.directory,
      path.join(this.config.file.directory, 'error'),
      path.join(this.config.file.directory, 'business'),
      path.join(this.config.file.directory, 'security'),
      path.join(this.config.file.directory, 'performance'),
      path.join(this.config.file.directory, 'audit')
    ]

    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true })
      } catch (error) {
        console.error(`创建日志目录失败 ${dir}:`, error)
      }
    }
  }

  // 初始化文件流
  private async initializeFileStreams(): Promise<void> {
    const streams = {
      'general': path.join(this.config.file.directory, `${this.config.service}.log`),
      'error': path.join(this.config.file.directory, 'error', `error.log`),
      'business': path.join(this.config.file.directory, 'business', `business.log`),
      'security': path.join(this.config.file.directory, 'security', `security.log`),
      'performance': path.join(this.config.file.directory, 'performance', `performance.log`),
      'audit': path.join(this.config.file.directory, 'audit', `audit.log`)
    }

    for (const [key, filepath] of Object.entries(streams)) {
      try {
        const stream = createWriteStream(filepath, { flags: 'a' })
        this.fileStreams.set(key, stream)
      } catch (error) {
        console.error(`创建日志文件流失败 ${filepath}:`, error)
      }
    }
  }

  // 启动刷新定时器
  private startFlushTimer(): void {
    this.flushInterval = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  // 按文件分组日志条目
  private groupEntriesByFile(entries: LogEntry[]): Map<string, LogEntry[]> {
    const groups = new Map<string, LogEntry[]>()

    for (const entry of entries) {
      let filename = 'general'

      // 根据级别或类别确定文件
      if (entry.level === 'error' || entry.level === 'fatal') {
        filename = 'error'
      } else if (entry.context.category) {
        filename = entry.context.category
      }

      if (!groups.has(filename)) {
        groups.set(filename, [])
      }
      groups.get(filename)!.push(entry)
    }

    return groups
  }

  // 日志查询方法
  async queryLogs(options: LogQueryOptions): Promise<LogEntry[]> {
    if (!this.prisma) {
      throw new Error('数据库日志查询需要Prisma客户端')
    }

    const where: any = {}

    if (options.level) {
      where.level = options.level.toUpperCase()
    }

    if (options.category) {
      where.category = options.category
    }

    if (options.userId) {
      where.userId = options.userId
    }

    if (options.startTime) {
      where.timestamp = { gte: options.startTime }
    }

    if (options.endTime) {
      where.timestamp = { ...where.timestamp, lte: options.endTime }
    }

    if (options.message) {
      where.message = { contains: options.message }
    }

    const logs = await this.prisma.systemLog.findMany({
      where,
      orderBy: { timestamp: options.order || 'desc' },
      take: options.limit || 100,
      skip: options.offset || 0
    })

    return logs.map(log => ({
      timestamp: log.timestamp,
      level: log.level.toLowerCase() as LogLevel,
      message: log.message,
      meta: JSON.parse(log.meta || '{}'),
      context: JSON.parse(log.context || '{}')
    }))
  }

  // 日志统计
  async getLogStats(timeRange: { start: Date; end: Date }): Promise<LogStats> {
    if (!this.prisma) {
      throw new Error('日志统计需要Prisma客户端')
    }

    const [
      totalLogs,
      errorLogs,
      warnLogs,
      categoryStats,
      topUsers,
      recentErrors
    ] = await Promise.all([
      // 总日志数
      this.prisma.systemLog.count({
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        }
      }),
      // 错误日志数
      this.prisma.systemLog.count({
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          level: { in: ['ERROR', 'FATAL'] }
        }
      }),
      // 警告日志数
      this.prisma.systemLog.count({
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          level: 'WARN'
        }
      }),
      // 按类别统计
      this.prisma.systemLog.groupBy({
        by: ['category'],
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        },
        _count: { id: true }
      }),
      // 用户活动统计
      this.prisma.systemLog.groupBy({
        by: ['userId'],
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          userId: { not: null }
        },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      }),
      // 最近的错误
      this.prisma.systemLog.findMany({
        where: {
          timestamp: {
            gte: timeRange.start,
            lte: timeRange.end
          },
          level: { in: ['ERROR', 'FATAL'] }
        },
        orderBy: { timestamp: 'desc' },
        take: 10
      })
    ])

    return {
      totalLogs,
      errorLogs,
      warnLogs,
      errorRate: totalLogs > 0 ? (errorLogs / totalLogs) * 100 : 0,
      categoryDistribution: categoryStats.map(stat => ({
        category: stat.category,
        count: stat._count.id
      })),
      topUsers: topUsers.map(user => ({
        userId: user.userId!,
        logCount: user._count.id
      })),
      recentErrors: recentErrors.map(log => ({
        timestamp: log.timestamp,
        message: log.message,
        level: log.level as LogLevel
      }))
    }
  }

  // 清理旧日志
  async cleanupOldLogs(retentionDays: number = 90): Promise<number> {
    if (!this.prisma) {
      throw new Error('清理日志需要Prisma客户端')
    }

    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

    const result = await this.prisma.systemLog.deleteMany({
      where: {
        timestamp: { lt: cutoffDate }
      }
    })

    console.log(`清理了 ${result.count} 条旧日志记录`)
    return result.count
  }

  // 关闭日志系统
  async close(): Promise<void> {
    // 刷新剩余日志
    await this.flush()

    // 停止定时器
    if (this.flushInterval) {
      clearInterval(this.flushInterval)
      this.flushInterval = null
    }

    // 关闭文件流
    for (const stream of this.fileStreams.values()) {
      stream.end()
    }
    this.fileStreams.clear()

    console.log('日志系统已关闭')
  }
}

// 类型定义
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

export interface LogEntry {
  timestamp: Date
  level: LogLevel
  message: string
  meta: any
  context: LogContext
}

export interface LogContext {
  requestId?: string
  sessionId?: string
  userId?: string
  traceId?: string
  category?: string
  operation?: string
  event?: string
  hostname?: string
  pid?: number
  service?: string
  environment?: string
}

export interface LoggerConfig {
  level: LogLevel
  service: string
  flushInterval: number
  console: {
    enabled: boolean
    colors: boolean
  }
  file: {
    enabled: boolean
    directory: string
    maxSize: number
    maxFiles: number
  }
  database: {
    enabled: boolean
  }
  remote: {
    enabled: boolean
    endpoint?: string
    apiKey?: string
  }
}

export interface LogQueryOptions {
  level?: LogLevel
  category?: string
  userId?: string
  startTime?: Date
  endTime?: Date
  message?: string
  limit?: number
  offset?: number
  order?: 'asc' | 'desc'
}

export interface LogStats {
  totalLogs: number
  errorLogs: number
  warnLogs: number
  errorRate: number
  categoryDistribution: Array<{
    category: string
    count: number
  }>
  topUsers: Array<{
    userId: string
    logCount: number
  }>
  recentErrors: Array<{
    timestamp: Date
    message: string
    level: LogLevel
  }>
}

export const DEFAULT_LOGGER_CONFIG: LoggerConfig = {
  level: 'info',
  service: 'erp-system',
  flushInterval: 5000, // 5秒
  console: {
    enabled: true,
    colors: true
  },
  file: {
    enabled: true,
    directory: './logs',
    maxSize: 100 * 1024 * 1024, // 100MB
    maxFiles: 10
  },
  database: {
    enabled: true
  },
  remote: {
    enabled: false
  }
}

// 创建全局日志实例
export function createLogger(config?: Partial<LoggerConfig>, prisma?: PrismaClient): Logger {
  const finalConfig = { ...DEFAULT_LOGGER_CONFIG, ...config }
  return new Logger(finalConfig, prisma)
}