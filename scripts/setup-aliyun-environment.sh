#!/bin/bash

# 阿里云服务器环境配置脚本
# 解决GitHub连接问题，配置国内镜像源

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    log_info "检测到操作系统: $OS $VER"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        apt update && apt upgrade -y
        apt install -y curl wget git vim htop
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
        yum update -y
        yum install -y curl wget git vim htop
    else
        log_warning "未知的操作系统，跳过系统更新"
    fi
    
    log_success "系统更新完成"
}

# 配置Git镜像
setup_git_mirrors() {
    log_info "配置Git镜像源..."
    
    # 配置Git使用国内镜像
    git config --global url."https://gitee.com/".insteadOf "https://github.com/"
    git config --global url."https://hub.fastgit.xyz/".insteadOf "https://github.com/"
    
    # 配置Git用户信息（如果未配置）
    if [[ -z $(git config --global user.name) ]]; then
        read -p "请输入Git用户名: " git_username
        git config --global user.name "$git_username"
    fi
    
    if [[ -z $(git config --global user.email) ]]; then
        read -p "请输入Git邮箱: " git_email
        git config --global user.email "$git_email"
    fi
    
    log_success "Git镜像配置完成"
}

# 配置npm镜像
setup_npm_mirrors() {
    log_info "配置npm镜像源..."
    
    # 检查是否已安装Node.js
    if ! command -v node &> /dev/null; then
        log_info "安装Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
            apt-get install -y nodejs
        elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
            yum install -y nodejs npm
        fi
    fi
    
    # 配置npm镜像
    npm config set registry https://registry.npmmirror.com/
    npm config set disturl https://npmmirror.com/dist
    npm config set electron_mirror https://npmmirror.com/mirrors/electron/
    npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
    npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
    npm config set chromedriver_cdnurl https://npmmirror.com/mirrors/chromedriver/
    
    log_success "npm镜像配置完成"
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，跳过安装步骤"
        return
    fi
    
    # 使用阿里云Docker安装脚本
    curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 将当前用户添加到docker组
    usermod -aG docker $USER
    
    log_success "Docker安装完成"
}

# 配置Docker镜像加速
setup_docker_mirrors() {
    log_info "配置Docker镜像加速..."
    
    mkdir -p /etc/docker
    
    cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF
    
    # 重启Docker服务
    systemctl daemon-reload
    systemctl restart docker
    
    log_success "Docker镜像加速配置完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，跳过安装步骤"
        return
    fi
    
    # 使用国内镜像下载Docker Compose
    DOCKER_COMPOSE_VERSION="2.20.2"
    curl -L "https://get.daocloud.io/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian使用ufw
        ufw allow 22
        ufw allow 80
        ufw allow 443
        ufw --force enable
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL使用firewalld
        systemctl start firewalld
        systemctl enable firewalld
        firewall-cmd --permanent --add-port=22/tcp
        firewall-cmd --permanent --add-port=80/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --reload
    else
        log_warning "未找到防火墙管理工具，请手动配置防火墙"
    fi
    
    log_success "防火墙配置完成"
}

# 创建项目目录
create_project_dirs() {
    log_info "创建项目目录..."
    
    mkdir -p /opt/linghua-app
    mkdir -p /opt/data/{postgres,redis}
    mkdir -p /opt/backups
    mkdir -p /opt/scripts
    mkdir -p /opt/logs
    
    # 设置目录权限
    chmod 755 /opt/linghua-app
    chmod 700 /opt/data
    chmod 755 /opt/backups
    chmod 755 /opt/scripts
    chmod 755 /opt/logs
    
    log_success "项目目录创建完成"
}

# 配置系统优化
optimize_system() {
    log_info "优化系统配置..."
    
    # 增加文件描述符限制
    echo "* soft nofile 65536" >> /etc/security/limits.conf
    echo "* hard nofile 65536" >> /etc/security/limits.conf
    
    # 优化内核参数
    cat >> /etc/sysctl.conf << 'EOF'
# 网络优化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# 文件系统优化
fs.file-max = 2097152
vm.swappiness = 10
EOF
    
    sysctl -p
    
    log_success "系统优化完成"
}

# 测试网络连接
test_network() {
    log_info "测试网络连接..."
    
    # 测试国内镜像源
    if curl -s --connect-timeout 5 https://registry.npmmirror.com/ > /dev/null; then
        log_success "npm镜像源连接正常"
    else
        log_warning "npm镜像源连接失败"
    fi
    
    if curl -s --connect-timeout 5 https://gitee.com/ > /dev/null; then
        log_success "Gitee连接正常"
    else
        log_warning "Gitee连接失败"
    fi
    
    if docker pull hello-world > /dev/null 2>&1; then
        log_success "Docker镜像拉取正常"
        docker rmi hello-world > /dev/null 2>&1
    else
        log_warning "Docker镜像拉取失败"
    fi
}

# 显示配置信息
show_info() {
    echo
    log_success "🎉 阿里云环境配置完成！"
    echo
    echo "📋 配置信息："
    echo "  🐳 Docker版本: $(docker --version)"
    echo "  🔧 Docker Compose版本: $(docker-compose --version)"
    echo "  📦 Node.js版本: $(node --version)"
    echo "  📦 npm版本: $(npm --version)"
    echo
    echo "📁 项目目录："
    echo "  📂 应用目录: /opt/linghua-app"
    echo "  📂 数据目录: /opt/data"
    echo "  📂 备份目录: /opt/backups"
    echo "  📂 脚本目录: /opt/scripts"
    echo
    echo "🔧 配置的镜像源："
    echo "  📦 npm: https://registry.npmmirror.com/"
    echo "  🐳 Docker: 阿里云镜像加速"
    echo "  📝 Git: Gitee镜像"
    echo
    echo "📝 下一步操作："
    echo "  1. 上传项目文件到 /opt/linghua-app"
    echo "  2. 配置环境变量文件"
    echo "  3. 启动Docker服务"
    echo
}

# 主函数
main() {
    echo "🚀 开始配置阿里云服务器环境"
    echo "================================"
    
    detect_os
    update_system
    setup_git_mirrors
    setup_npm_mirrors
    install_docker
    setup_docker_mirrors
    install_docker_compose
    setup_firewall
    create_project_dirs
    optimize_system
    test_network
    show_info
    
    log_success "环境配置完成！请重新登录以使Docker组权限生效。"
}

# 执行主函数
main "$@"
