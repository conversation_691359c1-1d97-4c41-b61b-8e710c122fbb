# 增强型 MCP 服务器集成规则

## 补充用户规则的 MCP 服务器使用部分

> 此文件包含对现有用户规则的补充内容，建议与原有规则合并使用

### MCP 服务器完整使用指南

#### **GitHub 集成 (GitHub Integration)**
1. 使用 `github` MCP 服务器进行代码仓库操作。
2. 优先用于：
   - 查看项目 issue 和 PR
   - 创建分支和提交
   - 搜索代码片段
   - 管理项目文档
3. 在 RESEARCH 模式：用于查看相关 issue 和 PR 历史
4. 在 EXECUTE 模式：用于提交代码更改和创建 PR

#### **知识管理 (Memory Management)**
1. 使用 `memory` MCP 服务器维护项目知识图谱。
2. 优先用于：
   - 存储项目架构知识
   - 记录重要设计决策
   - 维护组件关系图
   - 保存调试经验
3. 在 RESEARCH 模式：查询历史知识和经验
4. 在 REVIEW 模式：更新知识库

#### **文档协作 (Notion Integration)**
1. 使用 `notion` MCP 服务器管理项目文档。
2. 优先用于：
   - 维护需求文档
   - 更新开发进度
   - 管理任务清单
   - 记录会议纪要
3. 仅在用户明确要求操作 Notion 时使用

#### **浏览器自动化 (Browser Automation)**
1. 使用 `playwright-browser` MCP 服务器进行 Web 测试。
2. 优先用于：
   - UI 功能测试
   - 表单提交测试
   - 响应式设计验证
   - 性能测试
3. 在 EXECUTE 模式：执行自动化测试
4. 在 REVIEW 模式：验证功能实现

#### **高级思维 (Sequential Thinking)**
1. 使用 `sequential-thinking` MCP 服务器处理复杂问题。
2. 优先用于：
   - 复杂架构设计
   - 性能优化策略
   - 调试复杂问题
   - 多步骤任务规划
3. 在 INNOVATE 模式：深度分析和方案对比
4. 在 PLAN 模式：复杂计划的验证

### 各模式的 MCP 工具使用策略

#### RESEARCH 模式 MCP 工具组合：
- **必用**：`filesystem`（读取文件）、`interactive_feedback`（确认理解）
- **推荐**：`memory`（查询历史知识）、`github`（查看相关 PR/Issue）
- **按需**：`context7`（查找官方文档）、`time`（获取时间信息）

#### INNOVATE 模式 MCP 工具组合：
- **必用**：`interactive_feedback`（方案确认）
- **推荐**：`sequential-thinking`（复杂方案分析）、`memory`（查询设计模式）
- **按需**：`context7`（技术文档查询）

#### PLAN 模式 MCP 工具组合：
- **必用**：`interactive_feedback`（计划确认）
- **推荐**：`sequential-thinking`（计划验证）、`notion`（更新任务）
- **按需**：`memory`（记录设计决策）

#### EXECUTE 模式 MCP 工具组合：
- **必用**：`filesystem`（文件操作）、`interactive_feedback`（步骤确认）
- **推荐**：`github`（代码提交）、`playwright-browser`（功能测试）
- **按需**：`notion`（更新进度）

#### REVIEW 模式 MCP 工具组合：
- **必用**：`filesystem`（文件验证）、`interactive_feedback`（结果确认）
- **推荐**：`memory`（更新知识库）、`playwright-browser`（最终测试）
- **按需**：`github`（创建 PR）

### MCP 服务器优先级决策树

```
任务类型判断
├── 文件操作 → filesystem MCP (最高优先级)
├── 用户交互 → interactive_feedback MCP (贯穿全程)
├── 复杂分析 → sequential-thinking MCP
├── 代码管理 → github MCP
├── 知识管理 → memory MCP
├── 文档协作 → notion MCP
├── 浏览器测试 → playwright-browser MCP
├── 时间相关 → time MCP
└── 技术文档 → context7 MCP
```

### 资源管理和性能优化（针对4GB环境）

#### 并发使用限制：
1. **同时活跃 MCP 服务器不超过 3 个**
2. **优先级顺序**：interactive_feedback > filesystem > 其他
3. **重型工具延迟使用**：playwright-browser、sequential-thinking

#### 内存优化策略：
1. 使用完成的 MCP 服务器及时释放
2. 批量操作合并执行
3. 避免长时间保持浏览器会话

### MCP 工具故障回退机制

#### 主要工具故障处理：
1. **filesystem MCP 故障** → 回退到 Cursor 原生文件操作
2. **interactive_feedback MCP 故障** → 直接询问用户
3. **github MCP 故障** → 使用命令行 git 操作
4. **memory MCP 故障** → 使用临时文件记录
5. **其他 MCP 故障** → 跳过该工具，继续执行

#### 故障检测标准：
- 连续 2 次调用失败
- 响应时间超过 30 秒
- 返回错误状态码

### 更新的核心指令

在原有核心指令基础上，补充：

4. **MCP 工具最大化利用**：
   * 根据任务类型智能选择 MCP 服务器组合
   * 优先使用 MCP 工具而非传统方法
   * 遵循资源管理和性能优化原则
   * 建立故障回退机制确保任务连续性

5. **MCP 服务器协作原则**：
   * interactive_feedback 贯穿全程，确保用户参与
   * filesystem 用于所有文件操作
   * 其他 MCP 服务器按需组合使用
   * 避免功能重叠造成的资源浪费

### 示例场景应用

#### 场景1：新功能开发
1. RESEARCH：`filesystem` + `memory` + `github`
2. INNOVATE：`sequential-thinking` + `interactive_feedback`
3. PLAN：`notion` + `interactive_feedback`
4. EXECUTE：`filesystem` + `github` + `interactive_feedback`
5. REVIEW：`playwright-browser` + `memory` + `interactive_feedback`

#### 场景2：Bug 修复
1. RESEARCH：`filesystem` + `memory` + `github`
2. INNOVATE：`sequential-thinking` + `interactive_feedback`
3. PLAN：`interactive_feedback`
4. EXECUTE：`filesystem` + `interactive_feedback`
5. REVIEW：`memory` + `interactive_feedback`

#### 场景3：性能优化
1. RESEARCH：`filesystem` + `playwright-browser` + `memory`
2. INNOVATE：`sequential-thinking` + `interactive_feedback`
3. PLAN：`interactive_feedback`
4. EXECUTE：`filesystem` + `playwright-browser` + `interactive_feedback`
5. REVIEW：`memory` + `playwright-browser` + `interactive_feedback`

---

## 集成建议

将此规则与您现有的用户规则合并时，建议：

1. **替换** 现有的 "MCP 服务器使用" 部分
2. **更新** 各模式的允许/禁止操作，加入 MCP 工具使用
3. **补充** 核心指令部分
4. **保留** 现有的语言设置和基本协议

这样可以确保您的 AI 助手能够充分利用所有可用的 MCP 服务器，提高开发效率和代码质量。