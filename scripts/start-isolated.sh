#!/bin/bash

# 启动独立Docker环境脚本
# 用于启动完全独立的灵华珐琅馆ERP系统环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请先启动Docker服务"
        exit 1
    fi
    log_success "Docker服务正常运行"
}

# 检查端口是否被占用
check_ports() {
    log_info "检查端口占用情况..."
    
    ports=(5434 6381 3002 8081)
    port_names=("PostgreSQL" "Redis" "应用服务" "Adminer")
    
    for i in "${!ports[@]}"; do
        port=${ports[$i]}
        name=${port_names[$i]}
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port ($name) 已被占用"
            log_info "正在尝试查找占用进程..."
            lsof -Pi :$port -sTCP:LISTEN
            read -p "是否继续启动？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_error "用户取消启动"
                exit 1
            fi
        else
            log_success "端口 $port ($name) 可用"
        fi
    done
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    required_files=(
        "docker-compose.isolated.yml"
        ".env.isolated"
        "Dockerfile"
        "package.json"
        "prisma/schema.prisma"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "所有必要文件存在"
}

# 清理旧容器和网络
cleanup_old() {
    log_info "清理可能存在的旧容器和网络..."
    
    # 停止并删除容器
    containers=("linghua-isolated-postgres" "linghua-isolated-redis" "linghua-isolated-app" "linghua-isolated-adminer")
    for container in "${containers[@]}"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "^$container$"; then
            log_warning "发现旧容器 $container，正在删除..."
            docker rm -f "$container" >/dev/null 2>&1 || true
        fi
    done
    
    # 删除网络（如果存在）
    if docker network ls --format "table {{.Name}}" | grep -q "^linghua-isolated-network$"; then
        log_warning "发现旧网络 linghua-isolated-network，正在删除..."
        docker network rm linghua-isolated-network >/dev/null 2>&1 || true
    fi
    
    log_success "清理完成"
}

# 构建应用镜像
build_app() {
    log_info "构建应用镜像..."
    
    # 检查是否需要重新构建
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "0607linghua-enamel-gallery"; then
        read -p "发现已存在的应用镜像，是否重新构建？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过镜像构建"
            return
        fi
    fi
    
    docker build -t 0607linghua-enamel-gallery . || {
        log_error "应用镜像构建失败"
        exit 1
    }
    
    log_success "应用镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动独立Docker环境..."
    
    # 使用独立的环境变量文件启动
    docker-compose -f docker-compose.isolated.yml --env-file .env.isolated up -d || {
        log_error "服务启动失败"
        exit 1
    }
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待PostgreSQL
    log_info "等待PostgreSQL就绪..."
    timeout=60
    while ! docker exec linghua-isolated-postgres pg_isready -U postgres -d linghua_enamel_gallery_isolated >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            log_error "PostgreSQL启动超时"
            exit 1
        fi
    done
    log_success "PostgreSQL就绪"
    
    # 等待Redis
    log_info "等待Redis就绪..."
    timeout=30
    while ! docker exec linghua-isolated-redis redis-cli ping >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            log_error "Redis启动超时"
            exit 1
        fi
    done
    log_success "Redis就绪"
    
    # 等待应用服务
    log_info "等待应用服务就绪..."
    timeout=120
    while ! curl -f http://localhost:3002/api/health >/dev/null 2>&1; do
        sleep 5
        timeout=$((timeout - 5))
        if [[ $timeout -le 0 ]]; then
            log_warning "应用服务健康检查超时，但可能仍在启动中"
            break
        fi
    done
    
    if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
        log_success "应用服务就绪"
    else
        log_warning "应用服务可能仍在启动中，请稍后检查"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 设置环境变量
    export DATABASE_URL="postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated?schema=public"
    
    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    npx prisma generate || {
        log_error "Prisma客户端生成失败"
        exit 1
    }
    
    # 推送数据库模式
    log_info "推送数据库模式..."
    npx prisma db push || {
        log_error "数据库模式推送失败"
        exit 1
    }
    
    log_success "数据库初始化完成"
}

# 显示服务信息
show_info() {
    echo
    log_success "🎉 独立Docker环境启动成功！"
    echo
    echo "📋 服务信息："
    echo "  🗄️  PostgreSQL: localhost:5434"
    echo "      数据库: linghua_enamel_gallery_isolated"
    echo "      用户名: postgres"
    echo "      密码: isolated_password_2024"
    echo
    echo "  🔄 Redis: localhost:6381"
    echo
    echo "  🌐 应用服务: http://localhost:3002"
    echo
    echo "  🛠️  Adminer (数据库管理): http://localhost:8081"
    echo "      服务器: linghua-isolated-postgres"
    echo "      用户名: postgres"
    echo "      密码: isolated_password_2024"
    echo "      数据库: linghua_enamel_gallery_isolated"
    echo
    echo "📝 管理命令："
    echo "  停止服务: ./scripts/stop-isolated.sh"
    echo "  查看日志: docker-compose -f docker-compose.isolated.yml logs -f"
    echo "  查看状态: docker-compose -f docker-compose.isolated.yml ps"
    echo
}

# 主函数
main() {
    echo "🚀 启动灵华珐琅馆独立Docker环境"
    echo "=================================="
    
    check_docker
    check_files
    check_ports
    cleanup_old
    build_app
    start_services
    wait_for_services
    init_database
    show_info
    
    log_success "启动流程完成！"
}

# 执行主函数
main "$@"
