/**
 * 类型定义统一导出文件
 * 提供所有类型定义的集中导入入口
 */

// 业务模型类型
export * from './product'
export * from './finance'
export * from './user'
export * from './artwork'

// Prisma模型类型
export * from './prisma-models'

// 认证类型 - 声明文件，不能导出

// 通用类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginationParams {
  page: number
  limit: number
  total?: number
}

export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface SortParams {
  field: string
  order: 'asc' | 'desc'
}

export interface FilterParams {
  [key: string]: any
}

export interface SearchParams {
  query?: string
  filters?: FilterParams
  sort?: SortParams
  pagination?: PaginationParams
}

// 表单相关类型
export interface FormState {
  isLoading: boolean
  error: string | null
  success: string | null
}

export interface ValidationError {
  field: string
  message: string
}

// 系统状态类型
export interface SystemStatus {
  status: 'healthy' | 'warning' | 'error'
  message?: string
  timestamp: Date
}

// 权限相关类型
export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: Permission[]
}

// 审计日志类型
export interface AuditLog {
  id: string
  userId: string
  action: string
  resource: string
  details?: any
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}
