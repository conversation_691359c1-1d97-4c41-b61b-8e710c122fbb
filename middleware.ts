/**
 * 全局中间件 - 认证 + 安全性增强
 */

import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// 简化的速率限制存储
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// 获取客户端IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'
  return ip
}

// 简单的速率限制检查
function checkRateLimit(ip: string, limit: number = 100, windowMs: number = 15 * 60 * 1000): boolean {
  const now = Date.now()
  const key = `${ip}`
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= limit) {
    return false
  }

  record.count++
  return true
}

// 检测基本的攻击模式
function detectMaliciousPatterns(url: string, headers: Headers): boolean {
  const urlLower = url.toLowerCase()
  const userAgent = headers.get('user-agent')?.toLowerCase() || ''

  // XSS 模式
  const xssPatterns = [
    '<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=', '<iframe', '<object'
  ]

  // SQL注入模式
  const sqlPatterns = [
    'union select', 'drop table', 'delete from', '1=1', '1=\'1\'', 'or 1=1'
  ]

  // 路径遍历模式
  const pathTraversalPatterns = [
    '../', '..\\', '%2e%2e%2f', '%2e%2e%5c'
  ]

  const allPatterns = [...xssPatterns, ...sqlPatterns, ...pathTraversalPatterns]
  
  return allPatterns.some(pattern => 
    urlLower.includes(pattern) || userAgent.includes(pattern)
  )
}

// 设置安全头部
function setSecurityHeaders(response: NextResponse): NextResponse {
  // 基本安全头部
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // 生产环境下的HTTPS头部
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  }

  return response
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const clientIP = getClientIP(request)

  // 公开路径，不需要认证
  const publicPaths = [
    "/login",
    "/register", 
    "/forgot-password",
    "/reset-password",
    "/unauthorized",
    "/login-debug",
    "/login-test",
    "/api/health",
    "/api/status"
  ]

  // 静态资源路径，不需要认证
  const staticPaths = [
    "/_next",
    "/favicon.ico",
    "/placeholder.svg", 
    "/placeholder-logo.png",
    "/images",
    "/fonts"
  ]

  const isPublicPath = publicPaths.some((path) => pathname.startsWith(path))
  const isStaticPath = staticPaths.some((path) => pathname.startsWith(path))
  const isNextAuthPath = pathname.startsWith("/api/auth")
  const isApiPath = pathname.startsWith("/api")

  // 静态资源直接放行
  if (isStaticPath) {
    return NextResponse.next()
  }

  try {
    // 1. 恶意模式检测
    if (detectMaliciousPatterns(request.url, request.headers)) {
      console.warn(`[SECURITY] Malicious pattern detected from ${clientIP}: ${pathname}`)
      return new NextResponse('Forbidden', { status: 403 })
    }

    // 2. 速率限制
    let rateLimit = 100 // 默认限制
    if (pathname.startsWith('/api/auth')) {
      rateLimit = 10 // 认证端点更严格
    } else if (pathname.startsWith('/api/upload')) {
      rateLimit = 20 // 上传端点限制
    }

    if (!checkRateLimit(clientIP, rateLimit)) {
      console.warn(`[RATE_LIMIT] IP ${clientIP} exceeded rate limit for ${pathname}`)
      return new NextResponse('Too Many Requests', { status: 429 })
    }

    // 3. NextAuth API路径放行（已通过速率限制）
    if (isNextAuthPath) {
      return NextResponse.next()
    }

    // 4. 检查会话Cookie
    const authCookie = request.cookies.get("next-auth.session-token") ||
                      request.cookies.get("__Secure-next-auth.session-token")
    const hasSession = !!authCookie?.value

    // 5. 如果是公开路径且用户已登录，重定向到仪表盘
    if (isPublicPath && hasSession && pathname !== "/logout") {
      console.log("用户已登录，从", pathname, "重定向到 /dashboard")
      return NextResponse.redirect(new URL("/dashboard", request.url))
    }

    // 6. 如果不是公开路径且用户未登录，重定向到登录页
    if (!isPublicPath && !hasSession && !isApiPath) {
      const url = new URL("/login", request.url)
      url.searchParams.set("callbackUrl", encodeURI(request.url))
      return NextResponse.redirect(url)
    }

    // 7. API路径需要会话验证
    if (isApiPath && !isPublicPath && !hasSession) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // 8. 创建响应并设置安全头部
    const response = NextResponse.next()
    setSecurityHeaders(response)
    
    // 添加请求追踪头部
    response.headers.set('X-Request-ID', `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`)
    
    return response

  } catch (error) {
    console.error('[MIDDLEWARE ERROR]', error)
    
    // 出错时设置安全头部并继续
    const response = NextResponse.next()
    setSecurityHeaders(response)
    response.headers.set('X-Middleware-Error', 'true')
    
    return response
  }
}

// 匹配所有路径，除了静态资源
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - 静态资源 (_next/static, _next/image, favicon.ico等)
     * - 公共资源 (images, fonts等)
     */
    "/((?!_next/static|_next/image|_next/data|favicon.ico|images/|fonts/|placeholder.svg|placeholder-logo.png).*)"
  ],
}
