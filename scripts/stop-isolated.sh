#!/bin/bash

# 停止独立Docker环境脚本
# 用于停止和清理完全独立的灵华珐琅馆ERP系统环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，无法执行停止操作"
        exit 1
    fi
    log_success "Docker服务正常运行"
}

# 停止Docker Compose服务
stop_compose_services() {
    log_info "停止Docker Compose服务..."
    
    if [[ -f "docker-compose.isolated.yml" ]]; then
        docker-compose -f docker-compose.isolated.yml down || {
            log_warning "Docker Compose停止过程中出现警告，继续执行清理..."
        }
        log_success "Docker Compose服务已停止"
    else
        log_warning "未找到 docker-compose.isolated.yml 文件"
    fi
}

# 强制停止和删除容器
force_stop_containers() {
    log_info "强制停止和删除独立环境容器..."
    
    containers=("linghua-isolated-postgres" "linghua-isolated-redis" "linghua-isolated-app" "linghua-isolated-adminer")
    
    for container in "${containers[@]}"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "^$container$"; then
            log_info "停止并删除容器: $container"
            docker rm -f "$container" >/dev/null 2>&1 || {
                log_warning "删除容器 $container 时出现问题，继续..."
            }
        else
            log_info "容器 $container 不存在，跳过"
        fi
    done
    
    log_success "容器清理完成"
}

# 删除网络
remove_network() {
    log_info "删除独立网络..."
    
    if docker network ls --format "table {{.Name}}" | grep -q "^linghua-isolated-network$"; then
        docker network rm linghua-isolated-network >/dev/null 2>&1 || {
            log_warning "删除网络时出现问题，可能仍有容器在使用"
        }
        log_success "网络 linghua-isolated-network 已删除"
    else
        log_info "网络 linghua-isolated-network 不存在，跳过"
    fi
}

# 清理数据卷（可选）
cleanup_volumes() {
    echo
    read -p "是否删除数据卷？这将永久删除所有数据！(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_warning "正在删除数据卷..."
        
        volumes=("linghua_isolated_postgres_data" "linghua_isolated_redis_data" "linghua_isolated_app_cache")
        
        for volume in "${volumes[@]}"; do
            if docker volume ls --format "table {{.Name}}" | grep -q "^$volume$"; then
                docker volume rm "$volume" >/dev/null 2>&1 || {
                    log_warning "删除数据卷 $volume 时出现问题"
                }
                log_info "数据卷 $volume 已删除"
            else
                log_info "数据卷 $volume 不存在，跳过"
            fi
        done
        
        log_success "数据卷清理完成"
    else
        log_info "保留数据卷，下次启动时数据将被保留"
    fi
}

# 清理未使用的Docker资源
cleanup_docker_resources() {
    echo
    read -p "是否清理未使用的Docker资源（镜像、网络、卷）？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理未使用的Docker资源..."
        docker system prune -f >/dev/null 2>&1 || {
            log_warning "清理Docker资源时出现问题"
        }
        log_success "Docker资源清理完成"
    else
        log_info "跳过Docker资源清理"
    fi
}

# 检查端口释放情况
check_ports_released() {
    log_info "检查端口释放情况..."
    
    ports=(5434 6381 3002 8081)
    port_names=("PostgreSQL" "Redis" "应用服务" "Adminer")
    
    for i in "${!ports[@]}"; do
        port=${ports[$i]}
        name=${port_names[$i]}
        
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port ($name) 仍被占用"
            log_info "占用进程信息："
            lsof -Pi :$port -sTCP:LISTEN
        else
            log_success "端口 $port ($name) 已释放"
        fi
    done
}

# 显示清理结果
show_cleanup_result() {
    echo
    log_success "🧹 独立Docker环境清理完成！"
    echo
    echo "📋 清理结果："
    echo "  ✅ 容器已停止并删除"
    echo "  ✅ 网络已删除"
    echo "  ✅ 端口已释放"
    echo
    echo "📝 相关文件："
    echo "  📄 配置文件: docker-compose.isolated.yml (保留)"
    echo "  📄 环境变量: .env.isolated (保留)"
    echo "  📄 启动脚本: scripts/start-isolated.sh (保留)"
    echo
    echo "🔄 重新启动："
    echo "  ./scripts/start-isolated.sh"
    echo
}

# 主函数
main() {
    echo "🛑 停止灵华珐琅馆独立Docker环境"
    echo "=================================="
    
    check_docker
    stop_compose_services
    force_stop_containers
    remove_network
    cleanup_volumes
    cleanup_docker_resources
    check_ports_released
    show_cleanup_result
    
    log_success "停止流程完成！"
}

# 执行主函数
main "$@"
