import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

export async function GET() {
  try {
    // 查询咖啡店采购订单
    const purchases = await prisma.purchaseOrder.findMany({
      where: {
        type: 'COFFEE_SHOP' // 假设有类型字段区分咖啡店采购
      },
      include: {
        supplier: true,
        items: {
          include: {
            product: true
          }
        }
      },
      orderBy: {
        orderDate: 'desc'
      }
    })

    // 格式化数据
    const formattedPurchases = purchases.map(purchase => ({
      id: purchase.id,
      date: purchase.orderDate?.toISOString().split('T')[0] || '',
      supplier: purchase.supplier?.name || '未知供应商',
      items: purchase.items.map(item => ({
        name: item.product?.name || item.description || '未知商品',
        quantity: item.quantity,
        unit: item.unit || '个',
        unitPrice: item.unitPrice || 0
      })),
      totalAmount: purchase.totalAmount || 0,
      status: purchase.status,
      notes: purchase.notes
    }))

    return NextResponse.json(formattedPurchases)
  } catch (error) {
    console.error('咖啡店采购查询失败:', error)
    
    // 返回示例数据
    const samplePurchases = [
      {
        id: '1',
        date: '2025-06-07',
        supplier: '优质咖啡豆供应商',
        items: [
          { name: '蓝山咖啡豆', quantity: 10, unit: 'kg', unitPrice: 280 },
          { name: '意式浓缩豆', quantity: 15, unit: 'kg', unitPrice: 180 }
        ],
        totalAmount: 5500,
        status: 'DELIVERED',
        notes: '优质咖啡豆，口感醇厚'
      },
      {
        id: '2',
        date: '2025-06-06',
        supplier: '烘焙设备公司',
        items: [
          { name: '咖啡杯', quantity: 100, unit: '个', unitPrice: 15 },
          { name: '搅拌棒', quantity: 200, unit: '个', unitPrice: 2 }
        ],
        totalAmount: 1900,
        status: 'PENDING',
        notes: '日常用品补充'
      },
      {
        id: '3',
        date: '2025-06-05',
        supplier: '食材供应商',
        items: [
          { name: '牛奶', quantity: 50, unit: 'L', unitPrice: 8 },
          { name: '糖浆', quantity: 20, unit: '瓶', unitPrice: 25 }
        ],
        totalAmount: 900,
        status: 'CONFIRMED',
        notes: '新鲜食材'
      }
    ]

    return NextResponse.json(samplePurchases)
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // 创建新的采购订单
    const newPurchase = await prisma.purchaseOrder.create({
      data: {
        orderDate: new Date(body.date),
        supplierId: body.supplierId,
        totalAmount: body.totalAmount,
        status: body.status || 'PENDING',
        notes: body.notes,
        type: 'COFFEE_SHOP',
        items: {
          create: body.items.map((item: any) => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            unit: item.unit,
            description: item.name
          }))
        }
      },
      include: {
        supplier: true,
        items: {
          include: {
            product: true
          }
        }
      }
    })

    return NextResponse.json(newPurchase)
  } catch (error) {
    console.error('创建采购订单失败:', error)
    return NextResponse.json(
      { error: '创建采购订单失败' },
      { status: 500 }
    )
  }
}
