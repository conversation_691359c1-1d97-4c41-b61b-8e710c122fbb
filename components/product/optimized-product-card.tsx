"use client"

import React, { memo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LazyImage } from "@/components/ui/lazy-image"
import { Product } from "@/types/product"
import {
  EditIcon,
  TrashIcon,
  ImageIcon,
  MoreHorizontalIcon,
  TagIcon
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface OptimizedProductCardProps {
  product: Product
  isSelected: boolean
  onSelect: (productId: number, checked: boolean) => void
  onEdit: (product: Product) => void
  onDelete: (productId: number) => void
  onImagePreview: (imageUrl: string) => void
  onDoubleClickEdit: (productId: number, field: string, value: string | number) => void
}

export const OptimizedProductCard = memo<OptimizedProductCardProps>(({
  product,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onImagePreview,
  onDoubleClickEdit
}) => {
  // 使用useCallback优化事件处理函数
  const handleSelect = useCallback((checked: boolean) => {
    onSelect(product.id!, checked)
  }, [product.id, onSelect])

  const handleEdit = useCallback(() => {
    onEdit(product)
  }, [product, onEdit])

  const handleDelete = useCallback(() => {
    onDelete(product.id!)
  }, [product.id, onDelete])

  const handleImageClick = useCallback(() => {
    if (product.imageUrl) {
      onImagePreview(product.imageUrl)
    }
  }, [product.imageUrl, onImagePreview])

  const handleNameDoubleClick = useCallback(() => {
    onDoubleClickEdit(product.id!, 'name', product.name)
  }, [product.id, product.name, onDoubleClickEdit])

  const handlePriceDoubleClick = useCallback(() => {
    onDoubleClickEdit(product.id!, 'price', product.price)
  }, [product.id, product.price, onDoubleClickEdit])

  return (
    <Card className={`h-full transition-colors ${isSelected ? 'border-primary bg-primary/5' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={isSelected}
              onCheckedChange={handleSelect}
            />
            <CardTitle 
              className="text-sm font-medium cursor-pointer hover:text-primary"
              onDoubleClick={handleNameDoubleClick}
            >
              {product.name}
            </CardTitle>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <EditIcon className="h-4 w-4 mr-2" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                <TrashIcon className="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 产品图片 */}
        <div 
          className="aspect-square rounded-md overflow-hidden bg-muted cursor-pointer"
          onClick={handleImageClick}
        >
          {product.imageUrl ? (
            <LazyImage
              src={product.imageUrl}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ImageIcon className="h-8 w-8 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* 产品信息 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span 
              className="text-lg font-semibold cursor-pointer hover:text-primary"
              onDoubleClick={handlePriceDoubleClick}
            >
              ¥{product.price.toFixed(2)}
            </span>
            {product.categoryName && (
              <Badge variant="secondary" className="text-xs">
                {product.categoryName}
              </Badge>
            )}
          </div>

          {/* 标签 */}
          {product.tags && product.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {product.tags.slice(0, 3).map((tag) => (
                <Badge key={tag.id} variant="outline" className="text-xs">
                  <TagIcon className="h-3 w-3 mr-1" />
                  {tag.name}
                </Badge>
              ))}
              {product.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{product.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* 库存信息 */}
          {product.inventory !== null && (
            <div className="text-sm text-muted-foreground">
              库存: {product.inventory}
            </div>
          )}

          {/* 描述 */}
          {product.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {product.description}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
})

OptimizedProductCard.displayName = 'OptimizedProductCard'