# 🛠️ 性能优化实施指南

## 📋 **第一阶段：立即实施 (本周)**

### 1. 配置文件统一 ⏱️ 2小时

#### 步骤1: 备份现有配置
```bash
cp next.config.mjs next.config.mjs.backup
cp next.config.optimized.mjs next.config.optimized.mjs.backup
```

#### 步骤2: 合并配置文件
```javascript
// next.config.mjs (最终版本)
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'development'
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'development'
  },
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
    turbo: { memoryLimit: 512 }
  },
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200],
    imageSizes: [16, 32, 48, 64, 96],
    minimumCacheTTL: 60 * 60 * 24 * 30,
    unoptimized: process.env.NODE_ENV === 'development'
  },
  output: 'standalone',
  compress: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false
  },
  webpack: (config, { dev }) => {
    if (!dev) {
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 200000,
        cacheGroups: {
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
            name: 'ui',
            priority: 10,
            chunks: 'all',
            maxSize: 150000
          },
          utils: {
            test: /[\\/]node_modules[\\/](date-fns|lodash|clsx|class-variance-authority)[\\/]/,
            name: 'utils',
            priority: 5,
            chunks: 'all',
            maxSize: 100000
          }
        }
      }
    }
    return config
  }
}

export default nextConfig
```

#### 步骤3: 删除冗余文件
```bash
rm next.config.optimized.mjs
```

#### 验证
```bash
npm run build
npm run start
# 检查应用是否正常运行
```

### 2. 依赖包分析和优化 ⏱️ 4小时

#### 步骤1: 安装分析工具
```bash
npm install --save-dev @next/bundle-analyzer
npm install --save-dev depcheck
```

#### 步骤2: 分析bundle大小
```javascript
// next.config.mjs 添加
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true'
})

module.exports = withBundleAnalyzer(nextConfig)
```

```bash
ANALYZE=true npm run build
```

#### 步骤3: 检查未使用的依赖
```bash
npx depcheck
```

#### 步骤4: 优化导入方式
```typescript
// 优化前
import { Button, Dialog, Select, Input } from '@radix-ui/react-*'

// 优化后
import { Button } from '@radix-ui/react-button'
import { Dialog } from '@radix-ui/react-dialog'
import { Select } from '@radix-ui/react-select'
import { Input } from '@radix-ui/react-input'
```

#### 步骤5: 移除未使用的包
```bash
npm uninstall [未使用的包名]
```

### 3. 缓存策略统一 ⏱️ 6小时

#### 步骤1: 创建统一缓存管理器
```typescript
// lib/cache/unified-cache-manager.ts
import { LRUCache } from 'lru-cache'

interface CacheOptions {
  ttl?: number
  maxSize?: number
  strategy?: 'LRU' | 'LFU' | 'FIFO'
}

export class UnifiedCacheManager {
  private caches = new Map<string, LRUCache<string, any>>()
  
  getCache(name: string, options: CacheOptions = {}) {
    if (!this.caches.has(name)) {
      const cache = new LRUCache({
        max: options.maxSize || 100,
        ttl: options.ttl || 5 * 60 * 1000 // 5分钟默认
      })
      this.caches.set(name, cache)
    }
    return this.caches.get(name)!
  }
  
  clearAll() {
    this.caches.forEach(cache => cache.clear())
  }
  
  getStats() {
    const stats: Record<string, any> = {}
    this.caches.forEach((cache, name) => {
      stats[name] = {
        size: cache.size,
        max: cache.max,
        calculatedSize: cache.calculatedSize
      }
    })
    return stats
  }
}

export const cacheManager = new UnifiedCacheManager()
```

#### 步骤2: 创建缓存实例
```typescript
// lib/cache/cache-instances.ts
import { cacheManager } from './unified-cache-manager'

export const caches = {
  api: cacheManager.getCache('api', {
    ttl: 5 * 60 * 1000, // 5分钟
    maxSize: 200
  }),
  
  user: cacheManager.getCache('user', {
    ttl: 30 * 60 * 1000, // 30分钟
    maxSize: 50
  }),
  
  static: cacheManager.getCache('static', {
    ttl: 60 * 60 * 1000, // 1小时
    maxSize: 100
  }),
  
  database: cacheManager.getCache('database', {
    ttl: 10 * 60 * 1000, // 10分钟
    maxSize: 500
  })
}
```

#### 步骤3: 更新API路由使用统一缓存
```typescript
// app/api/products/route.ts
import { caches } from '@/lib/cache/cache-instances'

export async function GET(request: NextRequest) {
  const cacheKey = `products-${request.url}`
  
  // 检查缓存
  const cached = caches.api.get(cacheKey)
  if (cached) {
    return NextResponse.json(cached, {
      headers: { 'X-Cache': 'HIT' }
    })
  }
  
  // 获取数据
  const products = await prisma.product.findMany()
  
  // 缓存结果
  caches.api.set(cacheKey, products)
  
  return NextResponse.json(products, {
    headers: { 'X-Cache': 'MISS' }
  })
}
```

## 📊 **第二阶段：短期实施 (下周)**

### 4. React组件memo化 ⏱️ 8小时

#### 步骤1: 识别高频渲染组件
```bash
# 安装React DevTools Profiler
npm install --save-dev @welldone-software/why-did-you-render
```

#### 步骤2: 优化产品卡片组件
```typescript
// components/product-card.tsx
import React, { memo, useCallback } from 'react'

interface ProductCardProps {
  product: Product
  onSelect: (id: string) => void
  isSelected: boolean
}

export const ProductCard = memo<ProductCardProps>(({ 
  product, 
  onSelect, 
  isSelected 
}) => {
  const handleClick = useCallback(() => {
    onSelect(product.id)
  }, [product.id, onSelect])
  
  return (
    <div 
      className={`product-card ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
    >
      <h3>{product.name}</h3>
      <p>{product.price}</p>
    </div>
  )
})

ProductCard.displayName = 'ProductCard'
```

#### 步骤3: 优化列表组件
```typescript
// components/product-list.tsx
import React, { memo, useMemo } from 'react'
import { ProductCard } from './product-card'

interface ProductListProps {
  products: Product[]
  selectedIds: string[]
  onProductSelect: (id: string) => void
}

export const ProductList = memo<ProductListProps>(({ 
  products, 
  selectedIds, 
  onProductSelect 
}) => {
  const selectedSet = useMemo(() => 
    new Set(selectedIds), 
    [selectedIds]
  )
  
  return (
    <div className="product-list">
      {products.map(product => (
        <ProductCard
          key={product.id}
          product={product}
          onSelect={onProductSelect}
          isSelected={selectedSet.has(product.id)}
        />
      ))}
    </div>
  )
})

ProductList.displayName = 'ProductList'
```

### 5. 数据库查询优化 ⏱️ 12小时

#### 步骤1: 添加数据库索引
```sql
-- 在数据库中执行
CREATE INDEX IF NOT EXISTS idx_product_category_id ON "Product"(category_id);
CREATE INDEX IF NOT EXISTS idx_product_created_at ON "Product"(created_at);
CREATE INDEX IF NOT EXISTS idx_order_status ON "Order"(status);
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON "InventoryItem"(product_id);
```

#### 步骤2: 优化Prisma查询
```typescript
// lib/queries/product-queries.ts
import { caches } from '@/lib/cache/cache-instances'

export async function getProductsWithCategories(page = 1, limit = 20) {
  const cacheKey = `products-with-categories-${page}-${limit}`
  
  // 检查缓存
  const cached = caches.database.get(cacheKey)
  if (cached) return cached
  
  const skip = (page - 1) * limit
  
  const [products, total] = await Promise.all([
    prisma.product.findMany({
      skip,
      take: limit,
      include: {
        category: {
          select: { id: true, name: true }
        },
        _count: {
          select: { orderItems: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.product.count()
  ])
  
  const result = { products, total, pages: Math.ceil(total / limit) }
  
  // 缓存结果
  caches.database.set(cacheKey, result)
  
  return result
}
```

#### 步骤3: 实施批量查询
```typescript
// lib/queries/batch-queries.ts
export async function batchGetProductsByIds(ids: string[]) {
  // 检查缓存中已有的产品
  const cached: Product[] = []
  const uncachedIds: string[] = []
  
  ids.forEach(id => {
    const cachedProduct = caches.database.get(`product-${id}`)
    if (cachedProduct) {
      cached.push(cachedProduct)
    } else {
      uncachedIds.push(id)
    }
  })
  
  // 批量查询未缓存的产品
  let uncachedProducts: Product[] = []
  if (uncachedIds.length > 0) {
    uncachedProducts = await prisma.product.findMany({
      where: { id: { in: uncachedIds } }
    })
    
    // 缓存新查询的产品
    uncachedProducts.forEach(product => {
      caches.database.set(`product-${product.id}`, product)
    })
  }
  
  return [...cached, ...uncachedProducts]
}
```

## ✅ **验证和测试**

### 性能测试脚本
```bash
#!/bin/bash
# performance-test.sh

echo "🧪 开始性能测试..."

# 1. 构建应用
echo "📦 构建应用..."
npm run build

# 2. 启动应用
echo "🚀 启动应用..."
npm run start &
APP_PID=$!

# 等待应用启动
sleep 10

# 3. 运行Lighthouse测试
echo "💡 运行Lighthouse测试..."
npx lighthouse http://localhost:3000 --output=json --output-path=./lighthouse-report.json

# 4. 测试API性能
echo "⚡ 测试API性能..."
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3000/api/products"

# 5. 停止应用
kill $APP_PID

echo "✅ 性能测试完成!"
```

### 监控脚本
```typescript
// scripts/monitor-performance.ts
export async function monitorPerformance() {
  const results = {
    timestamp: new Date().toISOString(),
    lighthouse: await runLighthouseTest(),
    bundleSize: await analyzeBundleSize(),
    memoryUsage: process.memoryUsage(),
    cacheStats: cacheManager.getStats()
  }
  
  console.log('📊 性能监控结果:', results)
  
  // 保存结果到文件
  await fs.writeFile(
    `performance-report-${Date.now()}.json`,
    JSON.stringify(results, null, 2)
  )
  
  return results
}
```

## 📈 **成功指标**

### 第一阶段目标
- [ ] 配置文件统一完成
- [ ] Bundle大小减少 > 15%
- [ ] 缓存命中率 > 70%
- [ ] 构建时间减少 > 10%

### 第二阶段目标
- [ ] 组件渲染性能提升 > 30%
- [ ] 数据库查询时间减少 > 40%
- [ ] API响应时间减少 > 25%
- [ ] 内存使用减少 > 15%

---

**实施指南版本**: v1.0  
**最后更新**: 2025-06-24
