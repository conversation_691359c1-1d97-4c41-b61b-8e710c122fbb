import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { contactName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    const [channels, total] = await Promise.all([
      prisma.channel.findMany({
        where,
        include: {
          _count: {
            select: {
              inventory: true,
              prices: true,
              deposits: true,
              sales: true,
              settlements: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.channel.count({ where }),
    ]);

    return NextResponse.json({
      data: channels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching channels:', error);
    return NextResponse.json(
      { error: 'Failed to fetch channels' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // 验证必填字段
    if (!data.name || !data.code || !data.type) {
      return NextResponse.json(
        { error: '渠道名称、编码和类型为必填项' },
        { status: 400 }
      );
    }

    // 检查编码是否已存在
    const existingChannel = await prisma.channel.findUnique({
      where: { code: data.code },
    });

    if (existingChannel) {
      return NextResponse.json(
        { error: '渠道编码已存在' },
        { status: 400 }
      );
    }

    // 处理日期字段
    let cooperationStart = undefined;
    if (data.cooperationStart) {
      cooperationStart = new Date(data.cooperationStart);
    }

    const channel = await prisma.channel.create({
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        description: data.description,
        contactName: data.contactName,
        contactPhone: data.contactPhone,
        contactEmail: data.contactEmail,
        address: data.address,
        bankName: data.bankName,
        bankAccount: data.bankAccount,
        settlementCycle: data.settlementCycle ? parseInt(data.settlementCycle) : 1,
        cooperationStart,
        status: data.status || 'active',
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
      include: {
        _count: {
          select: {
            inventory: true,
            prices: true,
            deposits: true,
            sales: true,
            settlements: true,
          },
        },
      },
    });

    return NextResponse.json(channel, { status: 201 });
  } catch (error) {
    console.error('Error creating channel:', error);
    return NextResponse.json(
      { error: 'Failed to create channel' },
      { status: 500 }
    );
  }
}