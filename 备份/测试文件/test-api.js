const http = require('http')

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000
    }

    const req = http.request(options, (res) => {
      let body = ''
      res.on('data', (chunk) => {
        body += chunk
      })
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body)
          resolve({ 
            status: res.statusCode, 
            data: jsonBody,
            success: res.statusCode >= 200 && res.statusCode < 300
          })
        } catch (e) {
          resolve({ 
            status: res.statusCode, 
            data: body.substring(0, 100) + '...',
            success: false,
            error: 'Invalid JSON response'
          })
        }
      })
    })

    req.on('error', (error) => {
      resolve({ status: 0, error: error.message, success: false })
    })

    req.on('timeout', () => {
      resolve({ status: 0, error: 'timeout', success: false })
    })

    if (data) {
      req.write(JSON.stringify(data))
    }
    req.end()
  })
}

async function runAPITests() {
  console.log('🔄 开始API功能测试...\n')

  const tests = [
    { name: '状态检查', path: '/api/status' },
    { name: '数据库测试', path: '/api/test' },
    { name: '产品列表', path: '/api/products' },
    { name: '库存列表', path: '/api/inventory' },
  ]

  for (const test of tests) {
    console.log(`测试: ${test.name}`)
    const result = await testAPI(test.path)
    
    if (result.success) {
      console.log(`✅ 成功 (${result.status})`)
      if (result.data && typeof result.data === 'object') {
        console.log(`   响应: ${JSON.stringify(result.data).substring(0, 100)}...`)
      }
    } else {
      console.log(`❌ 失败 (${result.status || 'ERROR'})`)
      console.log(`   错误: ${result.error || '未知错误'}`)
    }
    console.log('')
  }

  console.log('🎉 API测试完成!')
}

runAPITests()
