/**
 * 预定义的缓存实例
 * 为不同用途创建优化的缓存配置
 */

import { cacheManager } from './unified-cache-manager'

// API响应缓存
export const apiCache = cacheManager.getCache('api', {
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 200,
  strategy: 'LRU',
  persistent: true
})

// 用户数据缓存
export const userCache = cacheManager.getCache('user', {
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 50,
  strategy: 'LFU',
  persistent: true
})

// 静态数据缓存（字典、配置等）
export const staticCache = cacheManager.getCache('static', {
  ttl: 60 * 60 * 1000, // 1小时
  maxSize: 100,
  strategy: 'FIFO',
  persistent: true,
  compression: true
})

// 数据库查询缓存
export const databaseCache = cacheManager.getCache('database', {
  ttl: 10 * 60 * 1000, // 10分钟
  maxSize: 500,
  strategy: 'LRU',
  persistent: true
})

// 临时数据缓存
export const tempCache = cacheManager.getCache('temp', {
  ttl: 60 * 1000, // 1分钟
  maxSize: 50,
  strategy: 'LRU',
  persistent: false
})

// 图片缓存
export const imageCache = cacheManager.getCache('images', {
  ttl: 24 * 60 * 60 * 1000, // 24小时
  maxSize: 500,
  strategy: 'LRU',
  persistent: true,
  compression: true
})

// 会话缓存
export const sessionCache = cacheManager.getCache('session', {
  ttl: 60 * 60 * 1000, // 1小时
  maxSize: 100,
  strategy: 'LRU',
  persistent: false
})

// 导出所有缓存实例
export const caches = {
  api: apiCache,
  user: userCache,
  static: staticCache,
  database: databaseCache,
  temp: tempCache,
  images: imageCache,
  session: sessionCache
}

// 缓存工具函数
export const cacheUtils = {
  // 获取所有缓存统计
  getAllStats: () => cacheManager.getAllStats(),
  
  // 清空所有缓存
  clearAll: () => cacheManager.clearAll(),
  
  // 清理过期项
  cleanup: () => cacheManager.cleanupExpired(),
  
  // 获取缓存总内存使用
  getTotalMemoryUsage: () => {
    const stats = cacheManager.getAllStats()
    return Object.values(stats).reduce((total, stat) => total + stat.memoryUsage, 0)
  },
  
  // 获取缓存总命中率
  getOverallHitRate: () => {
    const stats = cacheManager.getAllStats()
    const totalHits = Object.values(stats).reduce((total, stat) => total + stat.hitCount, 0)
    const totalRequests = Object.values(stats).reduce((total, stat) => total + stat.hitCount + stat.missCount, 0)
    return totalRequests > 0 ? totalHits / totalRequests : 0
  }
}

// 缓存装饰器
export function cached(
  cacheName: keyof typeof caches = 'api',
  ttl?: number,
  keyGenerator?: (...args: any[]) => string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const cache = caches[cacheName]

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator 
        ? keyGenerator(...args)
        : `${propertyName}_${JSON.stringify(args)}`

      // 尝试从缓存获取
      const cached = cache.get(key)
      if (cached !== null) {
        return cached
      }

      // 执行原方法并缓存结果
      const result = await method.apply(this, args)
      cache.set(key, result, ttl)
      
      return result
    }

    return descriptor
  }
}

// 缓存中间件（用于API路由）
export function withCache(
  cacheName: keyof typeof caches = 'api',
  ttl?: number,
  keyGenerator?: (request: Request) => string
) {
  return function (handler: Function) {
    return async function (request: Request, ...args: any[]) {
      const cache = caches[cacheName]
      const key = keyGenerator 
        ? keyGenerator(request)
        : `${request.url}_${request.method}`

      // 检查缓存
      const cached = cache.get(key)
      if (cached) {
        return new Response(JSON.stringify(cached), {
          headers: { 
            'Content-Type': 'application/json',
            'X-Cache': 'HIT'
          }
        })
      }

      // 执行处理程序
      const response = await handler(request, ...args)
      
      // 缓存响应（如果是成功响应）
      if (response.ok) {
        try {
          const data = await response.clone().json()
          cache.set(key, data, ttl)
        } catch (error) {
          // 忽略非JSON响应
        }
      }

      // 添加缓存头
      response.headers.set('X-Cache', 'MISS')
      
      return response
    }
  }
}