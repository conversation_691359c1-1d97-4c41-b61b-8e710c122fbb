# Story {EpicNum}.{StoryNum}: {Short Title Copied from Epic File}

## Status: { Draft | Approved | InProgress | Review | Done }

## Story

- As a [role]
- I want [action]
- so that [benefit]

## Acceptance Criteria (ACs)

{ Copy the Acceptance Criteria numbered list }

## Tasks / Subtasks

- [ ] Task 1 (AC: # if applicable)
  - [ ] Subtask1.1...
- [ ] Task 2 (AC: # if applicable)
  - [ ] Subtask 2.1...
- [ ] Task 3 (AC: # if applicable)
  - [ ] Subtask 3.1...

## Dev Technical Guidance {detail not covered in tasks/subtasks}

## Story Progress Notes

### Agent Model Used: `<Agent Model Name/Version>`

### Completion Notes List

{Any notes about implementation choices, difficulties, or follow-up needed}

### Change Log
