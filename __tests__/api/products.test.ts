import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest'
import { GET, POST, PUT, DELETE } from '@/app/api/products/route'
import { GET as getById, PUT as updateById, DELETE as deleteById } from '@/app/api/products/[id]/route'
import { 
  testPrisma, 
  createMockRequest, 
  mockSession, 
  setupTestDatabase,
  cleanupTestDatabase,
  expectSuccessResponse,
  expectErrorResponse,
  validateProductData,
  testUsers
} from './api-test-utils'

// Mock NextAuth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}))

describe('Products API', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
    await testPrisma.$disconnect()
  })

  beforeEach(async () => {
    // 清理产品数据
    await testPrisma.product.deleteMany()
    await testPrisma.productCategory.deleteMany()
    
    // 创建测试分类
    await testPrisma.productCategory.create({
      data: {
        id: 'test-category-1',
        name: '测试分类',
        description: '测试用分类'
      }
    })
  })

  describe('GET /api/products', () => {
    it('应该返回产品列表', async () => {
      // 创建测试产品
      await testPrisma.product.create({
        data: {
          name: '测试产品1',
          price: 100,
          cost: 50,
          categoryId: 'test-category-1'
        }
      })

      const request = createMockRequest('GET', '/api/products')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(Array.isArray(data)).toBe(true)
      expect(data).toHaveLength(1)
      validateProductData(data[0])
    })

    it('应该支持分页查询', async () => {
      // 创建多个测试产品
      for (let i = 1; i <= 15; i++) {
        await testPrisma.product.create({
          data: {
            name: `测试产品${i}`,
            price: i * 10,
            cost: i * 5,
            categoryId: 'test-category-1'
          }
        })
      }

      const request = createMockRequest('GET', '/api/products?page=1&limit=10')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.products).toHaveLength(10)
      expect(data.pagination.total).toBe(15)
      expect(data.pagination.page).toBe(1)
      expect(data.pagination.totalPages).toBe(2)
    })

    it('应该支持搜索功能', async () => {
      await testPrisma.product.create({
        data: {
          name: '景泰蓝花瓶',
          price: 200,
          cost: 100,
          categoryId: 'test-category-1'
        }
      })

      await testPrisma.product.create({
        data: {
          name: '陶瓷茶具',
          price: 150,
          cost: 75,
          categoryId: 'test-category-1'
        }
      })

      const request = createMockRequest('GET', '/api/products?search=景泰蓝')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toHaveLength(1)
      expect(data[0].name).toBe('景泰蓝花瓶')
    })
  })

  describe('POST /api/products', () => {
    it('应该创建新产品', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const productData = {
        name: '新产品',
        price: 300,
        cost: 150,
        categoryId: 'test-category-1',
        description: '产品描述'
      }

      const request = createMockRequest('POST', '/api/products', productData)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      validateProductData(data)
      expect(data.name).toBe(productData.name)
      expect(data.price).toBe(productData.price)

      // 验证数据库中是否真的创建了产品
      const dbProduct = await testPrisma.product.findUnique({
        where: { id: data.id }
      })
      expect(dbProduct).toBeTruthy()
    })

    it('应该验证必填字段', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidData = {
        price: 300,
        cost: 150
        // 缺少 name 字段
      }

      const request = createMockRequest('POST', '/api/products', invalidData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('name')
    })

    it('应该检查权限', async () => {
      const request = createMockRequest('POST', '/api/products', {})
      const response = await POST(request)
      
      expect(response.status).toBe(401)
    })
  })

  describe('GET /api/products/[id]', () => {
    it('应该返回指定产品详情', async () => {
      const product = await testPrisma.product.create({
        data: {
          name: '特定产品',
          price: 400,
          cost: 200,
          categoryId: 'test-category-1'
        }
      })

      const request = createMockRequest('GET', `/api/products/${product.id}`)
      const response = await getById(request, { params: { id: product.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      validateProductData(data)
      expect(data.id).toBe(product.id)
      expect(data.name).toBe('特定产品')
    })

    it('应该处理不存在的产品', async () => {
      const request = createMockRequest('GET', '/api/products/non-existent-id')
      const response = await getById(request, { params: { id: 'non-existent-id' } })
      
      expect(response.status).toBe(404)
      const error = await response.json()
      expect(error.error).toBe('产品不存在')
    })
  })

  describe('PUT /api/products/[id]', () => {
    it('应该更新产品信息', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const product = await testPrisma.product.create({
        data: {
          name: '原始产品',
          price: 100,
          cost: 50,
          categoryId: 'test-category-1'
        }
      })

      const updateData = {
        name: '更新产品',
        price: 200,
        description: '更新描述'
      }

      const request = createMockRequest('PUT', `/api/products/${product.id}`, updateData)
      const response = await updateById(request, { params: { id: product.id } })
      
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.name).toBe('更新产品')
      expect(data.price).toBe(200)
      expect(data.description).toBe('更新描述')

      // 验证数据库更新
      const dbProduct = await testPrisma.product.findUnique({
        where: { id: product.id }
      })
      expect(dbProduct?.name).toBe('更新产品')
    })

    it('应该处理不存在的产品更新', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('PUT', '/api/products/non-existent', { name: '更新' })
      const response = await updateById(request, { params: { id: 'non-existent' } })
      
      expect(response.status).toBe(404)
    })
  })

  describe('DELETE /api/products/[id]', () => {
    it('应该删除产品', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const product = await testPrisma.product.create({
        data: {
          name: '待删除产品',
          price: 100,
          cost: 50,
          categoryId: 'test-category-1'
        }
      })

      const request = createMockRequest('DELETE', `/api/products/${product.id}`)
      const response = await deleteById(request, { params: { id: product.id } })
      
      expect(response.status).toBe(200)

      // 验证产品已被删除
      const dbProduct = await testPrisma.product.findUnique({
        where: { id: product.id }
      })
      expect(dbProduct).toBeNull()
    })

    it('应该处理删除不存在的产品', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const request = createMockRequest('DELETE', '/api/products/non-existent')
      const response = await deleteById(request, { params: { id: 'non-existent' } })
      
      expect(response.status).toBe(404)
    })

    it('应该检查删除权限', async () => {
      const product = await testPrisma.product.create({
        data: {
          name: '受保护产品',
          price: 100,
          cost: 50,
          categoryId: 'test-category-1'
        }
      })

      const request = createMockRequest('DELETE', `/api/products/${product.id}`)
      const response = await deleteById(request, { params: { id: product.id } })
      
      expect(response.status).toBe(401)
    })
  })

  describe('产品业务逻辑测试', () => {
    it('应该验证价格逻辑', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const invalidPriceData = {
        name: '价格测试产品',
        price: -100, // 负价格
        cost: 50,
        categoryId: 'test-category-1'
      }

      const request = createMockRequest('POST', '/api/products', invalidPriceData)
      const response = await POST(request)
      
      expect(response.status).toBe(400)
      const error = await response.json()
      expect(error.error).toContain('价格')
    })

    it('应该处理库存相关逻辑', async () => {
      const { getServerSession } = await import('next-auth/next')
      vi.mocked(getServerSession).mockResolvedValue({
        user: testUsers.admin
      })

      const productWithStock = {
        name: '库存产品',
        price: 200,
        cost: 100,
        categoryId: 'test-category-1',
        stockQuantity: 50,
        minStockLevel: 10
      }

      const request = createMockRequest('POST', '/api/products', productWithStock)
      const response = await POST(request)
      
      expect(response.status).toBe(201)
      const data = await response.json()
      expect(data.stockQuantity).toBe(50)
      expect(data.minStockLevel).toBe(10)
    })
  })
})