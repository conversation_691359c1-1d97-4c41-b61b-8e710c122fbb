import { PrismaClient, Prisma, InventoryTransactionType } from '@prisma/client'
import { z } from 'zod'

// 库存业务逻辑核心类
export class InventoryBusiness {
  constructor(private prisma: PrismaClient) {}

  // 库存预警检查
  async checkLowStockAlerts(warehouseId?: string): Promise<{
    lowStockItems: Array<{
      id: string
      productId: string
      productName: string
      currentQuantity: number
      minStockLevel: number
      warehouseName: string
      shortage: number
      urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
    }>
    totalAlerts: number
  }> {
    const whereClause: any = {
      quantity: {
        gt: 0 // 只检查有库存的商品
      }
    }

    if (warehouseId) {
      whereClause.warehouseId = warehouseId
    }

    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            name: true,
            minStockLevel: true
          }
        },
        warehouse: {
          select: {
            name: true
          }
        }
      }
    })

    const lowStockItems = inventoryItems
      .filter(item => {
        const minLevel = item.product.minStockLevel || 10
        return item.quantity <= minLevel
      })
      .map(item => {
        const minLevel = item.product.minStockLevel || 10
        const shortage = minLevel - item.quantity
        
        // 计算紧急程度
        let urgencyLevel: 'low' | 'medium' | 'high' | 'critical'
        const stockRatio = item.quantity / minLevel
        
        if (stockRatio <= 0.1) urgencyLevel = 'critical'  // 10%以下
        else if (stockRatio <= 0.3) urgencyLevel = 'high' // 30%以下
        else if (stockRatio <= 0.6) urgencyLevel = 'medium' // 60%以下
        else urgencyLevel = 'low'

        return {
          id: item.id,
          productId: item.productId,
          productName: item.product.name,
          currentQuantity: item.quantity,
          minStockLevel: minLevel,
          warehouseName: item.warehouse.name,
          shortage,
          urgencyLevel
        }
      })
      .sort((a, b) => {
        // 按紧急程度排序
        const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 }
        return urgencyOrder[b.urgencyLevel] - urgencyOrder[a.urgencyLevel]
      })

    return {
      lowStockItems,
      totalAlerts: lowStockItems.length
    }
  }

  // 库存调拨
  async transferInventory(data: {
    productId: string
    fromWarehouseId: string
    toWarehouseId: string
    quantity: number
    reason?: string
    operatorId: string
  }): Promise<{
    success: boolean
    transferId?: string
    error?: string
  }> {
    if (data.fromWarehouseId === data.toWarehouseId) {
      return { success: false, error: '源仓库和目标仓库不能相同' }
    }

    if (data.quantity <= 0) {
      return { success: false, error: '调拨数量必须大于0' }
    }

    return await this.prisma.$transaction(async (tx) => {
      try {
        // 检查源仓库库存
        const sourceInventory = await tx.inventoryItem.findFirst({
          where: {
            productId: data.productId,
            warehouseId: data.fromWarehouseId
          }
        })

        if (!sourceInventory || sourceInventory.quantity < data.quantity) {
          return { 
            success: false, 
            error: `源仓库库存不足，当前库存：${sourceInventory?.quantity || 0}，需要：${data.quantity}` 
          }
        }

        // 减少源仓库库存
        await tx.inventoryItem.update({
          where: { id: sourceInventory.id },
          data: { 
            quantity: { decrement: data.quantity },
            updatedAt: new Date()
          }
        })

        // 增加目标仓库库存
        const targetInventory = await tx.inventoryItem.findFirst({
          where: {
            productId: data.productId,
            warehouseId: data.toWarehouseId
          }
        })

        if (targetInventory) {
          await tx.inventoryItem.update({
            where: { id: targetInventory.id },
            data: { 
              quantity: { increment: data.quantity },
              updatedAt: new Date()
            }
          })
        } else {
          await tx.inventoryItem.create({
            data: {
              productId: data.productId,
              warehouseId: data.toWarehouseId,
              quantity: data.quantity,
              reservedQuantity: 0
            }
          })
        }

        // 创建调拨记录
        const transfer = await tx.inventoryTransfer.create({
          data: {
            productId: data.productId,
            fromWarehouseId: data.fromWarehouseId,
            toWarehouseId: data.toWarehouseId,
            quantity: data.quantity,
            reason: data.reason || '库存调拨',
            operatorId: data.operatorId,
            status: 'COMPLETED'
          }
        })

        // 记录库存事务
        await tx.inventoryTransaction.createMany({
          data: [
            {
              type: InventoryTransactionType.TRANSFER_OUT,
              productId: data.productId,
              warehouseId: data.fromWarehouseId,
              quantity: -data.quantity,
              reason: `调拨至仓库ID: ${data.toWarehouseId}`,
              operatorId: data.operatorId,
              referenceId: transfer.id,
              referenceType: 'TRANSFER'
            },
            {
              type: InventoryTransactionType.TRANSFER_IN,
              productId: data.productId,
              warehouseId: data.toWarehouseId,
              quantity: data.quantity,
              reason: `从仓库ID: ${data.fromWarehouseId} 调入`,
              operatorId: data.operatorId,
              referenceId: transfer.id,
              referenceType: 'TRANSFER'
            }
          ]
        })

        return { success: true, transferId: transfer.id }
      } catch (error) {
        console.error('库存调拨失败:', error)
        return { success: false, error: '库存调拨过程中发生错误' }
      }
    })
  }

  // 库存盘点
  async performStockTaking(data: {
    warehouseId: string
    items: Array<{
      productId: string
      countedQuantity: number
      notes?: string
    }>
    operatorId: string
    reason?: string
  }): Promise<{
    success: boolean
    stockTakeId?: string
    adjustments: Array<{
      productId: string
      productName: string
      originalQuantity: number
      countedQuantity: number
      difference: number
      adjustmentType: 'increase' | 'decrease' | 'none'
    }>
    error?: string
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 创建盘点记录
        const stockTake = await tx.stockTake.create({
          data: {
            warehouseId: data.warehouseId,
            operatorId: data.operatorId,
            reason: data.reason || '定期盘点',
            status: 'COMPLETED'
          }
        })

        const adjustments = []

        for (const item of data.items) {
          // 获取当前库存
          const currentInventory = await tx.inventoryItem.findFirst({
            where: {
              productId: item.productId,
              warehouseId: data.warehouseId
            },
            include: {
              product: {
                select: { name: true }
              }
            }
          })

          const originalQuantity = currentInventory?.quantity || 0
          const difference = item.countedQuantity - originalQuantity

          let adjustmentType: 'increase' | 'decrease' | 'none'
          if (difference > 0) adjustmentType = 'increase'
          else if (difference < 0) adjustmentType = 'decrease'
          else adjustmentType = 'none'

          adjustments.push({
            productId: item.productId,
            productName: currentInventory?.product.name || 'Unknown',
            originalQuantity,
            countedQuantity: item.countedQuantity,
            difference,
            adjustmentType
          })

          // 创建盘点详情记录
          await tx.stockTakeItem.create({
            data: {
              stockTakeId: stockTake.id,
              productId: item.productId,
              originalQuantity,
              countedQuantity: item.countedQuantity,
              difference,
              notes: item.notes
            }
          })

          // 如果有差异，调整库存
          if (difference !== 0) {
            if (currentInventory) {
              await tx.inventoryItem.update({
                where: { id: currentInventory.id },
                data: { 
                  quantity: item.countedQuantity,
                  updatedAt: new Date()
                }
              })
            } else {
              // 如果没有库存记录，创建新的
              await tx.inventoryItem.create({
                data: {
                  productId: item.productId,
                  warehouseId: data.warehouseId,
                  quantity: item.countedQuantity,
                  reservedQuantity: 0
                }
              })
            }

            // 记录库存调整事务
            await tx.inventoryTransaction.create({
              data: {
                type: difference > 0 ? InventoryTransactionType.ADJUSTMENT_IN : InventoryTransactionType.ADJUSTMENT_OUT,
                productId: item.productId,
                warehouseId: data.warehouseId,
                quantity: difference,
                reason: `盘点调整: ${item.notes || '盘点发现差异'}`,
                operatorId: data.operatorId,
                referenceId: stockTake.id,
                referenceType: 'STOCK_TAKE'
              }
            })
          }
        }

        return {
          success: true,
          stockTakeId: stockTake.id,
          adjustments
        }
      } catch (error) {
        console.error('库存盘点失败:', error)
        return {
          success: false,
          adjustments: [],
          error: '库存盘点过程中发生错误'
        }
      }
    })
  }

  // 自动补货建议
  async generateRestockSuggestions(warehouseId?: string): Promise<{
    suggestions: Array<{
      productId: string
      productName: string
      currentQuantity: number
      minStockLevel: number
      maxStockLevel: number
      suggestedOrderQuantity: number
      estimatedCost: number
      supplier?: string
      priority: 'low' | 'medium' | 'high' | 'critical'
    }>
    totalSuggestions: number
    estimatedTotalCost: number
  }> {
    const whereClause: any = {}
    if (warehouseId) {
      whereClause.warehouseId = warehouseId
    }

    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            name: true,
            cost: true,
            minStockLevel: true,
            maxStockLevel: true,
            supplierName: true
          }
        }
      }
    })

    const suggestions = inventoryItems
      .filter(item => {
        const minLevel = item.product.minStockLevel || 10
        return item.quantity <= minLevel * 1.5 // 低于最小库存的1.5倍时建议补货
      })
      .map(item => {
        const minLevel = item.product.minStockLevel || 10
        const maxLevel = item.product.maxStockLevel || minLevel * 3
        const suggestedOrderQuantity = maxLevel - item.quantity
        const estimatedCost = suggestedOrderQuantity * (item.product.cost || 0)

        // 计算优先级
        let priority: 'low' | 'medium' | 'high' | 'critical'
        const stockRatio = item.quantity / minLevel
        
        if (stockRatio <= 0.1) priority = 'critical'
        else if (stockRatio <= 0.5) priority = 'high'
        else if (stockRatio <= 1.0) priority = 'medium'
        else priority = 'low'

        return {
          productId: item.productId,
          productName: item.product.name,
          currentQuantity: item.quantity,
          minStockLevel: minLevel,
          maxStockLevel: maxLevel,
          suggestedOrderQuantity,
          estimatedCost,
          supplier: item.product.supplierName || undefined,
          priority
        }
      })
      .sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      })

    const estimatedTotalCost = suggestions.reduce((sum, item) => sum + item.estimatedCost, 0)

    return {
      suggestions,
      totalSuggestions: suggestions.length,
      estimatedTotalCost
    }
  }

  // 库存预留（用于订单）
  async reserveInventory(data: {
    items: Array<{
      productId: string
      quantity: number
      warehouseId: string
    }>
    reservationReference: string
    reservationType: string
    operatorId: string
  }): Promise<{
    success: boolean
    reservationId?: string
    failedItems?: Array<{
      productId: string
      requestedQuantity: number
      availableQuantity: number
      reason: string
    }>
    error?: string
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        const failedItems = []

        // 检查所有商品是否有足够库存
        for (const item of data.items) {
          const inventory = await tx.inventoryItem.findFirst({
            where: {
              productId: item.productId,
              warehouseId: item.warehouseId
            }
          })

          const availableQuantity = inventory ? inventory.quantity - inventory.reservedQuantity : 0
          
          if (availableQuantity < item.quantity) {
            failedItems.push({
              productId: item.productId,
              requestedQuantity: item.quantity,
              availableQuantity,
              reason: '可用库存不足'
            })
          }
        }

        if (failedItems.length > 0) {
          return {
            success: false,
            failedItems,
            error: '部分商品库存不足，无法完成预留'
          }
        }

        // 创建预留记录
        const reservation = await tx.inventoryReservation.create({
          data: {
            reference: data.reservationReference,
            type: data.reservationType,
            operatorId: data.operatorId,
            status: 'ACTIVE'
          }
        })

        // 执行库存预留
        for (const item of data.items) {
          await tx.inventoryItem.updateMany({
            where: {
              productId: item.productId,
              warehouseId: item.warehouseId
            },
            data: {
              reservedQuantity: { increment: item.quantity }
            }
          })

          // 创建预留明细
          await tx.inventoryReservationItem.create({
            data: {
              reservationId: reservation.id,
              productId: item.productId,
              warehouseId: item.warehouseId,
              quantity: item.quantity
            }
          })

          // 记录库存事务
          await tx.inventoryTransaction.create({
            data: {
              type: InventoryTransactionType.RESERVE,
              productId: item.productId,
              warehouseId: item.warehouseId,
              quantity: -item.quantity, // 负数表示减少可用库存
              reason: `库存预留: ${data.reservationReference}`,
              operatorId: data.operatorId,
              referenceId: reservation.id,
              referenceType: 'RESERVATION'
            }
          })
        }

        return {
          success: true,
          reservationId: reservation.id
        }
      } catch (error) {
        console.error('库存预留失败:', error)
        return {
          success: false,
          error: '库存预留过程中发生错误'
        }
      }
    })
  }

  // 释放库存预留
  async releaseReservation(reservationId: string, operatorId: string): Promise<{
    success: boolean
    error?: string
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        const reservation = await tx.inventoryReservation.findUnique({
          where: { id: reservationId },
          include: {
            items: true
          }
        })

        if (!reservation) {
          return { success: false, error: '预留记录不存在' }
        }

        if (reservation.status !== 'ACTIVE') {
          return { success: false, error: '预留已被处理' }
        }

        // 释放所有预留的库存
        for (const item of reservation.items) {
          await tx.inventoryItem.updateMany({
            where: {
              productId: item.productId,
              warehouseId: item.warehouseId
            },
            data: {
              reservedQuantity: { decrement: item.quantity }
            }
          })

          // 记录库存事务
          await tx.inventoryTransaction.create({
            data: {
              type: InventoryTransactionType.RELEASE,
              productId: item.productId,
              warehouseId: item.warehouseId,
              quantity: item.quantity, // 正数表示增加可用库存
              reason: `释放预留: ${reservation.reference}`,
              operatorId: operatorId,
              referenceId: reservation.id,
              referenceType: 'RESERVATION'
            }
          })
        }

        // 更新预留状态
        await tx.inventoryReservation.update({
          where: { id: reservationId },
          data: { 
            status: 'RELEASED',
            updatedAt: new Date()
          }
        })

        return { success: true }
      } catch (error) {
        console.error('释放库存预留失败:', error)
        return { success: false, error: '释放库存预留过程中发生错误' }
      }
    })
  }

  // 获取库存报告
  async getInventoryReport(warehouseId?: string, categoryId?: string): Promise<{
    totalProducts: number
    totalValue: number
    lowStockCount: number
    outOfStockCount: number
    categories: Array<{
      categoryName: string
      productCount: number
      totalValue: number
      averageValue: number
    }>
    topValueProducts: Array<{
      productName: string
      quantity: number
      unitCost: number
      totalValue: number
    }>
  }> {
    const whereClause: any = {}
    if (warehouseId) whereClause.warehouseId = warehouseId

    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            name: true,
            cost: true,
            minStockLevel: true,
            categoryId: true,
            category: {
              select: { name: true }
            }
          }
        }
      }
    })

    const totalProducts = inventoryItems.length
    const totalValue = inventoryItems.reduce((sum, item) => 
      sum + (item.quantity * (item.product.cost || 0)), 0
    )

    const lowStockCount = inventoryItems.filter(item => 
      item.quantity <= (item.product.minStockLevel || 10)
    ).length

    const outOfStockCount = inventoryItems.filter(item => 
      item.quantity === 0
    ).length

    // 按分类统计
    const categoryStats = new Map()
    inventoryItems.forEach(item => {
      const categoryName = item.product.category?.name || '未分类'
      const value = item.quantity * (item.product.cost || 0)
      
      if (!categoryStats.has(categoryName)) {
        categoryStats.set(categoryName, {
          categoryName,
          productCount: 0,
          totalValue: 0
        })
      }
      
      const stats = categoryStats.get(categoryName)
      stats.productCount++
      stats.totalValue += value
    })

    const categories = Array.from(categoryStats.values()).map(category => ({
      ...category,
      averageValue: category.productCount > 0 ? category.totalValue / category.productCount : 0
    }))

    // 价值最高的产品
    const topValueProducts = inventoryItems
      .map(item => ({
        productName: item.product.name,
        quantity: item.quantity,
        unitCost: item.product.cost || 0,
        totalValue: item.quantity * (item.product.cost || 0)
      }))
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 10)

    return {
      totalProducts,
      totalValue,
      lowStockCount,
      outOfStockCount,
      categories,
      topValueProducts
    }
  }
}

// 库存业务逻辑验证规则
export const inventoryValidationSchemas = {
  transfer: z.object({
    productId: z.string().min(1, '产品ID不能为空'),
    fromWarehouseId: z.string().min(1, '源仓库ID不能为空'),
    toWarehouseId: z.string().min(1, '目标仓库ID不能为空'),
    quantity: z.number().min(1, '调拨数量必须大于0'),
    reason: z.string().optional(),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  stockTake: z.object({
    warehouseId: z.string().min(1, '仓库ID不能为空'),
    items: z.array(z.object({
      productId: z.string().min(1, '产品ID不能为空'),
      countedQuantity: z.number().min(0, '盘点数量不能为负数'),
      notes: z.string().optional()
    })).min(1, '盘点商品不能为空'),
    operatorId: z.string().min(1, '操作员ID不能为空'),
    reason: z.string().optional()
  }),

  reservation: z.object({
    items: z.array(z.object({
      productId: z.string().min(1, '产品ID不能为空'),
      quantity: z.number().min(1, '预留数量必须大于0'),
      warehouseId: z.string().min(1, '仓库ID不能为空')
    })).min(1, '预留商品不能为空'),
    reservationReference: z.string().min(1, '预留参考号不能为空'),
    reservationType: z.string().min(1, '预留类型不能为空'),
    operatorId: z.string().min(1, '操作员ID不能为空')
  })
}