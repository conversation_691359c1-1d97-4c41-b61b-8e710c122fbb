# 🚀 ERP系统部署运维指南

*更新时间: 2025-06-24*

## 📋 **部署概览**

本指南提供了完整的ERP系统生产环境部署和运维配置，包括Docker容器化、数据库设置、负载均衡、监控和备份策略。

## 🏗️ **系统架构**

### **生产环境架构图**
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
              ┌───────────────┼───────────────┐
              │               │               │
    ┌─────────▼─────────┐ ┌───▼────────┐ ┌───▼────────┐
    │   App Instance 1  │ │ Instance 2 │ │ Instance 3 │
    │   (Node.js)       │ │ (Node.js)  │ │ (Node.js)  │
    └─────────┬─────────┘ └─────┬──────┘ └─────┬──────┘
              │                 │              │
              └─────────────────┼──────────────┘
                                │
                    ┌───────────▼───────────┐
                    │     Database         │
                    │   (PostgreSQL)       │
                    │   + Read Replicas    │
                    └──────────────────────┘
```

### **核心组件**
- **前端**: Next.js 14 (SSR/SSG)
- **后端**: Node.js + TypeScript
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **负载均衡**: Nginx
- **容器**: Docker + Docker Compose
- **监控**: 内置监控系统 + 外部APM
- **日志**: 集中化日志管理

## 🐳 **Docker容器化部署**

### **1. 生产环境Dockerfile优化**

```dockerfile
# 多阶段构建，优化镜像大小
FROM node:18-alpine AS builder

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY prisma ./prisma/

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build
RUN npx prisma generate

# 生产镜像
FROM node:18-alpine AS runner

# 安全用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

WORKDIR /app

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules ./node_modules

# 设置权限
USER nextjs

EXPOSE 3000

ENV NODE_ENV production
ENV PORT 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

### **2. Docker Compose生产配置**

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://erp_user:${DB_PASSWORD}@db:5432/erp_prod
      - REDIS_URL=redis://redis:6379
      - CSRF_SECRET=${CSRF_SECRET}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # 数据库服务
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=erp_prod
      - POSTGRES_USER=erp_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db-init:/docker-entrypoint-initdb.d
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped

  # 数据库备份服务
  backup:
    image: postgres:15-alpine
    environment:
      - PGPASSWORD=${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"
    depends_on:
      - db
    networks:
      - app-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### **3. Nginx配置**

```nginx
# nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss;

    # 上游服务器
    upstream app_backend {
        least_conn;
        server app:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

    server {
        listen 80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com www.your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头部
        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header Referrer-Policy strict-origin-when-cross-origin always;

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API限流
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 登录限流
        location /api/auth/ {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 主要代理
        location / {
            proxy_pass http://app_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 健康检查
        location /health {
            access_log off;
            proxy_pass http://app_backend/api/health;
        }
    }
}
```

## 💾 **数据库配置**

### **1. PostgreSQL生产优化**

```sql
-- postgresql.conf 优化配置
-- 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- 连接配置
max_connections = 100
shared_preload_libraries = 'pg_stat_statements'

-- 日志配置
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000

-- 性能配置
random_page_cost = 1.1
seq_page_cost = 1.0
default_statistics_target = 100

-- 检查点配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
```

### **2. 数据库初始化脚本**

```bash
#!/bin/bash
# scripts/db-init/01-init.sh

set -e

# 创建扩展
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
EOSQL

# 创建索引
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- 产品表索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_category ON "Product"("categoryId");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_sku ON "Product"("sku");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_name ON "Product"("name");
    
    -- 订单表索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_customer ON "Order"("customerId");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status ON "Order"("status");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_date ON "Order"("createdAt");
    
    -- 库存表索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_product ON "InventoryItem"("productId");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_warehouse ON "InventoryItem"("warehouseId");
    
    -- 员工表索引
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_number ON "Employee"("employeeNumber");
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_department ON "Employee"("department");
EOSQL

echo "数据库初始化完成"
```

### **3. 备份脚本**

```bash
#!/bin/bash
# scripts/backup.sh

set -e

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="erp_prod"
DB_USER="erp_user"
DB_HOST="db"

# 创建备份目录
mkdir -p $BACKUP_DIR/daily
mkdir -p $BACKUP_DIR/weekly
mkdir -p $BACKUP_DIR/monthly

# 执行备份
echo "开始数据库备份: $DATE"

# 全量备份
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME \
    --format=custom \
    --compress=9 \
    --verbose \
    > $BACKUP_DIR/daily/backup_$DATE.dump

# 检查备份完整性
pg_restore --list $BACKUP_DIR/daily/backup_$DATE.dump > /dev/null

if [ $? -eq 0 ]; then
    echo "备份创建成功: backup_$DATE.dump"
    
    # 压缩备份
    gzip $BACKUP_DIR/daily/backup_$DATE.dump
    
    # 清理旧备份 (保留7天)
    find $BACKUP_DIR/daily -name "*.gz" -mtime +7 -delete
    
    # 每周备份 (周日)
    if [ $(date +%u) -eq 7 ]; then
        cp $BACKUP_DIR/daily/backup_$DATE.dump.gz $BACKUP_DIR/weekly/
        find $BACKUP_DIR/weekly -name "*.gz" -mtime +28 -delete
    fi
    
    # 每月备份 (1号)
    if [ $(date +%d) -eq 01 ]; then
        cp $BACKUP_DIR/daily/backup_$DATE.dump.gz $BACKUP_DIR/monthly/
        find $BACKUP_DIR/monthly -name "*.gz" -mtime +365 -delete
    fi
    
    echo "备份流程完成"
else
    echo "备份验证失败"
    exit 1
fi
```

## 🔧 **环境配置**

### **1. 生产环境变量**

```bash
# .env.production
NODE_ENV=production
PORT=3000

# 数据库
DATABASE_URL=postgresql://erp_user:${DB_PASSWORD}@db:5432/erp_prod
DATABASE_CONNECTION_POOL_SIZE=20

# Redis
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# 认证
NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
NEXTAUTH_URL=https://your-domain.com
ADMIN_CSRF_SECRET=${ADMIN_CSRF_SECRET}

# 安全
CSRF_SECRET=${CSRF_SECRET}
TRUSTED_IPS=10.0.0.0/8,**********/12,***********/16

# 监控
MONITORING_ENABLED=true
LOG_LEVEL=info
SENTRY_DSN=${SENTRY_DSN}

# 文件上传
UPLOAD_MAX_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# 邮件配置
SMTP_HOST=${SMTP_HOST}
SMTP_PORT=${SMTP_PORT}
SMTP_USER=${SMTP_USER}
SMTP_PASS=${SMTP_PASS}

# 备份配置
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=90
```

### **2. 生产环境密钥管理**

```bash
#!/bin/bash
# scripts/generate-secrets.sh

echo "生成生产环境密钥..."

# 生成随机密钥
export DB_PASSWORD=$(openssl rand -base64 32)
export REDIS_PASSWORD=$(openssl rand -base64 32)
export NEXTAUTH_SECRET=$(openssl rand -base64 64)
export CSRF_SECRET=$(openssl rand -base64 32)
export ADMIN_CSRF_SECRET=$(openssl rand -base64 32)

# 创建密钥文件
cat > .env.secrets <<EOF
DB_PASSWORD=$DB_PASSWORD
REDIS_PASSWORD=$REDIS_PASSWORD
NEXTAUTH_SECRET=$NEXTAUTH_SECRET
CSRF_SECRET=$CSRF_SECRET
ADMIN_CSRF_SECRET=$ADMIN_CSRF_SECRET
EOF

echo "密钥已生成并保存到 .env.secrets"
echo "请妥善保管此文件，不要提交到版本控制"
```

## 📊 **监控配置**

### **1. 健康检查配置**

```typescript
// lib/health-check.ts
import { MonitoringManager } from './monitoring'

export async function setupHealthChecks(monitoring: MonitoringManager) {
  // 数据库健康检查
  const dbCheck = async () => {
    try {
      await prisma.$queryRaw`SELECT 1`
      return { status: 'healthy', latency: Date.now() }
    } catch (error) {
      return { status: 'unhealthy', error: error.message }
    }
  }

  // Redis健康检查
  const redisCheck = async () => {
    try {
      await redis.ping()
      return { status: 'healthy' }
    } catch (error) {
      return { status: 'unhealthy', error: error.message }
    }
  }

  // 磁盘空间检查
  const diskCheck = async () => {
    const usage = await getDiskUsage()
    return {
      status: usage.percentage < 90 ? 'healthy' : 'warning',
      usage: usage.percentage,
      free: usage.free
    }
  }

  return {
    database: dbCheck,
    redis: redisCheck,
    disk: diskCheck
  }
}
```

### **2. 告警配置**

```yaml
# alerts.yml
alerts:
  - name: high_memory_usage
    condition: memory_usage > 90
    severity: critical
    actions:
      - email: <EMAIL>
      - webhook: https://hooks.slack.com/services/xxx

  - name: database_connection_timeout
    condition: db_connection_time > 5000
    severity: high
    actions:
      - email: <EMAIL>

  - name: high_error_rate
    condition: error_rate > 5
    severity: medium
    actions:
      - email: <EMAIL>
```

## 🚀 **部署流程**

### **1. 自动部署脚本**

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "开始部署ERP系统..."

# 检查环境
if [ ! -f ".env.production" ]; then
    echo "错误: .env.production 文件不存在"
    exit 1
fi

if [ ! -f ".env.secrets" ]; then
    echo "错误: .env.secrets 文件不存在"
    exit 1
fi

# 构建镜像
echo "构建Docker镜像..."
docker-compose -f docker-compose.prod.yml build

# 数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.prod.yml run --rm app npx prisma migrate deploy

# 启动服务
echo "启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 健康检查
echo "执行健康检查..."
for i in {1..10}; do
    if curl -f http://localhost/health; then
        echo "服务启动成功!"
        break
    fi
    
    if [ $i -eq 10 ]; then
        echo "健康检查失败，服务可能未正常启动"
        docker-compose -f docker-compose.prod.yml logs
        exit 1
    fi
    
    sleep 10
done

echo "部署完成!"
```

### **2. 滚动更新脚本**

```bash
#!/bin/bash
# scripts/rolling-update.sh

set -e

echo "开始滚动更新..."

# 获取当前运行的容器
CONTAINERS=$(docker-compose -f docker-compose.prod.yml ps -q app)

# 逐个更新容器
for container in $CONTAINERS; do
    echo "更新容器: $container"
    
    # 停止一个实例
    docker stop $container
    
    # 启动新实例
    docker-compose -f docker-compose.prod.yml up -d --scale app=3
    
    # 等待新实例健康
    sleep 30
    
    # 健康检查
    if ! curl -f http://localhost/health; then
        echo "健康检查失败，回滚更新"
        docker start $container
        exit 1
    fi
    
    # 移除旧容器
    docker rm $container
    
    echo "容器更新成功"
done

echo "滚动更新完成!"
```

## 📈 **性能优化**

### **1. 缓存策略**

```typescript
// lib/cache-strategy.ts
export const cacheConfig = {
  // 静态数据缓存 (1小时)
  static: {
    ttl: 3600,
    keys: ['products', 'categories', 'suppliers']
  },
  
  // 用户会话缓存 (30分钟)
  session: {
    ttl: 1800,
    keys: ['user:*', 'session:*']
  },
  
  // API响应缓存 (5分钟)
  api: {
    ttl: 300,
    keys: ['api:*']
  }
}
```

### **2. 数据库连接池优化**

```typescript
// lib/db-pool.ts
export const dbConfig = {
  pool: {
    min: 5,
    max: 20,
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 600000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200
  }
}
```

## 🔒 **安全配置**

### **1. SSL/TLS配置**

```bash
# 生成SSL证书 (Let's Encrypt)
#!/bin/bash
# scripts/setup-ssl.sh

certbot certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email <EMAIL> \
    --agree-tos \
    --no-eff-email \
    -d your-domain.com \
    -d www.your-domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### **2. 防火墙配置**

```bash
#!/bin/bash
# scripts/setup-firewall.sh

# 基本防火墙规则
ufw default deny incoming
ufw default allow outgoing

# 允许SSH
ufw allow ssh

# 允许HTTP/HTTPS
ufw allow 80
ufw allow 443

# 允许内部网络
ufw allow from 10.0.0.0/8
ufw allow from **********/12
ufw allow from ***********/16

# 启用防火墙
ufw --force enable
```

## 📋 **运维检查清单**

### **日常运维检查**
- [ ] 检查服务健康状态
- [ ] 查看系统资源使用情况
- [ ] 检查错误日志
- [ ] 验证备份完整性
- [ ] 监控安全告警

### **周度运维检查**
- [ ] 数据库性能分析
- [ ] 清理旧日志文件
- [ ] 更新安全补丁
- [ ] 容量规划评估
- [ ] 灾难恢复演练

### **月度运维检查**
- [ ] 全面性能报告
- [ ] 安全审计
- [ ] 备份策略优化
- [ ] 系统容量扩展评估
- [ ] 依赖组件更新

## 🆘 **故障排除**

### **常见问题解决**

1. **服务无法启动**
   ```bash
   # 检查日志
   docker-compose logs -f app
   
   # 检查端口占用
   netstat -tlnp | grep 3000
   ```

2. **数据库连接问题**
   ```bash
   # 检查数据库状态
   docker-compose exec db psql -U erp_user -d erp_prod -c "SELECT 1"
   
   # 检查连接池
   docker-compose exec app npm run db:pool:status
   ```

3. **内存泄漏排查**
   ```bash
   # 生成内存快照
   docker-compose exec app node --inspect=0.0.0.0:9229 dist/server.js
   
   # 分析内存使用
   docker stats --no-stream
   ```

## 📞 **技术支持**

### **联系信息**
- 技术支持: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx
- 文档地址: https://docs.company.com/erp

### **故障上报流程**
1. 收集错误信息和日志
2. 描述重现步骤
3. 记录影响范围
4. 通过工单系统提交
5. 跟踪处理进度

---

**注意事项**:
- 所有生产环境操作都需要经过审批
- 重要变更前必须进行备份
- 密钥文件不得提交到版本控制
- 定期更新安全补丁和依赖
- 遵循最小权限原则配置访问控制