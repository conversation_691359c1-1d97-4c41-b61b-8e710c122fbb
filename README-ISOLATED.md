# 灵华珐琅馆独立Docker环境

## 概述

这是一个完全独立的Docker环境配置，专为避免与其他ERP测试项目的数据库冲突而设计。该环境使用独立的端口、容器名称、网络和数据卷，确保与现有系统完全隔离。

## 🏗️ 架构设计

### 服务组件
- **PostgreSQL 15**: 独立数据库服务 (端口: 5434)
- **Redis 7**: 独立缓存服务 (端口: 6381)
- **Next.js App**: 应用服务 (端口: 3002)
- **Adminer**: 数据库管理工具 (端口: 8081)

### 隔离特性
- **独立网络**: `linghua-isolated-network`
- **独立容器**: 所有容器使用 `linghua-isolated-` 前缀
- **独立数据卷**: 使用 `isolated_` 前缀命名
- **独立端口**: 避免与现有服务冲突

## 🚀 快速开始

### 1. 启动独立环境

```bash
# 启动完整的独立环境
./scripts/start-isolated.sh
```

启动脚本会自动执行以下操作：
- ✅ 检查Docker服务状态
- ✅ 检查端口占用情况
- ✅ 清理可能存在的旧容器
- ✅ 构建应用镜像
- ✅ 启动所有服务
- ✅ 等待服务就绪
- ✅ 初始化数据库

### 2. 访问服务

启动完成后，您可以访问以下服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| 应用系统 | http://localhost:3002 | 主要的ERP系统界面 |
| 数据库管理 | http://localhost:8081 | Adminer数据库管理工具 |
| PostgreSQL | localhost:5434 | 数据库直连 |
| Redis | localhost:6381 | 缓存服务 |

### 3. 数据库连接信息

```
服务器: localhost
端口: 5434
数据库: linghua_enamel_gallery_isolated
用户名: postgres
密码: isolated_password_2024
```

## 📊 数据迁移

### 从现有数据库迁移数据

如果您需要将现有数据库的数据迁移到独立环境：

```bash
# 执行数据迁移
./scripts/migrate-to-isolated.sh
```

迁移脚本功能：
- 🔍 检查源数据库和目标数据库连接
- 📤 导出源数据库数据
- 💾 备份目标数据库（可选）
- 🗑️ 清空目标数据库（可选）
- 📥 导入数据到目标数据库
- ✅ 验证迁移结果

## 🛑 停止和清理

### 停止独立环境

```bash
# 停止所有服务
./scripts/stop-isolated.sh
```

停止脚本选项：
- 🛑 停止所有容器
- 🗑️ 删除容器和网络
- 💾 可选择是否删除数据卷
- 🧹 可选择清理Docker资源

### 数据持久化

默认情况下，停止脚本会保留数据卷，这意味着：
- ✅ 数据库数据会被保留
- ✅ Redis数据会被保留
- ✅ 应用缓存会被保留

如果您需要完全清理（包括数据），在运行停止脚本时选择删除数据卷。

## 🔧 管理命令

### Docker Compose 命令

```bash
# 查看服务状态
docker-compose -f docker-compose.isolated.yml ps

# 查看服务日志
docker-compose -f docker-compose.isolated.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.isolated.yml logs -f linghua-isolated-app

# 重启特定服务
docker-compose -f docker-compose.isolated.yml restart linghua-isolated-app

# 进入容器
docker exec -it linghua-isolated-postgres psql -U postgres -d linghua_enamel_gallery_isolated
docker exec -it linghua-isolated-redis redis-cli
docker exec -it linghua-isolated-app bash
```

### 数据库操作

```bash
# 连接数据库
psql "postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated"

# 备份数据库
pg_dump "postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated" > backup.sql

# 恢复数据库
psql "postgresql://postgres:isolated_password_2024@localhost:5434/linghua_enamel_gallery_isolated" < backup.sql
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :5434
lsof -i :6381
lsof -i :3001
lsof -i :8081

# 解决方案：停止占用进程或修改配置文件中的端口
```

#### 2. 容器启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.isolated.yml logs

# 检查容器状态
docker ps -a | grep linghua-isolated
```

#### 3. 数据库连接失败
```bash
# 检查数据库容器状态
docker exec linghua-isolated-postgres pg_isready -U postgres

# 检查网络连接
docker network inspect linghua-isolated-network
```

#### 4. 应用无法访问
```bash
# 检查应用健康状态
curl http://localhost:3002/api/health

# 查看应用日志
docker logs linghua-isolated-app
```

### 重置环境

如果遇到无法解决的问题，可以完全重置环境：

```bash
# 1. 停止并清理所有资源
./scripts/stop-isolated.sh
# 选择删除数据卷和清理Docker资源

# 2. 重新启动
./scripts/start-isolated.sh
```

## 📁 文件结构

```
.
├── docker-compose.isolated.yml    # 独立环境Docker配置
├── .env.isolated                  # 独立环境变量
├── README-ISOLATED.md             # 本文档
└── scripts/
    ├── start-isolated.sh          # 启动脚本
    ├── stop-isolated.sh           # 停止脚本
    └── migrate-to-isolated.sh     # 数据迁移脚本
```

## 🔒 安全注意事项

1. **密码安全**: 生产环境请修改默认密码
2. **网络安全**: 独立网络仅供内部通信
3. **数据备份**: 定期备份重要数据
4. **访问控制**: 确保只有授权用户可以访问管理工具

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查服务日志获取详细错误信息
3. 确保Docker服务正常运行
4. 验证端口没有被其他服务占用

---

**注意**: 此独立环境专为开发和测试目的设计，生产环境部署请参考相应的生产环境配置文档。
