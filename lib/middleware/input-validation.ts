import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'

// 输入验证和清洗配置
interface ValidationConfig {
  enableXSSProtection?: boolean
  enableSQLInjectionProtection?: boolean
  enablePathTraversalProtection?: boolean
  maxRequestSize?: number
  allowedFileTypes?: string[]
  sanitizeHTML?: boolean
  validateJSON?: boolean
}

const defaultConfig: Required<ValidationConfig> = {
  enableXSSProtection: true,
  enableSQLInjectionProtection: true,
  enablePathTraversalProtection: true,
  maxRequestSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
  sanitizeHTML: true,
  validateJSON: true
}

// XSS攻击模式
const XSS_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,
  /<iframe[^>]*>.*?<\/iframe>/gi,
  /<object[^>]*>.*?<\/object>/gi,
  /<embed[^>]*>/gi,
  /<link[^>]*>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /data:text\/html/gi,
  /on\w+\s*=/gi, // onclick, onload等事件
  /<svg[^>]*>.*?<\/svg>/gi
]

// SQL注入攻击模式
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
  /(\b(OR|AND)\s+[\w\s]*\s*=\s*[\w\s]*)/gi,
  /(--|\/\*|\*\/)/gi,
  /(\bUNION\b.*\bSELECT\b)/gi,
  /(\b1\s*=\s*1\b|\b1\s*=\s*'1'\b)/gi,
  /(\'\s*(OR|AND)\s*\'\w*\'\s*=\s*\'\w*\')/gi
]

// 路径遍历攻击模式
const PATH_TRAVERSAL_PATTERNS = [
  /\.\.\//gi,
  /\.\.\\/gi,
  /%2e%2e%2f/gi,
  /%2e%2e%5c/gi,
  /\.\.%2f/gi,
  /\.\.%5c/gi
]

export class InputValidator {
  private config: Required<ValidationConfig>

  constructor(config: Partial<ValidationConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
  }

  // 检测XSS攻击
  detectXSS(input: string): boolean {
    return XSS_PATTERNS.some(pattern => pattern.test(input))
  }

  // 检测SQL注入
  detectSQLInjection(input: string): boolean {
    return SQL_INJECTION_PATTERNS.some(pattern => pattern.test(input))
  }

  // 检测路径遍历
  detectPathTraversal(input: string): boolean {
    return PATH_TRAVERSAL_PATTERNS.some(pattern => pattern.test(input))
  }

  // 清洗HTML内容
  sanitizeHTML(input: string): string {
    if (!this.config.sanitizeHTML) return input
    
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: ['class', 'id'],
      FORBID_SCRIPTS: true,
      FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'link', 'style']
    })
  }

  // 清洗字符串（移除危险字符）
  sanitizeString(input: string): string {
    // URL解码
    let cleaned = decodeURIComponent(input)
    
    // 移除控制字符
    cleaned = cleaned.replace(/[\x00-\x1F\x7F]/g, '')
    
    // 规范化空格
    cleaned = cleaned.replace(/\s+/g, ' ').trim()
    
    // 移除危险的HTML实体
    cleaned = cleaned.replace(/&[#\w]+;/g, '')
    
    return cleaned
  }

  // 验证文件名
  validateFileName(fileName: string): boolean {
    // 检查文件扩展名
    const extension = fileName.split('.').pop()?.toLowerCase()
    if (!extension || !this.config.allowedFileTypes.includes(extension)) {
      return false
    }

    // 检查危险字符
    const dangerousChars = /[<>:"/\\|?*\x00-\x1F]/
    if (dangerousChars.test(fileName)) {
      return false
    }

    // 检查路径遍历
    if (this.detectPathTraversal(fileName)) {
      return false
    }

    return true
  }

  // 验证URL
  validateURL(url: string): boolean {
    try {
      const parsedURL = new URL(url)
      
      // 只允许http和https协议
      if (!['http:', 'https:'].includes(parsedURL.protocol)) {
        return false
      }

      // 检查是否包含危险模式
      if (this.detectXSS(url) || this.detectPathTraversal(url)) {
        return false
      }

      return true
    } catch {
      return false
    }
  }

  // 验证邮箱
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && !this.detectXSS(email)
  }

  // 验证电话号码
  validatePhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{8,20}$/
    return phoneRegex.test(phone) && !this.detectXSS(phone)
  }

  // 深度验证对象
  validateObjectDeep(obj: any, schema?: z.ZodSchema): { valid: boolean; sanitized: any; errors: string[] } {
    const errors: string[] = []
    const sanitized = this.sanitizeObjectDeep(obj)

    // 使用Zod schema验证（如果提供）
    if (schema) {
      try {
        schema.parse(sanitized)
      } catch (error) {
        if (error instanceof z.ZodError) {
          errors.push(...error.errors.map(e => `${e.path.join('.')}: ${e.message}`))
        } else {
          errors.push('Schema validation failed')
        }
      }
    }

    return {
      valid: errors.length === 0,
      sanitized,
      errors
    }
  }

  // 深度清洗对象
  private sanitizeObjectDeep(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj
    }

    if (typeof obj === 'string') {
      let sanitized = this.sanitizeString(obj)
      
      if (this.config.enableXSSProtection && this.detectXSS(sanitized)) {
        sanitized = this.sanitizeHTML(sanitized)
      }
      
      return sanitized
    }

    if (typeof obj === 'number' || typeof obj === 'boolean') {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObjectDeep(item))
    }

    if (typeof obj === 'object') {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(obj)) {
        const cleanKey = this.sanitizeString(key)
        sanitized[cleanKey] = this.sanitizeObjectDeep(value)
      }
      return sanitized
    }

    return obj
  }

  // 中间件函数
  middleware(schema?: z.ZodSchema) {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      try {
        // 检查请求大小
        const contentLength = req.headers.get('content-length')
        if (contentLength && parseInt(contentLength) > this.config.maxRequestSize) {
          return new NextResponse(
            JSON.stringify({
              error: 'Request too large',
              message: '请求数据过大'
            }),
            {
              status: 413,
              headers: { 'Content-Type': 'application/json' }
            }
          )
        }

        // 验证URL参数
        const url = new URL(req.url)
        for (const [key, value] of url.searchParams.entries()) {
          if (this.config.enableXSSProtection && this.detectXSS(value)) {
            return new NextResponse('XSS attack detected in URL parameters', { status: 400 })
          }
          
          if (this.config.enableSQLInjectionProtection && this.detectSQLInjection(value)) {
            return new NextResponse('SQL injection attempt detected', { status: 400 })
          }

          if (this.config.enablePathTraversalProtection && this.detectPathTraversal(value)) {
            return new NextResponse('Path traversal attempt detected', { status: 400 })
          }
        }

        // 验证请求体（如果存在）
        if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
          const contentType = req.headers.get('content-type') || ''
          
          if (contentType.includes('application/json')) {
            try {
              const body = await req.json()
              const validation = this.validateObjectDeep(body, schema)
              
              if (!validation.valid) {
                return new NextResponse(
                  JSON.stringify({
                    error: 'Validation failed',
                    details: validation.errors
                  }),
                  {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                  }
                )
              }

              // 将清洗后的数据附加到请求上（在实际使用中需要自定义逻辑）
              // req.validatedBody = validation.sanitized
            } catch (error) {
              return new NextResponse('Invalid JSON', { status: 400 })
            }
          }
        }

        return NextResponse.next()
      } catch (error) {
        console.error('Input validation middleware error:', error)
        return new NextResponse('Validation error', { status: 500 })
      }
    }
  }
}

// 预定义的验证器
export const generalValidator = new InputValidator()

export const strictValidator = new InputValidator({
  enableXSSProtection: true,
  enableSQLInjectionProtection: true,
  enablePathTraversalProtection: true,
  maxRequestSize: 5 * 1024 * 1024, // 5MB
  sanitizeHTML: true,
  validateJSON: true
})

export const fileUploadValidator = new InputValidator({
  maxRequestSize: 50 * 1024 * 1024, // 50MB for file uploads
  allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'zip']
})

// 预定义的Zod schemas
export const userSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  phone: z.string().optional(),
  role: z.enum(['ADMIN', 'MANAGER', 'EMPLOYEE', 'GUEST']).optional()
})

export const productSchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().optional(),
  price: z.number().min(0),
  cost: z.number().min(0).optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional()
})

export const employeeSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  phone: z.string().optional(),
  department: z.string().min(1).max(100),
  position: z.string().min(1).max(100),
  salary: z.number().min(0),
  hireDate: z.string().datetime().optional()
})

// 工具函数：为API路由添加输入验证
export function withInputValidation(validator: InputValidator, schema?: z.ZodSchema) {
  return function(handler: Function) {
    return async (req: NextRequest, ...args: any[]) => {
      const validationResult = await validator.middleware(schema)(req)
      
      if (validationResult && validationResult.status !== 200) {
        return validationResult
      }

      return handler(req, ...args)
    }
  }
}

// 工具函数：安全的JSON解析
export async function safeParseJSON(req: NextRequest, schema?: z.ZodSchema): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const body = await req.json()
    const validator = new InputValidator()
    const validation = validator.validateObjectDeep(body, schema)
    
    if (!validation.valid) {
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`
      }
    }

    return {
      success: true,
      data: validation.sanitized
    }
  } catch (error) {
    return {
      success: false,
      error: 'Invalid JSON format'
    }
  }
}

// 工具函数：清洗查询参数
export function sanitizeSearchParams(searchParams: URLSearchParams): Record<string, string> {
  const validator = new InputValidator()
  const sanitized: Record<string, string> = {}
  
  for (const [key, value] of searchParams.entries()) {
    const cleanKey = validator.sanitizeString(key)
    const cleanValue = validator.sanitizeString(value)
    sanitized[cleanKey] = cleanValue
  }
  
  return sanitized
}

// 工具函数：验证和清洗文件上传
export function validateFileUpload(file: File): { valid: boolean; error?: string } {
  const validator = new InputValidator()
  
  if (!validator.validateFileName(file.name)) {
    return { valid: false, error: '文件名包含非法字符或不支持的文件类型' }
  }

  if (file.size > defaultConfig.maxRequestSize) {
    return { valid: false, error: '文件大小超出限制' }
  }

  return { valid: true }
}