/** @type {import('next').NextConfig} */
const nextConfig = {
  // 生产环境启用严格检查，开发环境可以放宽
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'development',
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'development',
  },
  
  // 优化图片处理
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    domains: ['localhost'],
    unoptimized: process.env.NODE_ENV === 'development',
  },

  // 实验性功能
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },

  // 编译优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },

  // 压缩配置
  compress: true,
  
  // 性能优化
  poweredByHeader: false,
  reactStrictMode: true,
  webpack: (config, { isServer, dev }) => {
    // 性能优化配置
    if (!isServer) {
      // 优化chunk文件名
      config.output.chunkFilename = dev
        ? 'static/chunks/[name].js'
        : 'static/chunks/[name].[contenthash:8].js';

      // 提高chunk加载可靠性
      config.output.crossOriginLoading = 'anonymous';

      // 优化代码分割
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              chunks: 'all',
            },
            // React相关库单独打包
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
              name: 'react',
              priority: 20,
              chunks: 'all',
            },
            // UI库单独打包
            ui: {
              test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
              name: 'ui',
              priority: 15,
              chunks: 'all',
            },
            // 工具库单独打包
            utils: {
              test: /[\\/]node_modules[\\/](date-fns|lodash|clsx)[\\/]/,
              name: 'utils',
              priority: 10,
              chunks: 'all',
            },
          },
        },
        // 运行时chunk优化
        runtimeChunk: {
          name: 'runtime',
        },
      };
    }

    // 生产环境优化
    if (!dev) {
      // 移除开发工具
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    return config;
  },

  // 页面缓存配置
  onDemandEntries: {
    maxInactiveAge: 60 * 1000, // 1分钟
    pagesBufferLength: 5,
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
}

export default nextConfig
