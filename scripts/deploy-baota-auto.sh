#!/bin/bash

# 宝塔面板自动化部署脚本
# 支持从Gitee自动拉取代码并部署独立Docker环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
GITEE_REPO="https://gitee.com/your-username/linghua-enamel-gallery.git"
PROJECT_DIR="/www/wwwroot/linghua-app"
BACKUP_DIR="/www/backup/linghua"
DOMAIN="your-domain.com"
DB_PASSWORD="bt_secure_password_$(date +%s)"
REDIS_PASSWORD="bt_redis_password_$(date +%s)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "🚀 宝塔面板自动化部署脚本"
    echo "================================"
    echo "本脚本将自动完成以下操作："
    echo "1. 检查宝塔环境"
    echo "2. 安装Docker和Docker Compose"
    echo "3. 从Gitee克隆项目代码"
    echo "4. 配置独立Docker环境"
    echo "5. 启动所有服务"
    echo "6. 配置Nginx反向代理"
    echo "7. 申请SSL证书"
    echo "================================"
    echo
}

# 获取用户输入
get_user_input() {
    log_info "请提供以下配置信息："
    
    read -p "Gitee仓库地址 (默认: $GITEE_REPO): " input_repo
    if [[ -n "$input_repo" ]]; then
        GITEE_REPO="$input_repo"
    fi
    
    read -p "域名 (默认: $DOMAIN): " input_domain
    if [[ -n "$input_domain" ]]; then
        DOMAIN="$input_domain"
    fi
    
    read -p "项目部署目录 (默认: $PROJECT_DIR): " input_dir
    if [[ -n "$input_dir" ]]; then
        PROJECT_DIR="$input_dir"
    fi
    
    echo
    log_info "配置确认："
    echo "  仓库地址: $GITEE_REPO"
    echo "  域名: $DOMAIN"
    echo "  部署目录: $PROJECT_DIR"
    echo "  数据库密码: $DB_PASSWORD"
    echo "  Redis密码: $REDIS_PASSWORD"
    echo
    
    read -p "确认开始部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_error "用户取消部署"
        exit 1
    fi
}

# 检查宝塔环境
check_baota_environment() {
    log_info "检查宝塔面板环境..."
    
    # 检查是否安装了宝塔面板
    if [[ ! -f "/www/server/panel/BT-Panel" ]]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    # 检查宝塔服务状态
    if ! systemctl is-active --quiet bt; then
        log_warning "宝塔面板服务未运行，正在启动..."
        systemctl start bt
    fi
    
    log_success "宝塔面板环境检查完成"
}

# 安装Docker
install_docker() {
    log_info "检查并安装Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，跳过安装步骤"
        return
    fi
    
    # 使用宝塔软件商店安装Docker（如果可用）
    if [[ -f "/www/server/panel/plugin/docker/docker_main.py" ]]; then
        log_info "通过宝塔软件商店安装Docker..."
        python3 /www/server/panel/plugin/docker/docker_main.py install
    else
        # 手动安装Docker
        log_info "手动安装Docker..."
        curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun
        
        # 启动Docker服务
        systemctl start docker
        systemctl enable docker
        
        # 配置Docker镜像加速
        mkdir -p /etc/docker
        cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
        systemctl restart docker
    fi
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_info "安装Docker Compose..."
        curl -L "https://get.daocloud.io/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    fi
    
    log_success "Docker安装完成"
}

# 创建项目目录
create_project_directories() {
    log_info "创建项目目录..."
    
    # 创建主要目录
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/www/server/data/postgres"
    mkdir -p "/www/server/data/redis"
    
    # 设置权限
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    
    log_success "项目目录创建完成"
}

# 从Gitee克隆代码
clone_from_gitee() {
    log_info "从Gitee克隆项目代码..."
    
    # 备份现有项目（如果存在）
    if [[ -d "$PROJECT_DIR/.git" ]]; then
        log_info "备份现有项目..."
        cp -r "$PROJECT_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 清空项目目录
    rm -rf "$PROJECT_DIR"/*
    rm -rf "$PROJECT_DIR"/.*
    
    # 克隆代码
    cd "$(dirname "$PROJECT_DIR")"
    git clone "$GITEE_REPO" "$(basename "$PROJECT_DIR")"
    
    # 设置权限
    chown -R www:www "$PROJECT_DIR"
    chmod +x "$PROJECT_DIR"/scripts/*.sh
    
    log_success "代码克隆完成"
}

# 配置环境文件
configure_environment() {
    log_info "配置环境变量..."
    
    cd "$PROJECT_DIR"
    
    # 创建生产环境配置
    cat > .env.production << EOF
# 生产环境配置 - 宝塔面板部署
DATABASE_URL="postgresql://postgres:${DB_PASSWORD}@linghua-bt-postgres:5432/linghua_enamel_gallery_isolated?schema=public"
NEXTAUTH_URL="https://${DOMAIN}"
NEXTAUTH_SECRET="bt-super-secure-secret-$(openssl rand -hex 32)"
AUTH_SECRET="bt-super-secure-secret-$(openssl rand -hex 32)"
AUTH_TRUST_HOST="true"
REDIS_URL="redis://:${REDIS_PASSWORD}@linghua-bt-redis:6379"
NODE_ENV="production"
ENVIRONMENT="production"

# 应用配置
NEXT_PUBLIC_APP_URL="https://${DOMAIN}"
NEXT_PUBLIC_APP_NAME="灵华珐琅馆管理系统"
NEXT_PUBLIC_APP_VERSION="1.0.0-production"

# 安全配置
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="24h"
SESSION_TIMEOUT="86400"
EOF
    
    log_success "环境配置完成"
}

# 配置Docker Compose
configure_docker_compose() {
    log_info "配置Docker Compose..."
    
    cd "$PROJECT_DIR"
    
    # 创建生产环境Docker配置
    cat > docker-compose.production.yml << EOF
services:
  linghua-bt-postgres:
    image: postgres:15-alpine
    container_name: linghua-bt-postgres
    restart: always
    environment:
      POSTGRES_DB: linghua_enamel_gallery_isolated
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5434:5432"
    volumes:
      - /www/server/data/postgres:/var/lib/postgresql/data
    networks:
      - linghua-bt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d linghua_enamel_gallery_isolated"]
      interval: 10s
      timeout: 5s
      retries: 5

  linghua-bt-redis:
    image: redis:7-alpine
    container_name: linghua-bt-redis
    restart: always
    ports:
      - "6381:6379"
    volumes:
      - /www/server/data/redis:/data
    networks:
      - linghua-bt-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  linghua-bt-app:
    image: node:18-alpine
    container_name: linghua-bt-app
    restart: always
    working_dir: /app
    command: sh -c "npm config set registry https://registry.npmmirror.com/ && npm install --production && npm run build && npm start"
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${DB_PASSWORD}@linghua-bt-postgres:5432/linghua_enamel_gallery_isolated?schema=public
      - NEXTAUTH_URL=https://${DOMAIN}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@linghua-bt-redis:6379
    volumes:
      - ${PROJECT_DIR}:/app
      - /app/node_modules
      - ${PROJECT_DIR}/uploads:/app/uploads
    networks:
      - linghua-bt-network
    depends_on:
      linghua-bt-postgres:
        condition: service_healthy
      linghua-bt-redis:
        condition: service_healthy

  linghua-bt-adminer:
    image: adminer:latest
    container_name: linghua-bt-adminer
    restart: always
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=linghua-bt-postgres
      - ADMINER_DESIGN=pepa-linha
    networks:
      - linghua-bt-network
    depends_on:
      - linghua-bt-postgres

networks:
  linghua-bt-network:
    driver: bridge
    name: linghua-bt-network
EOF
    
    log_success "Docker Compose配置完成"
}

# 启动Docker服务
start_docker_services() {
    log_info "启动Docker服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止可能存在的旧服务
    docker-compose -f docker-compose.production.yml down 2>/dev/null || true
    
    # 启动服务
    docker-compose -f docker-compose.production.yml --env-file .env.production up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        log_success "Docker服务启动成功"
    else
        log_error "Docker服务启动失败"
        docker-compose -f docker-compose.production.yml logs
        exit 1
    fi
}

# 初始化数据库
initialize_database() {
    log_info "初始化数据库..."
    
    cd "$PROJECT_DIR"
    
    # 等待数据库完全启动
    sleep 10
    
    # 生成Prisma客户端并推送数据库模式
    docker exec linghua-bt-app sh -c "npx prisma generate && npx prisma db push"
    
    log_success "数据库初始化完成"
}

# 配置Nginx（通过宝塔API）
configure_nginx() {
    log_info "配置Nginx反向代理..."
    
    # 创建站点配置文件
    cat > "/www/server/panel/vhost/nginx/${DOMAIN}.conf" << EOF
server {
    listen 80;
    server_name ${DOMAIN};
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN};
    
    # SSL配置（证书路径将在申请SSL后自动更新）
    ssl_certificate /www/server/panel/vhost/cert/${DOMAIN}/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/${DOMAIN}/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 反向代理到应用
    location / {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 静态文件直接服务
    location /uploads/ {
        alias ${PROJECT_DIR}/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
EOF
    
    # 重载Nginx配置
    nginx -t && systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 申请SSL证书
apply_ssl_certificate() {
    log_info "申请SSL证书..."
    
    # 创建证书目录
    mkdir -p "/www/server/panel/vhost/cert/${DOMAIN}"
    
    # 使用Let's Encrypt申请证书
    if command -v certbot &> /dev/null; then
        certbot certonly --webroot -w /www/wwwroot/default -d "$DOMAIN" --email admin@"$DOMAIN" --agree-tos --non-interactive
        
        # 复制证书到宝塔目录
        cp "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" "/www/server/panel/vhost/cert/${DOMAIN}/"
        cp "/etc/letsencrypt/live/${DOMAIN}/privkey.pem" "/www/server/panel/vhost/cert/${DOMAIN}/"
        
        log_success "SSL证书申请成功"
    else
        log_warning "certbot未安装，请手动申请SSL证书"
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 创建重启脚本
    cat > "${PROJECT_DIR}/restart-services.sh" << EOF
#!/bin/bash
cd ${PROJECT_DIR}
docker-compose -f docker-compose.production.yml restart
echo "服务重启完成"
EOF
    
    # 创建备份脚本
    cat > "${PROJECT_DIR}/backup-data.sh" << EOF
#!/bin/bash
BACKUP_DIR="${BACKUP_DIR}/\$(date +%Y%m%d_%H%M%S)"
mkdir -p \$BACKUP_DIR

# 备份数据库
docker exec linghua-bt-postgres pg_dump -U postgres linghua_enamel_gallery_isolated > \$BACKUP_DIR/database.sql

# 备份上传文件
cp -r ${PROJECT_DIR}/uploads \$BACKUP_DIR/

# 压缩备份
tar -czf \$BACKUP_DIR.tar.gz -C ${BACKUP_DIR}/.. \$(basename \$BACKUP_DIR)
rm -rf \$BACKUP_DIR

echo "备份完成: \$BACKUP_DIR.tar.gz"
EOF
    
    # 创建更新脚本
    cat > "${PROJECT_DIR}/update-from-gitee.sh" << EOF
#!/bin/bash
cd ${PROJECT_DIR}

# 备份当前版本
cp -r . ${BACKUP_DIR}/backup-before-update-\$(date +%Y%m%d_%H%M%S)

# 拉取最新代码
git pull origin main

# 重启服务
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d

echo "更新完成"
EOF
    
    # 设置执行权限
    chmod +x "${PROJECT_DIR}"/*.sh
    
    log_success "管理脚本创建完成"
}

# 显示部署结果
show_deployment_result() {
    echo
    log_success "🎉 宝塔面板自动化部署完成！"
    echo
    echo "📋 部署信息："
    echo "  🌐 网站地址: https://${DOMAIN}"
    echo "  🗄️  数据库管理: http://${DOMAIN}:8081"
    echo "  📁 项目目录: ${PROJECT_DIR}"
    echo "  💾 备份目录: ${BACKUP_DIR}"
    echo
    echo "🔐 数据库连接信息："
    echo "  服务器: localhost:5434"
    echo "  数据库: linghua_enamel_gallery_isolated"
    echo "  用户名: postgres"
    echo "  密码: ${DB_PASSWORD}"
    echo
    echo "📝 管理命令："
    echo "  重启服务: ${PROJECT_DIR}/restart-services.sh"
    echo "  备份数据: ${PROJECT_DIR}/backup-data.sh"
    echo "  更新代码: ${PROJECT_DIR}/update-from-gitee.sh"
    echo
    echo "🔧 Docker管理："
    echo "  查看状态: docker-compose -f ${PROJECT_DIR}/docker-compose.production.yml ps"
    echo "  查看日志: docker-compose -f ${PROJECT_DIR}/docker-compose.production.yml logs -f"
    echo
}

# 主函数
main() {
    show_welcome
    get_user_input
    check_baota_environment
    install_docker
    create_project_directories
    clone_from_gitee
    configure_environment
    configure_docker_compose
    start_docker_services
    initialize_database
    configure_nginx
    apply_ssl_certificate
    create_management_scripts
    show_deployment_result
    
    log_success "自动化部署完成！"
}

# 执行主函数
main "$@"
