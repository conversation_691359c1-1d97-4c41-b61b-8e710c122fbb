import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

export async function GET() {
  try {
    // 查询最近的系统活动
    const activities = await Promise.all([
      // 最近的销售订单
      prisma.salesOrder.findMany({
        take: 5,
        orderBy: { orderDate: 'desc' },
        include: { customer: true }
      }).then(orders => orders.map(order => ({
        id: order.id,
        type: 'sale',
        title: `新销售订单 #${order.id}`,
        description: `客户: ${order.customer?.name || '未知'}, 金额: ¥${order.totalAmount}`,
        timestamp: order.orderDate?.toISOString() || new Date().toISOString(),
        user: '系统',
        status: order.status
      }))).catch(() => []),

      // 最近的产品更新
      prisma.product.findMany({
        take: 3,
        orderBy: { updatedAt: 'desc' }
      }).then(products => products.map(product => ({
        id: product.id,
        type: 'product',
        title: `产品更新: ${product.name}`,
        description: `库存: ${product.currentStock}, 价格: ¥${product.price}`,
        timestamp: product.updatedAt.toISOString(),
        user: '系统',
        status: 'updated'
      }))).catch(() => []),

      // 最近的员工活动
      prisma.user.findMany({
        take: 2,
        orderBy: { updatedAt: 'desc' },
        where: { role: 'EMPLOYEE' }
      }).then(users => users.map(user => ({
        id: user.id,
        type: 'employee',
        title: `员工登录: ${user.name}`,
        description: `最后活动时间`,
        timestamp: user.updatedAt.toISOString(),
        user: user.name || '未知用户',
        status: 'active'
      }))).catch(() => [])
    ])

    // 合并并排序所有活动
    const allActivities = activities.flat().sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    ).slice(0, 10)

    return NextResponse.json(allActivities)
  } catch (error) {
    console.error('查询活动数据失败:', error)
    
    // 返回示例数据
    const sampleActivities = [
      {
        id: '1',
        type: 'sale',
        title: '新销售订单 #001',
        description: '客户: 张女士, 金额: ¥2,800',
        timestamp: new Date().toISOString(),
        user: '销售员',
        status: 'completed'
      },
      {
        id: '2',
        type: 'product',
        title: '产品更新: 珐琅花瓶',
        description: '库存: 15, 价格: ¥1,200',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        user: '管理员',
        status: 'updated'
      }
    ]

    return NextResponse.json(sampleActivities)
  }
}
