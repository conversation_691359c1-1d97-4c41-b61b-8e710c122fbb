"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  WebVitalsMonitor, 
  PerformanceBudget, 
  PerformanceReporter 
} from './web-vitals-monitor'
import {
  Activity,
  Zap,
  Clock,
  Eye,
  Gauge,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'

interface MetricCardProps {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  unit: string
  icon: React.ReactNode
  description: string
}

function MetricCard({ name, value, rating, unit, icon, description }: MetricCardProps) {
  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'text-green-600 bg-green-50 border-green-200'
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'poor': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRatingBadgeColor = (rating: string) => {
    switch (rating) {
      case 'good': return 'bg-green-100 text-green-800'
      case 'needs-improvement': return 'bg-yellow-100 text-yellow-800'
      case 'poor': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card className={`border-2 ${getRatingColor(rating)}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{name}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {value.toFixed(name === 'CLS' ? 3 : 0)}{unit}
        </div>
        <div className="flex items-center justify-between mt-2">
          <p className="text-xs text-muted-foreground">{description}</p>
          <Badge className={getRatingBadgeColor(rating)}>
            {rating === 'good' ? '良好' : rating === 'needs-improvement' ? '需改进' : '较差'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}

interface PerformanceDashboardProps {
  showRealTime?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

export function PerformanceDashboard({ 
  showRealTime = true, 
  autoRefresh = true,
  refreshInterval = 30000 
}: PerformanceDashboardProps) {
  const [metrics, setMetrics] = useState<Record<string, any>>({})
  const [budgetStatus, setBudgetStatus] = useState<any>(null)
  const [performanceReport, setPerformanceReport] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // 处理Web Vitals指标
  const handleMetric = (metric: any) => {
    setMetrics(prev => ({
      ...prev,
      [metric.name]: metric
    }))
    
    PerformanceReporter.addMetric(metric)
    setPerformanceReport(PerformanceReporter.generateReport())
  }

  // 检查性能预算
  const checkBudget = async () => {
    const status = await PerformanceBudget.getBudgetStatus()
    setBudgetStatus(status)
  }

  // 刷新数据
  const refreshData = async () => {
    setIsLoading(true)
    try {
      await checkBudget()
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to refresh performance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(refreshData, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // 初始化
  useEffect(() => {
    checkBudget()
  }, [])

  const metricConfigs = [
    {
      key: 'LCP',
      name: 'Largest Contentful Paint',
      unit: 'ms',
      icon: <Eye className="h-4 w-4" />,
      description: '最大内容绘制时间'
    },
    {
      key: 'FID',
      name: 'First Input Delay',
      unit: 'ms',
      icon: <Zap className="h-4 w-4" />,
      description: '首次输入延迟'
    },
    {
      key: 'CLS',
      name: 'Cumulative Layout Shift',
      unit: '',
      icon: <Activity className="h-4 w-4" />,
      description: '累积布局偏移'
    },
    {
      key: 'FCP',
      name: 'First Contentful Paint',
      unit: 'ms',
      icon: <Clock className="h-4 w-4" />,
      description: '首次内容绘制'
    },
    {
      key: 'TTFB',
      name: 'Time to First Byte',
      unit: 'ms',
      icon: <Gauge className="h-4 w-4" />,
      description: '首字节时间'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 实时监控 */}
      {showRealTime && (
        <WebVitalsMonitor 
          onMetric={handleMetric}
          reportToAnalytics={true}
          debug={process.env.NODE_ENV === 'development'}
        />
      )}

      {/* 头部信息 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">性能监控仪表板</h2>
          <p className="text-muted-foreground">
            最后更新: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={refreshData} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新数据
        </Button>
      </div>

      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">核心指标</TabsTrigger>
          <TabsTrigger value="budget">性能预算</TabsTrigger>
          <TabsTrigger value="report">性能报告</TabsTrigger>
        </TabsList>

        {/* 核心指标 */}
        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {metricConfigs.map(config => {
              const metric = metrics[config.key]
              return (
                <MetricCard
                  key={config.key}
                  name={config.name}
                  value={metric?.value || 0}
                  rating={metric?.rating || 'good'}
                  unit={config.unit}
                  icon={config.icon}
                  description={config.description}
                />
              )
            })}
          </div>

          {/* 性能趋势图表区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                性能趋势
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground py-8">
                性能趋势图表将在这里显示
                <br />
                <small>需要集成图表库如 Recharts 或 Chart.js</small>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 性能预算 */}
        <TabsContent value="budget" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gauge className="h-5 w-5 mr-2" />
                性能预算状态
              </CardTitle>
            </CardHeader>
            <CardContent>
              {budgetStatus ? (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    {budgetStatus.passed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    )}
                    <span className="font-medium">
                      {budgetStatus.passed ? '所有预算检查通过' : '发现预算违规'}
                    </span>
                  </div>

                  {budgetStatus.violations.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">预算违规项目:</h4>
                      {budgetStatus.violations.map((violation: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <span className="font-medium">{violation.metric}</span>
                            <div className="text-sm text-muted-foreground">
                              实际: {violation.actual.toFixed(1)} / 预算: {violation.budget}
                            </div>
                          </div>
                          <Badge variant={violation.severity === 'error' ? 'destructive' : 'secondary'}>
                            {violation.severity === 'error' ? '严重' : '警告'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-4">
                  正在检查性能预算...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 性能报告 */}
        <TabsContent value="report" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>性能评分</CardTitle>
            </CardHeader>
            <CardContent>
              {performanceReport ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-4xl font-bold mb-2">
                      {performanceReport.summary.grade}
                    </div>
                    <div className="text-2xl text-muted-foreground mb-4">
                      {performanceReport.summary.score.toFixed(1)}分
                    </div>
                    <Progress 
                      value={performanceReport.summary.score} 
                      className="w-full max-w-md mx-auto"
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {performanceReport.summary.goodMetrics}
                      </div>
                      <div className="text-sm text-muted-foreground">良好指标</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">
                        {performanceReport.summary.totalMetrics}
                      </div>
                      <div className="text-sm text-muted-foreground">总指标数</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-red-600">
                        {performanceReport.summary.poorMetrics}
                      </div>
                      <div className="text-sm text-muted-foreground">较差指标</div>
                    </div>
                  </div>

                  {performanceReport.recommendations.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">优化建议:</h4>
                      <ul className="space-y-1">
                        {performanceReport.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-start">
                            <span className="mr-2">•</span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-4">
                  暂无性能数据
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}