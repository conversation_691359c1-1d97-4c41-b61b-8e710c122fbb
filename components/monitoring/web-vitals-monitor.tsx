"use client"

import { useEffect, useRef } from 'react'
import { getCLS, getFID, getFCP, getLCP, getTTFB, Metric } from 'web-vitals'

interface WebVitalsData {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType: string
}

interface WebVitalsMonitorProps {
  onMetric?: (metric: WebVitalsData) => void
  reportToAnalytics?: boolean
  debug?: boolean
}

// 性能阈值配置
const THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 }
}

// 获取性能评级
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS]
  if (!threshold) return 'good'
  
  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

// 发送指标到分析服务
async function sendToAnalytics(metric: WebVitalsData) {
  try {
    // 发送到自定义分析端点
    await fetch('/api/analytics/web-vitals', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...metric,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      })
    })

    // 发送到Google Analytics（如果配置了）
    if (typeof gtag !== 'undefined') {
      gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        custom_map: {
          metric_rating: metric.rating
        }
      })
    }
  } catch (error) {
    console.error('Failed to send metric to analytics:', error)
  }
}

export function WebVitalsMonitor({ 
  onMetric, 
  reportToAnalytics = true, 
  debug = false 
}: WebVitalsMonitorProps) {
  const metricsRef = useRef<Map<string, WebVitalsData>>(new Map())

  useEffect(() => {
    const handleMetric = (metric: Metric) => {
      const webVitalsData: WebVitalsData = {
        name: metric.name,
        value: metric.value,
        rating: getRating(metric.name, metric.value),
        delta: metric.delta,
        id: metric.id,
        navigationType: metric.navigationType || 'navigate'
      }

      // 存储指标
      metricsRef.current.set(metric.name, webVitalsData)

      // 调试输出
      if (debug) {
        console.log(`[Web Vitals] ${metric.name}:`, webVitalsData)
      }

      // 回调处理
      onMetric?.(webVitalsData)

      // 发送到分析服务
      if (reportToAnalytics) {
        sendToAnalytics(webVitalsData)
      }
    }

    // 注册所有Web Vitals指标
    getCLS(handleMetric)
    getFID(handleMetric)
    getFCP(handleMetric)
    getLCP(handleMetric)
    getTTFB(handleMetric)

    // 页面卸载时发送最终指标
    const handleBeforeUnload = () => {
      const metrics = Array.from(metricsRef.current.values())
      if (metrics.length > 0 && reportToAnalytics) {
        // 使用sendBeacon确保数据发送
        navigator.sendBeacon(
          '/api/analytics/web-vitals-batch',
          JSON.stringify({
            metrics,
            url: window.location.href,
            timestamp: Date.now()
          })
        )
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [onMetric, reportToAnalytics, debug])

  // 这个组件不渲染任何UI
  return null
}

// 性能预算监控
export class PerformanceBudget {
  private static budgets = {
    // 时间预算（毫秒）
    FCP: 1800,
    LCP: 2500,
    FID: 100,
    TTFB: 800,
    
    // 布局稳定性预算
    CLS: 0.1,
    
    // 资源预算（KB）
    totalJavaScript: 500,
    totalCSS: 100,
    totalImages: 1000,
    totalFonts: 100
  }

  static checkBudget(metric: WebVitalsData): boolean {
    const budget = this.budgets[metric.name as keyof typeof this.budgets]
    if (budget === undefined) return true
    
    return metric.value <= budget
  }

  static getBudgetStatus(): Promise<{
    passed: boolean
    violations: Array<{
      metric: string
      actual: number
      budget: number
      severity: 'warning' | 'error'
    }>
  }> {
    return new Promise((resolve) => {
      const violations: Array<{
        metric: string
        actual: number
        budget: number
        severity: 'warning' | 'error'
      }> = []

      // 检查资源大小
      if ('performance' in window && 'getEntriesByType' in performance) {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
        
        const jsSize = resources
          .filter(r => r.name.includes('.js'))
          .reduce((total, r) => total + (r.transferSize || 0), 0) / 1024

        const cssSize = resources
          .filter(r => r.name.includes('.css'))
          .reduce((total, r) => total + (r.transferSize || 0), 0) / 1024

        const imageSize = resources
          .filter(r => r.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i))
          .reduce((total, r) => total + (r.transferSize || 0), 0) / 1024

        const fontSize = resources
          .filter(r => r.name.match(/\.(woff|woff2|ttf|otf)$/i))
          .reduce((total, r) => total + (r.transferSize || 0), 0) / 1024

        // 检查预算违规
        if (jsSize > this.budgets.totalJavaScript) {
          violations.push({
            metric: 'totalJavaScript',
            actual: jsSize,
            budget: this.budgets.totalJavaScript,
            severity: jsSize > this.budgets.totalJavaScript * 1.5 ? 'error' : 'warning'
          })
        }

        if (cssSize > this.budgets.totalCSS) {
          violations.push({
            metric: 'totalCSS',
            actual: cssSize,
            budget: this.budgets.totalCSS,
            severity: cssSize > this.budgets.totalCSS * 1.5 ? 'error' : 'warning'
          })
        }

        if (imageSize > this.budgets.totalImages) {
          violations.push({
            metric: 'totalImages',
            actual: imageSize,
            budget: this.budgets.totalImages,
            severity: imageSize > this.budgets.totalImages * 1.5 ? 'error' : 'warning'
          })
        }

        if (fontSize > this.budgets.totalFonts) {
          violations.push({
            metric: 'totalFonts',
            actual: fontSize,
            budget: this.budgets.totalFonts,
            severity: fontSize > this.budgets.totalFonts * 1.5 ? 'error' : 'warning'
          })
        }
      }

      resolve({
        passed: violations.length === 0,
        violations
      })
    })
  }

  static updateBudgets(newBudgets: Partial<typeof PerformanceBudget.budgets>) {
    Object.assign(this.budgets, newBudgets)
  }
}

// 性能报告生成器
export class PerformanceReporter {
  private static metrics: WebVitalsData[] = []

  static addMetric(metric: WebVitalsData) {
    this.metrics.push(metric)
  }

  static generateReport(): {
    summary: {
      score: number
      grade: 'A' | 'B' | 'C' | 'D' | 'F'
      totalMetrics: number
      goodMetrics: number
      poorMetrics: number
    }
    details: WebVitalsData[]
    recommendations: string[]
  } {
    const goodCount = this.metrics.filter(m => m.rating === 'good').length
    const poorCount = this.metrics.filter(m => m.rating === 'poor').length
    const total = this.metrics.length

    const score = total > 0 ? (goodCount / total) * 100 : 0
    
    let grade: 'A' | 'B' | 'C' | 'D' | 'F'
    if (score >= 90) grade = 'A'
    else if (score >= 80) grade = 'B'
    else if (score >= 70) grade = 'C'
    else if (score >= 60) grade = 'D'
    else grade = 'F'

    const recommendations: string[] = []
    
    this.metrics.forEach(metric => {
      if (metric.rating === 'poor') {
        switch (metric.name) {
          case 'LCP':
            recommendations.push('优化最大内容绘制：压缩图片、使用CDN、优化服务器响应时间')
            break
          case 'FID':
            recommendations.push('减少首次输入延迟：减少JavaScript执行时间、使用Web Workers')
            break
          case 'CLS':
            recommendations.push('改善累积布局偏移：为图片设置尺寸、避免动态插入内容')
            break
          case 'FCP':
            recommendations.push('优化首次内容绘制：内联关键CSS、优化字体加载')
            break
          case 'TTFB':
            recommendations.push('减少首字节时间：优化服务器配置、使用缓存')
            break
        }
      }
    })

    return {
      summary: {
        score,
        grade,
        totalMetrics: total,
        goodMetrics: goodCount,
        poorMetrics: poorCount
      },
      details: this.metrics,
      recommendations: [...new Set(recommendations)]
    }
  }

  static reset() {
    this.metrics = []
  }
}