import { PrismaClient, Prisma } from '@prisma/client'
import { LRUCache } from 'lru-cache'

// 查询性能优化配置
interface QueryOptimizerConfig {
  enableQueryCache?: boolean
  cacheSize?: number
  cacheTTL?: number
  enableQueryLogging?: boolean
  slowQueryThreshold?: number
  enableConnectionPooling?: boolean
  maxConnections?: number
  enableReadReplicas?: boolean
  readReplicaUrls?: string[]
}

const defaultConfig: Required<QueryOptimizerConfig> = {
  enableQueryCache: true,
  cacheSize: 1000,
  cacheTTL: 300000, // 5分钟
  enableQueryLogging: process.env.NODE_ENV === 'development',
  slowQueryThreshold: 1000, // 1秒
  enableConnectionPooling: true,
  maxConnections: 10,
  enableReadReplicas: false,
  readReplicaUrls: []
}

// 查询缓存实例
let queryCache: LRUCache<string, any> | null = null

// 查询统计
interface QueryStats {
  query: string
  duration: number
  timestamp: Date
  cached: boolean
}

const queryStats: QueryStats[] = []

export class QueryOptimizer {
  private config: Required<QueryOptimizerConfig>
  private masterClient: PrismaClient
  private readClients: PrismaClient[]

  constructor(config: Partial<QueryOptimizerConfig> = {}) {
    this.config = { ...defaultConfig, ...config }
    
    // 初始化查询缓存
    if (this.config.enableQueryCache && !queryCache) {
      queryCache = new LRUCache({
        max: this.config.cacheSize,
        ttl: this.config.cacheTTL
      })
    }

    // 初始化主数据库连接
    this.masterClient = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL
        }
      },
      log: this.config.enableQueryLogging ? [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'warn' }
      ] : []
    })

    // 初始化读副本连接
    this.readClients = []
    if (this.config.enableReadReplicas && this.config.readReplicaUrls.length > 0) {
      this.readClients = this.config.readReplicaUrls.map(url => 
        new PrismaClient({
          datasources: { db: { url } }
        })
      )
    }

    // 设置查询日志
    if (this.config.enableQueryLogging) {
      this.setupQueryLogging()
    }
  }

  // 设置查询日志
  private setupQueryLogging(): void {
    this.masterClient.$on('query', (e) => {
      const duration = parseInt(e.duration)
      
      queryStats.push({
        query: e.query,
        duration,
        timestamp: new Date(),
        cached: false
      })

      // 记录慢查询
      if (duration > this.config.slowQueryThreshold) {
        console.warn(`[SLOW QUERY] ${duration}ms: ${e.query}`)
      }

      // 保持统计数据大小
      if (queryStats.length > 1000) {
        queryStats.splice(0, 500)
      }
    })

    this.masterClient.$on('error', (e) => {
      console.error('[DATABASE ERROR]', e)
    })
  }

  // 生成缓存键
  private generateCacheKey(model: string, operation: string, args: any): string {
    return `${model}:${operation}:${JSON.stringify(args)}`
  }

  // 获取读客户端（负载均衡）
  private getReadClient(): PrismaClient {
    if (this.readClients.length === 0) {
      return this.masterClient
    }
    
    const index = Math.floor(Math.random() * this.readClients.length)
    return this.readClients[index]
  }

  // 优化的查询方法
  async optimizedQuery<T>(
    model: string,
    operation: string,
    args: any,
    options: {
      useCache?: boolean
      useReadReplica?: boolean
      cacheTTL?: number
    } = {}
  ): Promise<T> {
    const {
      useCache = this.config.enableQueryCache,
      useReadReplica = this.config.enableReadReplicas && ['findMany', 'findFirst', 'findUnique', 'count', 'aggregate'].includes(operation),
      cacheTTL = this.config.cacheTTL
    } = options

    const cacheKey = this.generateCacheKey(model, operation, args)
    const startTime = Date.now()

    // 尝试从缓存获取
    if (useCache && queryCache && ['findMany', 'findFirst', 'findUnique', 'count'].includes(operation)) {
      const cached = queryCache.get(cacheKey)
      if (cached) {
        queryStats.push({
          query: `${model}.${operation}`,
          duration: Date.now() - startTime,
          timestamp: new Date(),
          cached: true
        })
        return cached
      }
    }

    // 选择数据库客户端
    const client = useReadReplica ? this.getReadClient() : this.masterClient

    try {
      // 执行查询
      const result = await (client[model as keyof PrismaClient] as any)[operation](args)
      
      // 缓存结果（只缓存读操作）
      if (useCache && queryCache && ['findMany', 'findFirst', 'findUnique', 'count'].includes(operation)) {
        queryCache.set(cacheKey, result, { ttl: cacheTTL })
      }

      return result
    } catch (error) {
      console.error(`[QUERY ERROR] ${model}.${operation}:`, error)
      throw error
    }
  }

  // 清除缓存
  clearCache(pattern?: string): void {
    if (!queryCache) return

    if (pattern) {
      // 清除匹配模式的缓存
      for (const key of queryCache.keys()) {
        if (key.includes(pattern)) {
          queryCache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      queryCache.clear()
    }
  }

  // 预热缓存
  async warmupCache(queries: Array<{ model: string; operation: string; args: any }>): Promise<void> {
    console.log(`[CACHE WARMUP] Starting warmup for ${queries.length} queries`)
    
    const promises = queries.map(async ({ model, operation, args }) => {
      try {
        await this.optimizedQuery(model, operation, args, { useCache: true })
      } catch (error) {
        console.error(`[CACHE WARMUP ERROR] ${model}.${operation}:`, error)
      }
    })

    await Promise.allSettled(promises)
    console.log('[CACHE WARMUP] Completed')
  }

  // 批量操作优化
  async batchOperation<T>(
    operations: Array<() => Promise<T>>,
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = []
    
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize)
      const batchResults = await Promise.allSettled(batch.map(op => op()))
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results[i + index] = result.value
        } else {
          console.error(`[BATCH ERROR] Operation ${i + index}:`, result.reason)
          throw result.reason
        }
      })
    }
    
    return results
  }

  // 获取查询统计
  getQueryStats(): {
    totalQueries: number
    averageDuration: number
    slowQueries: number
    cacheHitRate: number
    recentQueries: QueryStats[]
  } {
    if (queryStats.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        cacheHitRate: 0,
        recentQueries: []
      }
    }

    const totalQueries = queryStats.length
    const totalDuration = queryStats.reduce((sum, stat) => sum + stat.duration, 0)
    const averageDuration = totalDuration / totalQueries
    const slowQueries = queryStats.filter(stat => stat.duration > this.config.slowQueryThreshold).length
    const cachedQueries = queryStats.filter(stat => stat.cached).length
    const cacheHitRate = cachedQueries / totalQueries

    return {
      totalQueries,
      averageDuration,
      slowQueries,
      cacheHitRate,
      recentQueries: queryStats.slice(-20) // 最近20条查询
    }
  }

  // 数据库健康检查
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    latency: number
    connections: {
      master: boolean
      readReplicas: boolean[]
    }
    cache: {
      enabled: boolean
      size: number
      hitRate: number
    }
  }> {
    const startTime = Date.now()
    
    try {
      // 检查主数据库连接
      await this.masterClient.$queryRaw`SELECT 1`
      const masterLatency = Date.now() - startTime

      // 检查读副本连接
      const readReplicaStatus = await Promise.allSettled(
        this.readClients.map(client => client.$queryRaw`SELECT 1`)
      )

      const stats = this.getQueryStats()
      
      return {
        status: masterLatency < 100 ? 'healthy' : masterLatency < 500 ? 'degraded' : 'unhealthy',
        latency: masterLatency,
        connections: {
          master: true,
          readReplicas: readReplicaStatus.map(result => result.status === 'fulfilled')
        },
        cache: {
          enabled: this.config.enableQueryCache,
          size: queryCache?.size || 0,
          hitRate: stats.cacheHitRate
        }
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        connections: {
          master: false,
          readReplicas: []
        },
        cache: {
          enabled: this.config.enableQueryCache,
          size: queryCache?.size || 0,
          hitRate: 0
        }
      }
    }
  }

  // 连接池管理
  async closeConnections(): Promise<void> {
    await this.masterClient.$disconnect()
    await Promise.all(this.readClients.map(client => client.$disconnect()))
  }
}

// 默认查询优化器实例
export const queryOptimizer = new QueryOptimizer({
  enableQueryCache: true,
  cacheSize: 1000,
  cacheTTL: 300000, // 5分钟
  enableQueryLogging: process.env.NODE_ENV === 'development',
  slowQueryThreshold: 1000 // 1秒
})

// 优化的Prisma客户端扩展
export function createOptimizedPrismaClient(config?: Partial<QueryOptimizerConfig>) {
  const optimizer = new QueryOptimizer(config)
  
  return new Proxy(optimizer, {
    get(target, prop) {
      if (typeof prop === 'string' && prop in target) {
        return target[prop as keyof QueryOptimizer]
      }
      
      // 代理Prisma模型操作
      return new Proxy({}, {
        get(_, operation) {
          if (typeof operation === 'string') {
            return (args: any) => target.optimizedQuery(prop as string, operation, args)
          }
        }
      })
    }
  })
}

// 查询优化装饰器
export function withQueryOptimization(options?: { useCache?: boolean; useReadReplica?: boolean }) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args: any[]) {
      const startTime = Date.now()
      
      try {
        const result = await originalMethod.apply(this, args)
        
        const duration = Date.now() - startTime
        if (duration > defaultConfig.slowQueryThreshold) {
          console.warn(`[SLOW METHOD] ${target.constructor.name}.${propertyKey}: ${duration}ms`)
        }
        
        return result
      } catch (error) {
        console.error(`[METHOD ERROR] ${target.constructor.name}.${propertyKey}:`, error)
        throw error
      }
    }

    return descriptor
  }
}

// 常用查询优化助手
export const QueryHelpers = {
  // 分页查询优化
  async paginatedQuery<T>(
    model: string,
    args: any,
    page: number = 1,
    limit: number = 10
  ): Promise<{ data: T[]; total: number; page: number; totalPages: number }> {
    const skip = (page - 1) * limit

    const [data, total] = await Promise.all([
      queryOptimizer.optimizedQuery(model, 'findMany', {
        ...args,
        skip,
        take: limit
      }),
      queryOptimizer.optimizedQuery(model, 'count', {
        where: args.where
      })
    ])

    return {
      data,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    }
  },

  // 搜索查询优化
  async searchQuery<T>(
    model: string,
    searchFields: string[],
    searchTerm: string,
    additionalFilters: any = {}
  ): Promise<T[]> {
    const searchConditions = searchFields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive'
      }
    }))

    return queryOptimizer.optimizedQuery(model, 'findMany', {
      where: {
        AND: [
          { OR: searchConditions },
          additionalFilters
        ]
      }
    })
  },

  // 关联查询优化
  async findWithRelations<T>(
    model: string,
    id: string,
    relations: string[]
  ): Promise<T | null> {
    const include = relations.reduce((acc, relation) => {
      acc[relation] = true
      return acc
    }, {} as any)

    return queryOptimizer.optimizedQuery(model, 'findUnique', {
      where: { id },
      include
    })
  }
}

// 数据库索引建议
export const IndexSuggestions = {
  // 分析查询模式并提供索引建议
  analyzeQueries(): string[] {
    const suggestions: string[] = []
    
    // 分析常用查询字段
    const fieldUsage = new Map<string, number>()
    
    queryStats.forEach(stat => {
      // 简单的查询分析（实际实现需要更复杂的SQL解析）
      if (stat.query.includes('WHERE')) {
        const whereMatch = stat.query.match(/WHERE\s+(\w+)/i)
        if (whereMatch) {
          const field = whereMatch[1]
          fieldUsage.set(field, (fieldUsage.get(field) || 0) + 1)
        }
      }
    })

    // 生成索引建议
    fieldUsage.forEach((count, field) => {
      if (count > 10) { // 如果字段被频繁使用
        suggestions.push(`考虑为字段 ${field} 添加索引`)
      }
    })

    return suggestions
  }
}