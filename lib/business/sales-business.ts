import { PrismaClient, Prisma, OrderStatus, PaymentStatus, ShippingStatus } from '@prisma/client'
import { z } from 'zod'
import { Decimal } from '@prisma/client/runtime/library'

// 销售业务逻辑核心类
export class SalesBusiness {
  constructor(private prisma: PrismaClient) {}

  // 智能订单创建
  async createSalesOrder(data: {
    customerId: string
    items: Array<{
      productId: string
      quantity: number
      unitPrice?: number
      discount?: number
      notes?: string
    }>
    paymentMethod: 'CASH' | 'CARD' | 'TRANSFER' | 'WECHAT' | 'ALIPAY'
    deliveryAddress?: string
    expectedDeliveryDate?: Date
    notes?: string
    operatorId: string
  }): Promise<{
    success: boolean
    orderId?: string
    orderNumber?: string
    totalAmount?: number
    reservationIds: string[]
    warnings: string[]
    error?: string
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        // 验证客户信息
        const customer = await tx.customer.findUnique({
          where: { id: data.customerId },
          include: { creditLimit: true }
        })

        if (!customer) {
          return {
            success: false,
            reservationIds: [],
            warnings: [],
            error: '客户不存在'
          }
        }

        const warnings = []
        let totalAmount = 0
        const orderItems = []

        // 处理订单项目
        for (const item of data.items) {
          const product = await tx.product.findUnique({
            where: { id: item.productId },
            include: { category: true }
          })

          if (!product) {
            warnings.push(`产品 ${item.productId} 不存在`)
            continue
          }

          // 价格计算
          const unitPrice = item.unitPrice || product.price?.toNumber() || 0
          const discount = item.discount || 0
          const lineTotal = (unitPrice - discount) * item.quantity

          totalAmount += lineTotal

          orderItems.push({
            productId: item.productId,
            productName: product.name,
            quantity: item.quantity,
            unitPrice: unitPrice,
            discount: discount,
            lineTotal: lineTotal,
            notes: item.notes
          })

          // 检查库存可用性
          const availableInventory = await tx.inventoryItem.aggregate({
            where: { productId: item.productId },
            _sum: {
              quantity: true,
              reservedQuantity: true
            }
          })

          const totalStock = availableInventory._sum.quantity || 0
          const totalReserved = availableInventory._sum.reservedQuantity || 0
          const availableStock = totalStock - totalReserved

          if (availableStock < item.quantity) {
            warnings.push(`产品 ${product.name} 库存不足，需要 ${item.quantity}，可用 ${availableStock}`)
          }
        }

        // 检查客户信用额度
        if (customer.creditLimit && totalAmount > customer.creditLimit.toNumber()) {
          warnings.push(`订单金额 ${totalAmount} 超过客户信用额度 ${customer.creditLimit.toNumber()}`)
        }

        // 生成订单号
        const orderNumber = await this.generateOrderNumber(tx)

        // 创建销售订单
        const order = await tx.order.create({
          data: {
            orderNumber,
            customerId: data.customerId,
            totalAmount: new Decimal(totalAmount),
            paymentMethod: data.paymentMethod,
            deliveryAddress: data.deliveryAddress,
            expectedDeliveryDate: data.expectedDeliveryDate,
            notes: data.notes,
            status: OrderStatus.PENDING,
            paymentStatus: PaymentStatus.PENDING,
            shippingStatus: ShippingStatus.PENDING,
            operatorId: data.operatorId
          }
        })

        // 创建订单明细
        for (const item of orderItems) {
          await tx.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: new Decimal(item.unitPrice),
              discount: new Decimal(item.discount),
              lineTotal: new Decimal(item.lineTotal),
              notes: item.notes
            }
          })
        }

        // 预留库存
        const reservationIds: string[] = []
        for (const item of data.items) {
          const inventoryItems = await tx.inventoryItem.findMany({
            where: { productId: item.productId },
            orderBy: { updatedAt: 'asc' } // FIFO原则
          })

          let remainingQuantity = item.quantity
          for (const inventory of inventoryItems) {
            if (remainingQuantity <= 0) break

            const availableQty = inventory.quantity - inventory.reservedQuantity
            if (availableQty <= 0) continue

            const reserveQty = Math.min(remainingQuantity, availableQty)

            // 创建库存预留
            const reservation = await tx.inventoryReservation.create({
              data: {
                reference: order.orderNumber,
                type: 'SALES_ORDER',
                operatorId: data.operatorId,
                status: 'ACTIVE'
              }
            })

            await tx.inventoryReservationItem.create({
              data: {
                reservationId: reservation.id,
                productId: item.productId,
                warehouseId: inventory.warehouseId,
                quantity: reserveQty
              }
            })

            // 更新库存预留数量
            await tx.inventoryItem.update({
              where: { id: inventory.id },
              data: {
                reservedQuantity: { increment: reserveQty }
              }
            })

            reservationIds.push(reservation.id)
            remainingQuantity -= reserveQty
          }

          if (remainingQuantity > 0) {
            warnings.push(`产品 ${item.productId} 只能预留 ${item.quantity - remainingQuantity} 件，缺少 ${remainingQuantity} 件`)
          }
        }

        return {
          success: true,
          orderId: order.id,
          orderNumber: order.orderNumber,
          totalAmount,
          reservationIds,
          warnings
        }
      } catch (error) {
        console.error('创建销售订单失败:', error)
        return {
          success: false,
          reservationIds: [],
          warnings: [],
          error: '创建销售订单过程中发生错误'
        }
      }
    })
  }

  // 订单状态管理
  async updateOrderStatus(data: {
    orderId: string
    status: OrderStatus
    paymentStatus?: PaymentStatus
    shippingStatus?: ShippingStatus
    notes?: string
    operatorId: string
  }): Promise<{
    success: boolean
    order?: any
    nextActions: string[]
    error?: string
  }> {
    return await this.prisma.$transaction(async (tx) => {
      try {
        const currentOrder = await tx.order.findUnique({
          where: { id: data.orderId },
          include: {
            items: {
              include: { product: true }
            },
            customer: true
          }
        })

        if (!currentOrder) {
          return {
            success: false,
            nextActions: [],
            error: '订单不存在'
          }
        }

        // 更新订单状态
        const updatedOrder = await tx.order.update({
          where: { id: data.orderId },
          data: {
            status: data.status,
            paymentStatus: data.paymentStatus || currentOrder.paymentStatus,
            shippingStatus: data.shippingStatus || currentOrder.shippingStatus,
            notes: data.notes || currentOrder.notes,
            updatedAt: new Date()
          },
          include: {
            items: {
              include: { product: true }
            },
            customer: true
          }
        })

        // 创建状态变更记录
        await tx.orderStatusHistory.create({
          data: {
            orderId: data.orderId,
            previousStatus: currentOrder.status,
            newStatus: data.status,
            changeReason: data.notes || '状态更新',
            operatorId: data.operatorId
          }
        })

        const nextActions = []

        // 根据新状态确定后续行动
        switch (data.status) {
          case OrderStatus.CONFIRMED:
            nextActions.push('订单已确认，开始准备发货')
            if (updatedOrder.paymentStatus === PaymentStatus.PENDING) {
              nextActions.push('等待客户付款')
            }
            break
            
          case OrderStatus.PROCESSING:
            nextActions.push('订单处理中，准备配货')
            await this.processOrderFulfillment(tx, data.orderId)
            break
            
          case OrderStatus.SHIPPED:
            nextActions.push('订单已发货，更新物流信息')
            await this.updateInventoryAfterShipping(tx, data.orderId)
            break
            
          case OrderStatus.DELIVERED:
            nextActions.push('订单已送达，等待客户确认')
            break
            
          case OrderStatus.COMPLETED:
            nextActions.push('订单已完成')
            await this.finalizeOrder(tx, data.orderId)
            break
            
          case OrderStatus.CANCELLED:
            nextActions.push('订单已取消，释放库存预留')
            await this.releaseOrderReservations(tx, data.orderId)
            break
        }

        return {
          success: true,
          order: updatedOrder,
          nextActions
        }
      } catch (error) {
        console.error('更新订单状态失败:', error)
        return {
          success: false,
          nextActions: [],
          error: '更新订单状态过程中发生错误'
        }
      }
    })
  }

  // 自动定价策略
  async calculateOptimalPricing(data: {
    productId: string
    customerId?: string
    quantity: number
    season?: string
    competitorPrices?: number[]
  }): Promise<{
    suggestedPrice: number
    priceBreakdown: {
      basePrice: number
      volumeDiscount: number
      seasonalAdjustment: number
      customerDiscount: number
      competitiveAdjustment: number
      finalPrice: number
    }
    reasoning: string[]
    profitMargin: number
  }> {
    const product = await this.prisma.product.findUnique({
      where: { id: data.productId },
      include: {
        category: true,
        salesHistory: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!product) {
      throw new Error('产品不存在')
    }

    const basePrice = product.price?.toNumber() || 0
    const cost = product.cost?.toNumber() || 0
    const reasoning = []

    let volumeDiscount = 0
    let seasonalAdjustment = 0
    let customerDiscount = 0
    let competitiveAdjustment = 0

    // 批量折扣
    if (data.quantity >= 100) {
      volumeDiscount = basePrice * 0.15 // 15%批量折扣
      reasoning.push(`大批量订购 (${data.quantity}件) 享受15%折扣`)
    } else if (data.quantity >= 50) {
      volumeDiscount = basePrice * 0.10 // 10%批量折扣
      reasoning.push(`批量订购 (${data.quantity}件) 享受10%折扣`)
    } else if (data.quantity >= 20) {
      volumeDiscount = basePrice * 0.05 // 5%批量折扣
      reasoning.push(`批量订购 (${data.quantity}件) 享受5%折扣`)
    }

    // 季节性调整
    if (data.season) {
      const currentMonth = new Date().getMonth() + 1
      if (['春节', '情人节', '母亲节'].includes(data.season) || [1, 2, 5].includes(currentMonth)) {
        seasonalAdjustment = basePrice * 0.05 // 节日溢价5%
        reasoning.push(`${data.season}节日期间价格上调5%`)
      } else if (['淡季', '清仓'].includes(data.season) || [7, 8, 11].includes(currentMonth)) {
        seasonalAdjustment = -basePrice * 0.10 // 淡季折扣10%
        reasoning.push(`淡季销售享受10%折扣`)
      }
    }

    // 客户等级折扣
    if (data.customerId) {
      const customer = await this.prisma.customer.findUnique({
        where: { id: data.customerId },
        include: {
          orders: {
            where: { status: OrderStatus.COMPLETED },
            select: { totalAmount: true }
          }
        }
      })

      if (customer) {
        const totalPurchases = customer.orders.reduce(
          (sum, order) => sum + order.totalAmount.toNumber(), 0
        )

        if (totalPurchases > 100000) {
          customerDiscount = basePrice * 0.08 // VIP客户8%折扣
          reasoning.push('VIP客户享受8%专属折扣')
        } else if (totalPurchases > 50000) {
          customerDiscount = basePrice * 0.05 // 老客户5%折扣
          reasoning.push('老客户享受5%忠诚度折扣')
        } else if (totalPurchases > 10000) {
          customerDiscount = basePrice * 0.03 // 常客3%折扣
          reasoning.push('常客享受3%折扣')
        }
      }
    }

    // 竞争性定价调整
    if (data.competitorPrices && data.competitorPrices.length > 0) {
      const avgCompetitorPrice = data.competitorPrices.reduce((a, b) => a + b) / data.competitorPrices.length
      const priceDifference = basePrice - avgCompetitorPrice

      if (priceDifference > avgCompetitorPrice * 0.1) {
        competitiveAdjustment = -basePrice * 0.05 // 降价5%保持竞争力
        reasoning.push('为保持市场竞争力，价格下调5%')
      } else if (priceDifference < -avgCompetitorPrice * 0.1) {
        competitiveAdjustment = basePrice * 0.03 // 适度涨价3%
        reasoning.push('相比竞品有价格优势，适度上调3%')
      }
    }

    const finalPrice = Math.max(
      basePrice + seasonalAdjustment + competitiveAdjustment - volumeDiscount - customerDiscount,
      cost * 1.1 // 确保至少10%利润率
    )

    const profitMargin = cost > 0 ? ((finalPrice - cost) / finalPrice) * 100 : 0

    if (profitMargin < 10) {
      reasoning.push('⚠️ 警告：利润率低于10%，请谨慎定价')
    }

    return {
      suggestedPrice: Math.round(finalPrice * 100) / 100,
      priceBreakdown: {
        basePrice,
        volumeDiscount,
        seasonalAdjustment,
        customerDiscount,
        competitiveAdjustment,
        finalPrice
      },
      reasoning,
      profitMargin: Math.round(profitMargin * 100) / 100
    }
  }

  // 销售分析和预测
  async analyzeSalesPerformance(data: {
    dateRange?: {
      startDate: Date
      endDate: Date
    }
    productIds?: string[]
    customerIds?: string[]
    categoryIds?: string[]
  }): Promise<{
    summary: {
      totalOrders: number
      totalRevenue: number
      averageOrderValue: number
      conversionRate: number
      topSellingProducts: Array<{
        productId: string
        productName: string
        totalSold: number
        revenue: number
      }>
      topCustomers: Array<{
        customerId: string
        customerName: string
        totalOrders: number
        totalSpent: number
      }>
    }
    trends: {
      dailySales: Array<{
        date: Date
        orders: number
        revenue: number
      }>
      monthlyGrowth: number
      seasonalPatterns: Array<{
        month: number
        averageRevenue: number
        orderCount: number
      }>
    }
    forecasts: {
      nextMonthRevenue: number
      nextQuarterRevenue: number
      confidence: number
    }
  }> {
    const defaultEndDate = new Date()
    const defaultStartDate = new Date()
    defaultStartDate.setMonth(defaultEndDate.getMonth() - 3) // 默认查看3个月

    const startDate = data.dateRange?.startDate || defaultStartDate
    const endDate = data.dateRange?.endDate || defaultEndDate

    // 构建查询条件
    const whereClause: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      },
      status: { not: OrderStatus.CANCELLED }
    }

    if (data.customerIds?.length) {
      whereClause.customerId = { in: data.customerIds }
    }

    // 获取订单数据
    const orders = await this.prisma.order.findMany({
      where: whereClause,
      include: {
        items: {
          include: { product: true }
        },
        customer: true
      }
    })

    // 计算基本指标
    const totalOrders = orders.length
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount.toNumber(), 0)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    // 计算转化率（简化版本）
    const totalQuotes = await this.prisma.quote.count({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    })
    const conversionRate = totalQuotes > 0 ? (totalOrders / totalQuotes) * 100 : 0

    // 热销产品分析
    const productSales = new Map()
    orders.forEach(order => {
      order.items.forEach(item => {
        const productId = item.productId
        if (!productSales.has(productId)) {
          productSales.set(productId, {
            productId,
            productName: item.product.name,
            totalSold: 0,
            revenue: 0
          })
        }
        const stats = productSales.get(productId)
        stats.totalSold += item.quantity
        stats.revenue += item.lineTotal.toNumber()
      })
    })

    const topSellingProducts = Array.from(productSales.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10)

    // 顶级客户分析
    const customerSales = new Map()
    orders.forEach(order => {
      const customerId = order.customerId
      if (!customerSales.has(customerId)) {
        customerSales.set(customerId, {
          customerId,
          customerName: order.customer.name,
          totalOrders: 0,
          totalSpent: 0
        })
      }
      const stats = customerSales.get(customerId)
      stats.totalOrders++
      stats.totalSpent += order.totalAmount.toNumber()
    })

    const topCustomers = Array.from(customerSales.values())
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 10)

    // 日销售趋势
    const dailySalesMap = new Map()
    orders.forEach(order => {
      const dateKey = order.createdAt.toISOString().split('T')[0]
      if (!dailySalesMap.has(dateKey)) {
        dailySalesMap.set(dateKey, {
          date: new Date(dateKey),
          orders: 0,
          revenue: 0
        })
      }
      const dayStats = dailySalesMap.get(dateKey)
      dayStats.orders++
      dayStats.revenue += order.totalAmount.toNumber()
    })

    const dailySales = Array.from(dailySalesMap.values()).sort((a, b) => a.date.getTime() - b.date.getTime())

    // 月增长率
    const currentMonth = new Date()
    const lastMonth = new Date()
    lastMonth.setMonth(currentMonth.getMonth() - 1)

    const currentMonthRevenue = orders
      .filter(order => order.createdAt >= lastMonth)
      .reduce((sum, order) => sum + order.totalAmount.toNumber(), 0)

    const previousMonthStart = new Date(lastMonth)
    previousMonthStart.setMonth(lastMonth.getMonth() - 1)

    const previousMonthOrders = await this.prisma.order.findMany({
      where: {
        createdAt: {
          gte: previousMonthStart,
          lt: lastMonth
        },
        status: { not: OrderStatus.CANCELLED }
      }
    })

    const previousMonthRevenue = previousMonthOrders.reduce(
      (sum, order) => sum + order.totalAmount.toNumber(), 0
    )

    const monthlyGrowth = previousMonthRevenue > 0 
      ? ((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100 
      : 0

    // 季节性模式（简化版本）
    const seasonalPatterns = Array.from({ length: 12 }, (_, i) => ({
      month: i + 1,
      averageRevenue: totalRevenue / 3, // 简化计算
      orderCount: Math.round(totalOrders / 3)
    }))

    // 简单的线性预测
    const recentTrend = dailySales.slice(-30) // 最近30天
    const avgDailyRevenue = recentTrend.reduce((sum, day) => sum + day.revenue, 0) / recentTrend.length
    const nextMonthRevenue = avgDailyRevenue * 30
    const nextQuarterRevenue = avgDailyRevenue * 90

    return {
      summary: {
        totalOrders,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        conversionRate: Math.round(conversionRate * 100) / 100,
        topSellingProducts,
        topCustomers
      },
      trends: {
        dailySales,
        monthlyGrowth: Math.round(monthlyGrowth * 100) / 100,
        seasonalPatterns
      },
      forecasts: {
        nextMonthRevenue: Math.round(nextMonthRevenue * 100) / 100,
        nextQuarterRevenue: Math.round(nextQuarterRevenue * 100) / 100,
        confidence: 75 // 简化的置信度
      }
    }
  }

  // 辅助方法
  private async generateOrderNumber(tx: any): Promise<string> {
    const date = new Date()
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '')
    
    const dailyCount = await tx.order.count({
      where: {
        createdAt: {
          gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
          lt: new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
        }
      }
    })

    return `SO${dateStr}${String(dailyCount + 1).padStart(4, '0')}`
  }

  private async processOrderFulfillment(tx: any, orderId: string): Promise<void> {
    // 处理订单履行逻辑
    await tx.orderProcessingLog.create({
      data: {
        orderId,
        action: 'START_FULFILLMENT',
        notes: '开始订单履行处理',
        timestamp: new Date()
      }
    })
  }

  private async updateInventoryAfterShipping(tx: any, orderId: string): Promise<void> {
    // 发货后更新库存
    const order = await tx.order.findUnique({
      where: { id: orderId },
      include: { items: true }
    })

    if (order) {
      for (const item of order.items) {
        // 从预留转为实际出库
        await tx.inventoryTransaction.create({
          data: {
            type: 'SALE',
            productId: item.productId,
            quantity: -item.quantity,
            reason: `销售出库: ${order.orderNumber}`,
            operatorId: order.operatorId,
            referenceId: orderId,
            referenceType: 'SALES_ORDER'
          }
        })
      }
    }
  }

  private async finalizeOrder(tx: any, orderId: string): Promise<void> {
    // 完成订单的最终处理
    await tx.orderProcessingLog.create({
      data: {
        orderId,
        action: 'FINALIZE_ORDER',
        notes: '订单已完成',
        timestamp: new Date()
      }
    })
  }

  private async releaseOrderReservations(tx: any, orderId: string): Promise<void> {
    // 释放订单的库存预留
    const order = await tx.order.findUnique({
      where: { id: orderId }
    })

    if (order) {
      const reservations = await tx.inventoryReservation.findMany({
        where: {
          reference: order.orderNumber,
          status: 'ACTIVE'
        },
        include: { items: true }
      })

      for (const reservation of reservations) {
        for (const item of reservation.items) {
          await tx.inventoryItem.updateMany({
            where: {
              productId: item.productId,
              warehouseId: item.warehouseId
            },
            data: {
              reservedQuantity: { decrement: item.quantity }
            }
          })
        }

        await tx.inventoryReservation.update({
          where: { id: reservation.id },
          data: { status: 'RELEASED' }
        })
      }
    }
  }
}

// 销售业务逻辑验证规则
export const salesValidationSchemas = {
  createOrder: z.object({
    customerId: z.string().min(1, '客户ID不能为空'),
    items: z.array(z.object({
      productId: z.string().min(1, '产品ID不能为空'),
      quantity: z.number().min(1, '数量必须大于0'),
      unitPrice: z.number().optional(),
      discount: z.number().min(0, '折扣不能为负数').optional(),
      notes: z.string().optional()
    })).min(1, '订单项目不能为空'),
    paymentMethod: z.enum(['CASH', 'CARD', 'TRANSFER', 'WECHAT', 'ALIPAY']),
    deliveryAddress: z.string().optional(),
    expectedDeliveryDate: z.date().optional(),
    notes: z.string().optional(),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  updateOrderStatus: z.object({
    orderId: z.string().min(1, '订单ID不能为空'),
    status: z.enum(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'COMPLETED', 'CANCELLED']),
    paymentStatus: z.enum(['PENDING', 'PAID', 'PARTIALLY_PAID', 'REFUNDED']).optional(),
    shippingStatus: z.enum(['PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED']).optional(),
    notes: z.string().optional(),
    operatorId: z.string().min(1, '操作员ID不能为空')
  }),

  pricingCalculation: z.object({
    productId: z.string().min(1, '产品ID不能为空'),
    customerId: z.string().optional(),
    quantity: z.number().min(1, '数量必须大于0'),
    season: z.string().optional(),
    competitorPrices: z.array(z.number()).optional()
  }),

  salesAnalysis: z.object({
    dateRange: z.object({
      startDate: z.date(),
      endDate: z.date()
    }).optional(),
    productIds: z.array(z.string()).optional(),
    customerIds: z.array(z.string()).optional(),
    categoryIds: z.array(z.string()).optional()
  })
}