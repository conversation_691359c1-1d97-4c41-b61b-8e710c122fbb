"use client"

import React, { memo, useMemo, useCallback, useState } from 'react'
import { OptimizedProductCard } from './optimized-product-card'
import { Product, ProductCategory, ProductFilter } from "@/types/product"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  SearchIcon,
  PlusIcon,
  FilterIcon,
  LayoutGridIcon,
  ListIcon,
  RefreshCwIcon
} from "lucide-react"

interface OptimizedProductListProps {
  products: Product[]
  categories: ProductCategory[]
  filter: ProductFilter
  onFilterChange: (filter: Partial<ProductFilter>) => void
  onAddProduct: () => void
  onEditProduct: (product: Product) => void
  onDeleteProduct: (productId: number) => void
  onSelectionChange: (productIds: number[]) => void
  onDoubleClickEdit: (productId: number, field: string, value: string | number) => void
  isLoading?: boolean
}

export const OptimizedProductList = memo<OptimizedProductListProps>(({
  products,
  categories,
  filter,
  onFilterChange,
  onAddProduct,
  onEditProduct,
  onDeleteProduct,
  onSelectionChange,
  onDoubleClickEdit,
  isLoading = false
}) => {
  const [selectedProducts, setSelectedProducts] = useState<number[]>([])
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // 使用useMemo优化过滤和排序逻辑
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products

    // 搜索过滤
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query) ||
        product.categoryName?.toLowerCase().includes(query)
      )
    }

    // 分类过滤
    if (filter.categoryFilter && filter.categoryFilter !== 'all') {
      filtered = filtered.filter(product => 
        product.categoryId?.toString() === filter.categoryFilter
      )
    }

    // 库存过滤
    if (filter.inStockOnly) {
      filtered = filtered.filter(product => 
        product.inventory === null || product.inventory > 0
      )
    }

    // 排序
    if (filter.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any = a[filter.sortBy as keyof Product]
        let bValue: any = b[filter.sortBy as keyof Product]

        if (filter.sortBy === 'price') {
          aValue = Number(aValue) || 0
          bValue = Number(bValue) || 0
        }

        if (filter.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1
        }
        return aValue > bValue ? 1 : -1
      })
    }

    return filtered
  }, [products, filter])

  // 使用useMemo优化选中状态集合
  const selectedSet = useMemo(() => 
    new Set(selectedProducts), 
    [selectedProducts]
  )

  // 使用useCallback优化事件处理函数
  const handleSelectProduct = useCallback((productId: number, checked: boolean) => {
    const newSelection = checked
      ? [...selectedProducts, productId]
      : selectedProducts.filter(id => id !== productId)

    setSelectedProducts(newSelection)
    onSelectionChange(newSelection)
  }, [selectedProducts, onSelectionChange])

  const handleSelectAll = useCallback((checked: boolean) => {
    const newSelection = checked 
      ? filteredAndSortedProducts.map(p => p.id!).filter(id => id !== undefined)
      : []
    setSelectedProducts(newSelection)
    onSelectionChange(newSelection)
  }, [filteredAndSortedProducts, onSelectionChange])

  const handleSearchChange = useCallback((value: string) => {
    onFilterChange({ searchQuery: value })
  }, [onFilterChange])

  const handleCategoryChange = useCallback((value: string) => {
    onFilterChange({ categoryFilter: value === 'all' ? null : value })
  }, [onFilterChange])

  const handleImagePreview = useCallback((imageUrl: string) => {
    setPreviewImage(imageUrl)
  }, [])

  const handleClosePreview = useCallback(() => {
    setPreviewImage(null)
  }, [])

  // 计算统计信息
  const stats = useMemo(() => ({
    total: filteredAndSortedProducts.length,
    selected: selectedProducts.length,
    totalValue: filteredAndSortedProducts.reduce((sum, product) => sum + (product.price || 0), 0)
  }), [filteredAndSortedProducts, selectedProducts])

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          {/* 搜索框 */}
          <div className="relative flex-1 max-w-sm">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索产品..."
              value={filter.searchQuery || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* 分类过滤 */}
          <Select
            value={filter.categoryFilter || 'all'}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有分类</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id.toString()}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* 视图模式切换 */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <LayoutGridIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <ListIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button onClick={onAddProduct}>
            <PlusIcon className="h-4 w-4 mr-2" />
            添加产品
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center gap-4">
          <span>共 {stats.total} 个产品</span>
          {stats.selected > 0 && (
            <span>已选择 {stats.selected} 个</span>
          )}
          <span>总价值 ¥{stats.totalValue.toFixed(2)}</span>
        </div>
        
        {/* 全选 */}
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={stats.selected === stats.total && stats.total > 0}
            onCheckedChange={handleSelectAll}
          />
          <span>全选</span>
        </div>
      </div>

      {/* 产品网格/列表 */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <RefreshCwIcon className="h-8 w-8 animate-spin" />
        </div>
      ) : filteredAndSortedProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">没有找到匹配的产品</p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
            : "space-y-2"
        }>
          {filteredAndSortedProducts.map((product) => (
            <OptimizedProductCard
              key={product.id}
              product={product}
              isSelected={selectedSet.has(product.id!)}
              onSelect={handleSelectProduct}
              onEdit={onEditProduct}
              onDelete={onDeleteProduct}
              onImagePreview={handleImagePreview}
              onDoubleClickEdit={onDoubleClickEdit}
            />
          ))}
        </div>
      )}

      {/* 图片预览对话框 */}
      <Dialog open={!!previewImage} onOpenChange={handleClosePreview}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>图片预览</DialogTitle>
          </DialogHeader>
          {previewImage && (
            <div className="flex justify-center">
              <img
                src={previewImage}
                alt="产品图片"
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
})

OptimizedProductList.displayName = 'OptimizedProductList'