import { PrismaClient } from '@prisma/client'
import os from 'os'
import { EventEmitter } from 'events'

// 系统监控核心类
export class SystemMonitor extends EventEmitter {
  private intervalId: NodeJS.Timeout | null = null
  private metrics: SystemMetrics[] = []
  private alertRules: AlertRule[] = []
  private isMonitoring = false

  constructor(
    private prisma: PrismaClient,
    private config: MonitoringConfig = DEFAULT_MONITORING_CONFIG
  ) {
    super()
    this.setupDefaultAlertRules()
  }

  // 开始监控
  start(): void {
    if (this.isMonitoring) {
      console.warn('监控已经在运行')
      return
    }

    this.isMonitoring = true
    this.intervalId = setInterval(() => {
      this.collectMetrics()
    }, this.config.collectionInterval)

    console.log(`系统监控已启动，采集间隔: ${this.config.collectionInterval}ms`)
    this.emit('monitoring_started')
  }

  // 停止监控
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    this.isMonitoring = false
    console.log('系统监控已停止')
    this.emit('monitoring_stopped')
  }

  // 收集系统指标
  private async collectMetrics(): Promise<void> {
    try {
      const timestamp = new Date()
      const metrics: SystemMetrics = {
        timestamp,
        system: await this.getSystemMetrics(),
        database: await this.getDatabaseMetrics(),
        application: await this.getApplicationMetrics(),
        business: await this.getBusinessMetrics()
      }

      this.metrics.push(metrics)
      
      // 保持最近1000条记录
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000)
      }

      // 检查告警规则
      await this.checkAlerts(metrics)

      // 持久化关键指标
      if (this.config.persistMetrics) {
        await this.persistMetrics(metrics)
      }

      this.emit('metrics_collected', metrics)
    } catch (error) {
      console.error('采集监控数据失败:', error)
      this.emit('collection_error', error)
    }
  }

  // 获取系统指标
  private async getSystemMetrics(): Promise<SystemMetrics['system']> {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
        heap: {
          total: memoryUsage.heapTotal,
          used: memoryUsage.heapUsed,
          usage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
        },
        rss: memoryUsage.rss,
        external: memoryUsage.external
      },
      cpu: {
        usage: await this.getCpuUsage(),
        loadAverage: os.loadavg(),
        cores: os.cpus().length,
        model: os.cpus()[0]?.model || 'Unknown'
      },
      disk: await this.getDiskUsage(),
      network: await this.getNetworkStats(),
      uptime: {
        system: os.uptime(),
        process: process.uptime()
      }
    }
  }

  // 获取数据库指标
  private async getDatabaseMetrics(): Promise<SystemMetrics['database']> {
    try {
      const startTime = Date.now()
      
      // 测试数据库连接
      await this.prisma.$queryRaw`SELECT 1`
      const connectionTime = Date.now() - startTime

      // 获取数据库统计信息
      const tableStats = await this.getTableStats()
      const slowQueries = await this.getSlowQueries()

      return {
        connectionTime,
        isConnected: true,
        activeConnections: await this.getActiveConnections(),
        queryStats: {
          totalQueries: tableStats.totalQueries,
          slowQueries: slowQueries.length,
          averageQueryTime: tableStats.averageQueryTime
        },
        tableStats,
        performance: {
          transactionRate: await this.getTransactionRate(),
          lockWaitTime: await this.getLockWaitTime(),
          indexHitRatio: await this.getIndexHitRatio()
        }
      }
    } catch (error) {
      console.error('获取数据库指标失败:', error)
      return {
        connectionTime: -1,
        isConnected: false,
        activeConnections: 0,
        queryStats: {
          totalQueries: 0,
          slowQueries: 0,
          averageQueryTime: 0
        },
        tableStats: { totalQueries: 0, averageQueryTime: 0 },
        performance: {
          transactionRate: 0,
          lockWaitTime: 0,
          indexHitRatio: 0
        }
      }
    }
  }

  // 获取应用指标
  private async getApplicationMetrics(): Promise<SystemMetrics['application']> {
    return {
      requests: await this.getRequestStats(),
      errors: await this.getErrorStats(),
      responseTime: await this.getResponseTimeStats(),
      activeUsers: await this.getActiveUsers(),
      cache: await this.getCacheStats(),
      sessions: await this.getSessionStats()
    }
  }

  // 获取业务指标
  private async getBusinessMetrics(): Promise<SystemMetrics['business']> {
    try {
      const today = new Date()
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

      const [
        totalProducts,
        totalCustomers,
        totalEmployees,
        dailyOrders,
        dailyRevenue,
        lowStockCount,
        pendingOrders
      ] = await Promise.all([
        this.prisma.product.count(),
        this.prisma.customer.count(),
        this.prisma.employee.count(),
        this.prisma.order.count({
          where: {
            createdAt: { gte: yesterday }
          }
        }),
        this.prisma.order.aggregate({
          where: {
            createdAt: { gte: yesterday },
            status: { not: 'CANCELLED' }
          },
          _sum: { totalAmount: true }
        }),
        this.prisma.inventoryItem.count({
          where: {
            quantity: { lte: this.prisma.inventoryItem.fields.minStockLevel }
          }
        }),
        this.prisma.order.count({
          where: {
            status: { in: ['PENDING', 'PROCESSING'] }
          }
        })
      ])

      return {
        inventory: {
          totalProducts,
          lowStockCount,
          totalValue: await this.getTotalInventoryValue()
        },
        sales: {
          dailyOrders,
          dailyRevenue: dailyRevenue._sum.totalAmount?.toNumber() || 0,
          pendingOrders,
          conversionRate: await this.getConversionRate()
        },
        customers: {
          total: totalCustomers,
          active: await this.getActiveCustomers(),
          new: await this.getNewCustomers()
        },
        employees: {
          total: totalEmployees,
          active: await this.getActiveEmployees(),
          onLeave: await this.getEmployeesOnLeave()
        }
      }
    } catch (error) {
      console.error('获取业务指标失败:', error)
      return {
        inventory: { totalProducts: 0, lowStockCount: 0, totalValue: 0 },
        sales: { dailyOrders: 0, dailyRevenue: 0, pendingOrders: 0, conversionRate: 0 },
        customers: { total: 0, active: 0, new: 0 },
        employees: { total: 0, active: 0, onLeave: 0 }
      }
    }
  }

  // 检查告警规则
  private async checkAlerts(metrics: SystemMetrics): Promise<void> {
    for (const rule of this.alertRules) {
      if (rule.enabled && this.evaluateAlertRule(rule, metrics)) {
        const alert: Alert = {
          id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          rule: rule.name,
          severity: rule.severity,
          message: rule.message,
          timestamp: new Date(),
          metrics: this.extractRelevantMetrics(rule, metrics),
          resolved: false
        }

        await this.triggerAlert(alert)
      }
    }
  }

  // 触发告警
  private async triggerAlert(alert: Alert): Promise<void> {
    try {
      // 保存告警记录
      await this.prisma.systemAlert.create({
        data: {
          alertId: alert.id,
          rule: alert.rule,
          severity: alert.severity,
          message: alert.message,
          metadata: JSON.stringify(alert.metrics),
          resolved: false
        }
      })

      // 发送告警通知
      this.emit('alert', alert)
      
      console.warn(`🚨 告警触发: ${alert.rule} - ${alert.message}`)

      // 根据严重程度执行不同的通知策略
      if (alert.severity === 'critical') {
        await this.sendCriticalAlert(alert)
      } else if (alert.severity === 'high') {
        await this.sendHighPriorityAlert(alert)
      }
    } catch (error) {
      console.error('触发告警失败:', error)
    }
  }

  // 获取历史指标
  getHistoricalMetrics(
    startTime: Date,
    endTime: Date,
    interval: 'minute' | 'hour' | 'day' = 'hour'
  ): SystemMetrics[] {
    return this.metrics.filter(
      m => m.timestamp >= startTime && m.timestamp <= endTime
    )
  }

  // 获取实时状态
  getCurrentStatus(): SystemStatus {
    const latestMetrics = this.metrics[this.metrics.length - 1]
    
    if (!latestMetrics) {
      return {
        overall: 'unknown',
        system: 'unknown',
        database: 'unknown',
        application: 'unknown',
        business: 'unknown',
        lastUpdate: new Date()
      }
    }

    return {
      overall: this.calculateOverallStatus(latestMetrics),
      system: this.evaluateSystemStatus(latestMetrics.system),
      database: latestMetrics.database.isConnected ? 'healthy' : 'unhealthy',
      application: this.evaluateApplicationStatus(latestMetrics.application),
      business: this.evaluateBusinessStatus(latestMetrics.business),
      lastUpdate: latestMetrics.timestamp
    }
  }

  // 生成健康报告
  async generateHealthReport(): Promise<HealthReport> {
    const currentStatus = this.getCurrentStatus()
    const recentAlerts = await this.getRecentAlerts(24) // 最近24小时
    const performanceTrends = this.analyzePerformanceTrends()
    
    return {
      timestamp: new Date(),
      status: currentStatus,
      alerts: {
        total: recentAlerts.length,
        critical: recentAlerts.filter(a => a.severity === 'critical').length,
        high: recentAlerts.filter(a => a.severity === 'high').length,
        medium: recentAlerts.filter(a => a.severity === 'medium').length,
        low: recentAlerts.filter(a => a.severity === 'low').length
      },
      trends: performanceTrends,
      recommendations: this.generateRecommendations(currentStatus, recentAlerts, performanceTrends)
    }
  }

  // 辅助方法实现
  private setupDefaultAlertRules(): void {
    this.alertRules = [
      {
        name: 'high_memory_usage',
        enabled: true,
        severity: 'high',
        condition: 'memory.usage > 90',
        message: '内存使用率超过90%',
        threshold: 90,
        duration: 300000 // 5分钟
      },
      {
        name: 'high_cpu_usage',
        enabled: true,
        severity: 'high',
        condition: 'cpu.usage > 80',
        message: 'CPU使用率超过80%',
        threshold: 80,
        duration: 300000
      },
      {
        name: 'database_slow_response',
        enabled: true,
        severity: 'medium',
        condition: 'database.connectionTime > 5000',
        message: '数据库响应时间超过5秒',
        threshold: 5000,
        duration: 60000
      },
      {
        name: 'low_inventory_alert',
        enabled: true,
        severity: 'medium',
        condition: 'business.inventory.lowStockCount > 10',
        message: '低库存商品数量超过10个',
        threshold: 10,
        duration: 0
      }
    ]
  }

  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage()
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage)
        const totalUsage = endUsage.user + endUsage.system
        const usage = (totalUsage / 1000000) / 0.1 * 100 // 转换为百分比
        resolve(Math.min(usage, 100))
      }, 100)
    })
  }

  private async getDiskUsage(): Promise<any> {
    // 简化实现，实际应该使用文件系统API
    return {
      total: 1000000000000, // 1TB
      used: 500000000000,   // 500GB
      free: 500000000000,   // 500GB
      usage: 50
    }
  }

  private async getNetworkStats(): Promise<any> {
    // 简化实现
    return {
      bytesReceived: 0,
      bytesSent: 0,
      packetsReceived: 0,
      packetsSent: 0
    }
  }

  private async getTableStats(): Promise<any> {
    // 简化实现
    return {
      totalQueries: 1000,
      averageQueryTime: 50
    }
  }

  private async getSlowQueries(): Promise<any[]> {
    return []
  }

  private async getActiveConnections(): Promise<number> {
    return 5
  }

  private async getTransactionRate(): Promise<number> {
    return 100
  }

  private async getLockWaitTime(): Promise<number> {
    return 0
  }

  private async getIndexHitRatio(): Promise<number> {
    return 95
  }

  private async getRequestStats(): Promise<any> {
    return {
      total: 1000,
      success: 950,
      error: 50,
      rate: 10
    }
  }

  private async getErrorStats(): Promise<any> {
    return {
      total: 50,
      rate: 0.5,
      types: {}
    }
  }

  private async getResponseTimeStats(): Promise<any> {
    return {
      average: 200,
      p95: 500,
      p99: 1000
    }
  }

  private async getActiveUsers(): Promise<number> {
    return 25
  }

  private async getCacheStats(): Promise<any> {
    return {
      hitRate: 85,
      size: 1000000,
      evictions: 10
    }
  }

  private async getSessionStats(): Promise<any> {
    return {
      active: 25,
      total: 100
    }
  }

  private async getTotalInventoryValue(): Promise<number> {
    const result = await this.prisma.inventoryItem.aggregate({
      _sum: {
        quantity: true
      }
    })
    return result._sum.quantity || 0
  }

  private async getConversionRate(): Promise<number> {
    return 15.5
  }

  private async getActiveCustomers(): Promise<number> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    return await this.prisma.customer.count({
      where: {
        orders: {
          some: {
            createdAt: { gte: thirtyDaysAgo }
          }
        }
      }
    })
  }

  private async getNewCustomers(): Promise<number> {
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    return await this.prisma.customer.count({
      where: {
        createdAt: { gte: yesterday }
      }
    })
  }

  private async getActiveEmployees(): Promise<number> {
    return await this.prisma.employee.count({
      where: { status: 'ACTIVE' }
    })
  }

  private async getEmployeesOnLeave(): Promise<number> {
    return await this.prisma.employee.count({
      where: { status: 'ON_LEAVE' }
    })
  }

  private evaluateAlertRule(rule: AlertRule, metrics: SystemMetrics): boolean {
    // 简化的规则评估实现
    try {
      const condition = rule.condition
      if (condition.includes('memory.usage')) {
        return metrics.system.memory.usage > rule.threshold
      }
      if (condition.includes('cpu.usage')) {
        return metrics.system.cpu.usage > rule.threshold
      }
      if (condition.includes('database.connectionTime')) {
        return metrics.database.connectionTime > rule.threshold
      }
      if (condition.includes('business.inventory.lowStockCount')) {
        return metrics.business.inventory.lowStockCount > rule.threshold
      }
      return false
    } catch (error) {
      console.error('评估告警规则失败:', error)
      return false
    }
  }

  private extractRelevantMetrics(rule: AlertRule, metrics: SystemMetrics): any {
    // 提取与规则相关的指标
    return {
      timestamp: metrics.timestamp,
      rule: rule.name,
      value: 'extracted_value' // 简化实现
    }
  }

  private async sendCriticalAlert(alert: Alert): Promise<void> {
    // 发送紧急告警通知
    console.error(`🔥 CRITICAL ALERT: ${alert.message}`)
  }

  private async sendHighPriorityAlert(alert: Alert): Promise<void> {
    // 发送高优先级告警通知
    console.warn(`⚠️ HIGH PRIORITY ALERT: ${alert.message}`)
  }

  private async getRecentAlerts(hours: number): Promise<Alert[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000)
    const alerts = await this.prisma.systemAlert.findMany({
      where: {
        createdAt: { gte: since }
      }
    })

    return alerts.map(a => ({
      id: a.alertId,
      rule: a.rule,
      severity: a.severity as AlertSeverity,
      message: a.message,
      timestamp: a.createdAt,
      metrics: JSON.parse(a.metadata || '{}'),
      resolved: a.resolved
    }))
  }

  private calculateOverallStatus(metrics: SystemMetrics): HealthStatus {
    const systemHealth = this.evaluateSystemStatus(metrics.system)
    const dbHealth = metrics.database.isConnected ? 'healthy' : 'unhealthy'
    const appHealth = this.evaluateApplicationStatus(metrics.application)
    const businessHealth = this.evaluateBusinessStatus(metrics.business)

    const healthScores = [systemHealth, dbHealth, appHealth, businessHealth]
    
    if (healthScores.includes('unhealthy')) return 'unhealthy'
    if (healthScores.includes('degraded')) return 'degraded'
    return 'healthy'
  }

  private evaluateSystemStatus(system: SystemMetrics['system']): HealthStatus {
    if (system.memory.usage > 90 || system.cpu.usage > 90) return 'unhealthy'
    if (system.memory.usage > 80 || system.cpu.usage > 80) return 'degraded'
    return 'healthy'
  }

  private evaluateApplicationStatus(app: SystemMetrics['application']): HealthStatus {
    if (app.responseTime.average > 2000) return 'unhealthy'
    if (app.responseTime.average > 1000) return 'degraded'
    return 'healthy'
  }

  private evaluateBusinessStatus(business: SystemMetrics['business']): HealthStatus {
    if (business.inventory.lowStockCount > 20) return 'degraded'
    return 'healthy'
  }

  private analyzePerformanceTrends(): PerformanceTrends {
    // 简化的趋势分析
    return {
      memory: 'stable',
      cpu: 'stable',
      database: 'improving',
      responseTime: 'stable'
    }
  }

  private generateRecommendations(
    status: SystemStatus,
    alerts: Alert[],
    trends: PerformanceTrends
  ): string[] {
    const recommendations = []

    if (status.system === 'unhealthy') {
      recommendations.push('系统资源不足，建议升级硬件或优化应用程序')
    }

    if (status.database === 'unhealthy') {
      recommendations.push('数据库连接异常，请检查数据库服务状态')
    }

    if (alerts.filter(a => a.severity === 'critical').length > 0) {
      recommendations.push('存在严重告警，需要立即处理')
    }

    if (recommendations.length === 0) {
      recommendations.push('系统运行正常，继续保持监控')
    }

    return recommendations
  }

  private async persistMetrics(metrics: SystemMetrics): Promise<void> {
    try {
      await this.prisma.systemMetrics.create({
        data: {
          timestamp: metrics.timestamp,
          memoryUsage: metrics.system.memory.usage,
          cpuUsage: metrics.system.cpu.usage,
          diskUsage: metrics.system.disk.usage,
          dbConnectionTime: metrics.database.connectionTime,
          activeUsers: metrics.application.activeUsers,
          responseTime: metrics.application.responseTime.average,
          metadata: JSON.stringify(metrics)
        }
      })
    } catch (error) {
      console.error('持久化指标失败:', error)
    }
  }
}

// 类型定义
export interface SystemMetrics {
  timestamp: Date
  system: {
    memory: {
      total: number
      free: number
      used: number
      usage: number
      heap: {
        total: number
        used: number
        usage: number
      }
      rss: number
      external: number
    }
    cpu: {
      usage: number
      loadAverage: number[]
      cores: number
      model: string
    }
    disk: {
      total: number
      used: number
      free: number
      usage: number
    }
    network: {
      bytesReceived: number
      bytesSent: number
      packetsReceived: number
      packetsSent: number
    }
    uptime: {
      system: number
      process: number
    }
  }
  database: {
    connectionTime: number
    isConnected: boolean
    activeConnections: number
    queryStats: {
      totalQueries: number
      slowQueries: number
      averageQueryTime: number
    }
    tableStats: any
    performance: {
      transactionRate: number
      lockWaitTime: number
      indexHitRatio: number
    }
  }
  application: {
    requests: {
      total: number
      success: number
      error: number
      rate: number
    }
    errors: {
      total: number
      rate: number
      types: Record<string, number>
    }
    responseTime: {
      average: number
      p95: number
      p99: number
    }
    activeUsers: number
    cache: {
      hitRate: number
      size: number
      evictions: number
    }
    sessions: {
      active: number
      total: number
    }
  }
  business: {
    inventory: {
      totalProducts: number
      lowStockCount: number
      totalValue: number
    }
    sales: {
      dailyOrders: number
      dailyRevenue: number
      pendingOrders: number
      conversionRate: number
    }
    customers: {
      total: number
      active: number
      new: number
    }
    employees: {
      total: number
      active: number
      onLeave: number
    }
  }
}

export interface AlertRule {
  name: string
  enabled: boolean
  severity: AlertSeverity
  condition: string
  message: string
  threshold: number
  duration: number
}

export interface Alert {
  id: string
  rule: string
  severity: AlertSeverity
  message: string
  timestamp: Date
  metrics: any
  resolved: boolean
}

export type AlertSeverity = 'critical' | 'high' | 'medium' | 'low'
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy' | 'unknown'

export interface SystemStatus {
  overall: HealthStatus
  system: HealthStatus
  database: HealthStatus
  application: HealthStatus
  business: HealthStatus
  lastUpdate: Date
}

export interface HealthReport {
  timestamp: Date
  status: SystemStatus
  alerts: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
  }
  trends: PerformanceTrends
  recommendations: string[]
}

export interface PerformanceTrends {
  memory: 'improving' | 'stable' | 'degrading'
  cpu: 'improving' | 'stable' | 'degrading'
  database: 'improving' | 'stable' | 'degrading'
  responseTime: 'improving' | 'stable' | 'degrading'
}

export interface MonitoringConfig {
  collectionInterval: number
  persistMetrics: boolean
  alerting: {
    enabled: boolean
    channels: string[]
  }
  retention: {
    metrics: number // 天数
    alerts: number // 天数
  }
}

export const DEFAULT_MONITORING_CONFIG: MonitoringConfig = {
  collectionInterval: 30000, // 30秒
  persistMetrics: true,
  alerting: {
    enabled: true,
    channels: ['console', 'database']
  },
  retention: {
    metrics: 30, // 保留30天
    alerts: 90   // 保留90天
  }
}