/**
 * API响应优化中间件
 * 提供缓存、压缩、限流等功能
 */

import { NextRequest, NextResponse } from 'next/server'
import { apiCache } from '@/lib/cache/cache-instances'

export interface ApiOptimizationOptions {
  cache?: {
    enabled: boolean
    ttl?: number
    keyGenerator?: (request: NextRequest) => string
  }
  compression?: {
    enabled: boolean
    threshold?: number // 最小压缩大小（字节）
  }
  rateLimit?: {
    enabled: boolean
    maxRequests: number
    windowMs: number
  }
  cors?: {
    enabled: boolean
    origins?: string[]
  }
}

// 简单的内存限流器
class RateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>()

  isAllowed(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now()
    const record = this.requests.get(key)

    if (!record || now > record.resetTime) {
      this.requests.set(key, { count: 1, resetTime: now + windowMs })
      return true
    }

    if (record.count >= maxRequests) {
      return false
    }

    record.count++
    return true
  }

  cleanup() {
    const now = Date.now()
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key)
      }
    }
  }
}

const rateLimiter = new RateLimiter()

// 定期清理限流记录
setInterval(() => {
  rateLimiter.cleanup()
}, 60000) // 每分钟清理一次

/**
 * API优化装饰器
 */
export function withApiOptimization(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: ApiOptimizationOptions = {}
) {
  return async function optimizedHandler(request: NextRequest, ...args: any[]): Promise<NextResponse> {
    const startTime = Date.now()

    try {
      // 1. CORS处理
      if (options.cors?.enabled) {
        const origin = request.headers.get('origin')
        const allowedOrigins = options.cors.origins || ['*']
        
        if (origin && !allowedOrigins.includes('*') && !allowedOrigins.includes(origin)) {
          return new NextResponse('CORS not allowed', { status: 403 })
        }
      }

      // 2. 限流检查
      if (options.rateLimit?.enabled) {
        const clientIp = request.headers.get('x-forwarded-for') || 
                        request.headers.get('x-real-ip') || 
                        'unknown'
        
        const isAllowed = rateLimiter.isAllowed(
          clientIp,
          options.rateLimit.maxRequests,
          options.rateLimit.windowMs
        )

        if (!isAllowed) {
          return new NextResponse('Rate limit exceeded', { 
            status: 429,
            headers: {
              'Retry-After': Math.ceil(options.rateLimit.windowMs / 1000).toString()
            }
          })
        }
      }

      // 3. 缓存检查
      if (options.cache?.enabled && request.method === 'GET') {
        const cacheKey = options.cache.keyGenerator 
          ? options.cache.keyGenerator(request)
          : `${request.url}_${request.method}`

        const cached = apiCache.get(cacheKey)
        if (cached) {
          const response = NextResponse.json(cached)
          response.headers.set('X-Cache', 'HIT')
          response.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
          return response
        }
      }

      // 4. 执行原始处理程序
      const response = await handler(request, ...args)

      // 5. 缓存响应
      if (options.cache?.enabled && 
          request.method === 'GET' && 
          response.ok) {
        try {
          const cacheKey = options.cache.keyGenerator 
            ? options.cache.keyGenerator(request)
            : `${request.url}_${request.method}`

          const responseClone = response.clone()
          const data = await responseClone.json()
          
          apiCache.set(cacheKey, data, options.cache.ttl)
          response.headers.set('X-Cache', 'MISS')
        } catch (error) {
          // 忽略非JSON响应的缓存
          console.warn('Failed to cache response:', error)
        }
      }

      // 6. 添加性能头
      response.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)

      // 7. CORS头
      if (options.cors?.enabled) {
        const origin = request.headers.get('origin')
        const allowedOrigins = options.cors.origins || ['*']
        
        if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
          response.headers.set('Access-Control-Allow-Origin', origin || '*')
          response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
          response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        }
      }

      // 8. 压缩处理（Next.js自动处理，这里只是标记）
      if (options.compression?.enabled) {
        response.headers.set('X-Compression-Enabled', 'true')
      }

      return response

    } catch (error) {
      console.error('API optimization error:', error)
      
      // 返回错误响应
      const errorResponse = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
      
      errorResponse.headers.set('X-Response-Time', `${Date.now() - startTime}ms`)
      return errorResponse
    }
  }
}

/**
 * 预设的优化配置
 */
export const optimizationPresets = {
  // 高频读取API（如产品列表）
  highFrequencyRead: {
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000, // 5分钟
    },
    compression: {
      enabled: true,
      threshold: 1024, // 1KB
    },
    rateLimit: {
      enabled: true,
      maxRequests: 100,
      windowMs: 60 * 1000, // 1分钟
    },
    cors: {
      enabled: true,
    }
  },

  // 低频读取API（如统计数据）
  lowFrequencyRead: {
    cache: {
      enabled: true,
      ttl: 30 * 60 * 1000, // 30分钟
    },
    compression: {
      enabled: true,
      threshold: 512,
    },
    rateLimit: {
      enabled: true,
      maxRequests: 50,
      windowMs: 60 * 1000,
    },
    cors: {
      enabled: true,
    }
  },

  // 写入API（如创建、更新）
  writeOperation: {
    cache: {
      enabled: false, // 写入操作不缓存
    },
    compression: {
      enabled: true,
      threshold: 1024,
    },
    rateLimit: {
      enabled: true,
      maxRequests: 30,
      windowMs: 60 * 1000,
    },
    cors: {
      enabled: true,
    }
  },

  // 搜索API
  searchOperation: {
    cache: {
      enabled: true,
      ttl: 2 * 60 * 1000, // 2分钟
      keyGenerator: (request: NextRequest) => {
        const url = new URL(request.url)
        const query = url.searchParams.get('q') || ''
        return `search-${query}-${request.url}`
      }
    },
    compression: {
      enabled: true,
      threshold: 512,
    },
    rateLimit: {
      enabled: true,
      maxRequests: 60,
      windowMs: 60 * 1000,
    },
    cors: {
      enabled: true,
    }
  }
}

/**
 * 缓存失效工具
 */
export class CacheInvalidator {
  /**
   * 失效产品相关缓存
   */
  static invalidateProductCaches() {
    const keys = apiCache.keys()
    const productKeys = keys.filter(key => 
      key.includes('products') || 
      key.includes('product-') ||
      key.includes('search-')
    )
    
    productKeys.forEach(key => apiCache.delete(key))
  }

  /**
   * 失效分类相关缓存
   */
  static invalidateCategoryCaches() {
    const keys = apiCache.keys()
    const categoryKeys = keys.filter(key => 
      key.includes('categories') || 
      key.includes('category-')
    )
    
    categoryKeys.forEach(key => apiCache.delete(key))
  }

  /**
   * 失效用户相关缓存
   */
  static invalidateUserCaches(userId?: string) {
    const keys = apiCache.keys()
    const userKeys = keys.filter(key => 
      key.includes('users') || 
      key.includes('user-') ||
      (userId && key.includes(userId))
    )
    
    userKeys.forEach(key => apiCache.delete(key))
  }

  /**
   * 失效所有缓存
   */
  static invalidateAll() {
    apiCache.clear()
  }
}

/**
 * API性能监控
 */
export class ApiPerformanceMonitor {
  private static metrics = new Map<string, {
    totalRequests: number
    totalTime: number
    errors: number
    cacheHits: number
    cacheMisses: number
  }>()

  static recordRequest(endpoint: string, responseTime: number, isError: boolean, cacheHit: boolean) {
    const current = this.metrics.get(endpoint) || {
      totalRequests: 0,
      totalTime: 0,
      errors: 0,
      cacheHits: 0,
      cacheMisses: 0
    }

    current.totalRequests++
    current.totalTime += responseTime
    
    if (isError) current.errors++
    if (cacheHit) current.cacheHits++
    else current.cacheMisses++

    this.metrics.set(endpoint, current)
  }

  static getMetrics() {
    const result: Record<string, any> = {}
    
    for (const [endpoint, metrics] of this.metrics.entries()) {
      result[endpoint] = {
        ...metrics,
        averageResponseTime: metrics.totalRequests > 0 ? metrics.totalTime / metrics.totalRequests : 0,
        errorRate: metrics.totalRequests > 0 ? metrics.errors / metrics.totalRequests : 0,
        cacheHitRate: (metrics.cacheHits + metrics.cacheMisses) > 0 
          ? metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses) 
          : 0
      }
    }

    return result
  }

  static reset() {
    this.metrics.clear()
  }
}