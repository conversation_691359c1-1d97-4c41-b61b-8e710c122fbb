// 业务逻辑模块统一导出
export { InventoryBusiness, inventoryValidationSchemas } from './inventory-business'
export { FinanceBusiness, financeValidationSchemas } from './finance-business'
export { ProductionBusiness, productionValidationSchemas } from './production-business'
export { SalesBusiness, salesValidationSchemas } from './sales-business'
export { BusinessRulesEngine, dataConstraintSchemas } from './business-rules'

// 重新导出所有验证schemas
export const validationSchemas = {
  inventory: {
    transfer: inventoryValidationSchemas.transfer,
    stockTake: inventoryValidationSchemas.stockTake,
    reservation: inventoryValidationSchemas.reservation
  },
  finance: {
    reconciliation: financeValidationSchemas.reconciliation,
    budget: financeValidationSchemas.budget,
    cashFlowAnalysis: financeValidationSchemas.cashFlowAnalysis,
    financialReport: financeValidationSchemas.financialReport
  },
  production: {
    productionSchedule: productionValidationSchemas.productionSchedule,
    qualityCheck: productionValidationSchemas.qualityCheck
  },
  sales: {
    createOrder: salesValidationSchemas.createOrder,
    updateOrderStatus: salesValidationSchemas.updateOrderStatus,
    pricingCalculation: salesValidationSchemas.pricingCalculation,
    salesAnalysis: salesValidationSchemas.salesAnalysis
  },
  constraints: {
    product: dataConstraintSchemas.product,
    customer: dataConstraintSchemas.customer,
    employee: dataConstraintSchemas.employee,
    order: dataConstraintSchemas.order,
    inventory: dataConstraintSchemas.inventory,
    financialTransaction: dataConstraintSchemas.financialTransaction
  }
}

// 业务逻辑服务类型定义
export interface BusinessServices {
  inventory: InventoryBusiness
  finance: FinanceBusiness
  production: ProductionBusiness
  sales: SalesBusiness
  rules: BusinessRulesEngine
}

// 创建业务服务实例的工厂函数
import { PrismaClient } from '@prisma/client'

export function createBusinessServices(prisma: PrismaClient): BusinessServices {
  return {
    inventory: new InventoryBusiness(prisma),
    finance: new FinanceBusiness(prisma),
    production: new ProductionBusiness(prisma),
    sales: new SalesBusiness(prisma),
    rules: new BusinessRulesEngine(prisma)
  }
}

// 业务逻辑中间件
export class BusinessLogicMiddleware {
  constructor(private services: BusinessServices) {}

  // 通用业务逻辑验证中间件
  async validateBusinessLogic(
    entity: string,
    operation: 'create' | 'update' | 'delete',
    data: any,
    context?: any
  ) {
    const result = await this.services.rules.validateBusinessRules(
      entity,
      operation,
      data,
      context
    )

    if (!result.isValid) {
      const errors = result.violations
        .filter(v => v.severity === 'error')
        .map(v => v.message)
      
      throw new Error(`业务规则验证失败: ${errors.join(', ')}`)
    }

    return result
  }

  // 库存相关业务逻辑
  async processInventoryOperation(operation: string, data: any) {
    switch (operation) {
      case 'checkLowStock':
        return await this.services.inventory.checkLowStockAlerts(data.warehouseId)
      
      case 'transfer':
        await this.validateBusinessLogic('inventory', 'create', data)
        return await this.services.inventory.transferInventory(data)
      
      case 'stockTake':
        await this.validateBusinessLogic('inventory', 'update', data)
        return await this.services.inventory.performStockTaking(data)
      
      case 'generateRestockSuggestions':
        return await this.services.inventory.generateRestockSuggestions(data.warehouseId)
      
      case 'reserveInventory':
        await this.validateBusinessLogic('inventory', 'create', data)
        return await this.services.inventory.reserveInventory(data)
      
      case 'releaseReservation':
        return await this.services.inventory.releaseReservation(data.reservationId, data.operatorId)
      
      default:
        throw new Error(`不支持的库存操作: ${operation}`)
    }
  }

  // 财务相关业务逻辑
  async processFinanceOperation(operation: string, data: any) {
    switch (operation) {
      case 'reconciliation':
        await this.validateBusinessLogic('financial', 'create', data)
        return await this.services.finance.performReconciliation(data)
      
      case 'calculateProductCosts':
        return await this.services.finance.calculateProductCosts(data)
      
      case 'analyzeCashFlow':
        return await this.services.finance.analyzeCashFlow(data)
      
      case 'manageBudget':
        await this.validateBusinessLogic('financial', 'create', data)
        return await this.services.finance.manageBudget(data)
      
      case 'generateFinancialReports':
        return await this.services.finance.generateFinancialReports(data)
      
      default:
        throw new Error(`不支持的财务操作: ${operation}`)
    }
  }

  // 生产相关业务逻辑
  async processProductionOperation(operation: string, data: any) {
    switch (operation) {
      case 'createProductionSchedule':
        await this.validateBusinessLogic('production', 'create', data)
        return await this.services.production.createProductionSchedule(data)
      
      case 'performQualityCheck':
        return await this.services.production.performQualityCheck(data)
      
      case 'trackProductionCosts':
        return await this.services.production.trackProductionCosts(data.productionOrderId)
      
      case 'trackProductionProgress':
        return await this.services.production.trackProductionProgress(data.productionOrderId)
      
      default:
        throw new Error(`不支持的生产操作: ${operation}`)
    }
  }

  // 销售相关业务逻辑
  async processSalesOperation(operation: string, data: any) {
    switch (operation) {
      case 'createSalesOrder':
        await this.validateBusinessLogic('order', 'create', data)
        return await this.services.sales.createSalesOrder(data)
      
      case 'updateOrderStatus':
        await this.validateBusinessLogic('order', 'update', data)
        return await this.services.sales.updateOrderStatus(data)
      
      case 'calculateOptimalPricing':
        return await this.services.sales.calculateOptimalPricing(data)
      
      case 'analyzeSalesPerformance':
        return await this.services.sales.analyzeSalesPerformance(data)
      
      default:
        throw new Error(`不支持的销售操作: ${operation}`)
    }
  }

  // 综合业务分析
  async performComprehensiveAnalysis(dateRange?: { startDate: Date; endDate: Date }) {
    const [
      inventoryReport,
      salesAnalysis,
      cashFlowAnalysis,
      dataIntegrityCheck
    ] = await Promise.all([
      this.services.inventory.getInventoryReport(),
      this.services.sales.analyzeSalesPerformance({ dateRange }),
      this.services.finance.analyzeCashFlow({
        dateRange: dateRange || {
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          endDate: new Date()
        },
        granularity: 'daily'
      }),
      this.services.rules.validateDataIntegrity()
    ])

    return {
      inventory: {
        summary: inventoryReport,
        alerts: await this.services.inventory.checkLowStockAlerts(),
        suggestions: await this.services.inventory.generateRestockSuggestions()
      },
      sales: salesAnalysis,
      finance: {
        cashFlow: cashFlowAnalysis
      },
      dataQuality: dataIntegrityCheck,
      generatedAt: new Date(),
      recommendations: this.generateBusinessRecommendations({
        inventoryReport,
        salesAnalysis,
        cashFlowAnalysis,
        dataIntegrityCheck
      })
    }
  }

  // 生成业务建议
  private generateBusinessRecommendations(analysisData: any): string[] {
    const recommendations = []

    // 库存建议
    if (analysisData.inventoryReport.lowStockCount > 5) {
      recommendations.push('关注库存预警：多个产品库存偏低，建议及时补货')
    }

    if (analysisData.inventoryReport.outOfStockCount > 0) {
      recommendations.push('紧急处理：存在缺货产品，可能影响销售')
    }

    // 销售建议
    if (analysisData.salesAnalysis.trends.monthlyGrowth < 0) {
      recommendations.push('销售预警：月度销售额出现负增长，建议分析原因并制定对策')
    }

    if (analysisData.salesAnalysis.summary.conversionRate < 20) {
      recommendations.push('提升转化率：当前转化率较低，建议优化销售流程')
    }

    // 财务建议
    if (analysisData.cashFlowAnalysis.summary.netCashFlow < 0) {
      recommendations.push('现金流预警：净现金流为负，建议加强资金管理')
    }

    // 数据质量建议
    if (analysisData.dataIntegrityCheck.summary.criticalIssues > 0) {
      recommendations.push('数据质量问题：发现严重数据问题，建议立即修复')
    }

    // 默认建议
    if (recommendations.length === 0) {
      recommendations.push('系统运行正常，建议继续监控各项指标')
    }

    return recommendations
  }
}

// 业务逻辑常量和配置
export const BUSINESS_CONSTANTS = {
  INVENTORY: {
    LOW_STOCK_RATIO: 0.3,
    CRITICAL_STOCK_RATIO: 0.1,
    MAX_TRANSFER_QUANTITY: 9999,
    DEFAULT_WAREHOUSE_ID: 'default-warehouse'
  },
  FINANCE: {
    HIGH_VALUE_THRESHOLD: 100000,
    BUDGET_WARNING_THRESHOLD: 0.8,
    RECONCILIATION_TOLERANCE: 0.01,
    DEFAULT_CURRENCY: 'CNY'
  },
  PRODUCTION: {
    MAX_PRODUCTION_DAYS: 365,
    QUALITY_PASS_THRESHOLD: 0.95,
    DEFAULT_WORKING_HOURS: 8,
    URGENT_PRIORITY_THRESHOLD: 7
  },
  SALES: {
    MIN_PROFIT_MARGIN: 0.1,
    HIGH_VALUE_ORDER_THRESHOLD: 100000,
    MAX_CREDIT_UTILIZATION: 0.8,
    DEFAULT_DELIVERY_DAYS: 7
  }
}

// 错误类型定义
export class BusinessLogicError extends Error {
  constructor(
    message: string,
    public code: string,
    public entity?: string,
    public field?: string
  ) {
    super(message)
    this.name = 'BusinessLogicError'
  }
}

export class ValidationError extends BusinessLogicError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', undefined, field)
    this.name = 'ValidationError'
  }
}

export class BusinessRuleViolationError extends BusinessLogicError {
  constructor(message: string, rule: string, entity?: string) {
    super(message, 'BUSINESS_RULE_VIOLATION', entity)
    this.name = 'BusinessRuleViolationError'
  }
}