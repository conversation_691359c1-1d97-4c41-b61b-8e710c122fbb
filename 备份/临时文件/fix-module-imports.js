/**
 * 批量修复模块导入问题
 * 修复auth-middleware导入路径
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🔧 开始修复auth-middleware导入问题...')

// 查找所有包含 @/lib/auth-middleware 导入的文件
try {
  const result = execSync('grep -r "@/lib/auth-middleware" app/ --include="*.ts" --include="*.tsx"', { encoding: 'utf8' })
  const lines = result.trim().split('\n')

  const filesToFix = new Set()
  lines.forEach(line => {
    const filePath = line.split(':')[0]
    if (filePath) {
      filesToFix.add(filePath)
    }
  })

  console.log(`发现 ${filesToFix.size} 个文件需要修复`)

  filesToFix.forEach(filePath => {
    console.log(`修复文件: ${filePath}`)

    let content = fs.readFileSync(filePath, 'utf8')
    content = content.replace(
      /from ["']@\/lib\/auth-middleware["']/g,
      'from "@/lib/auth/auth-middleware"'
    )

    fs.writeFileSync(filePath, content, 'utf8')
    console.log(`✅ 已修复: ${filePath}`)
  })

  console.log('🎉 auth-middleware导入修复完成！')

} catch (error) {
  console.log('没有找到需要修复的文件或命令执行失败')
  console.log('手动修复已知文件...')

  // 手动修复已知的文件
  const knownFiles = [
    'app/api/permissions/route.ts',
    'app/api/permissions/[id]/route.ts',
    'app/api/permissions/check/route.ts',
    'app/api/roles/[id]/permissions/route.ts',
    'app/api/roles/[id]/route.ts',
    'app/api/roles/route.ts',
    'app/api/settings/config/route.ts'
  ]

  knownFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      console.log(`修复文件: ${filePath}`)

      let content = fs.readFileSync(filePath, 'utf8')
      if (content.includes('@/lib/auth-middleware')) {
        content = content.replace(
          /from ["']@\/lib\/auth-middleware["']/g,
          'from "@/lib/auth/auth-middleware"'
        )

        fs.writeFileSync(filePath, content, 'utf8')
        console.log(`✅ 已修复: ${filePath}`)
      }
    }
  })
}
