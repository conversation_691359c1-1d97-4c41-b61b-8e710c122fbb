const fs = require('fs')

// 读取文件内容
let content = fs.readFileSync('components/production/production-dashboard.tsx', 'utf8')

// 移除重复的错误代码块
content = content.replace(/\s*} else {\s*throw new Error\('Failed to fetch data'\)\s*}\s*}\s*} catch \(error\) {\s*console\.error\('Failed to load dashboard data:', error\)\s*\/\/ 作为最后的备选，使用默认数据\s*setData\(getDefaultDashboardData\(\)\)\s*} finally {\s*setIsLoading\(false\)\s*}\s*}/, '')

// 移除不需要的函数
content = content.replace(/\/\/ 基于真实订单数据计算仪表板数据[\s\S]*?(?=\s*if \(isLoading\))/m, '')

// 写回文件
fs.writeFileSync('components/production/production-dashboard.tsx', content)

console.log('✅ 语法错误已修复')
