# 宝塔面板一键自动部署指南

## 🚀 快速开始

### 前提条件
1. ✅ 已安装宝塔面板的服务器
2. ✅ 域名已解析到服务器IP
3. ✅ 代码已推送到Gitee仓库

### 一键部署命令

```bash
# 1. 下载自动部署脚本
wget https://gitee.com/your-username/linghua-enamel-gallery/raw/main/scripts/deploy-baota-auto.sh

# 2. 设置执行权限
chmod +x deploy-baota-auto.sh

# 3. 运行自动部署
./deploy-baota-auto.sh
```

### 部署过程

脚本会自动完成以下操作：

1. **环境检查** ✅
   - 检查宝塔面板状态
   - 验证系统环境

2. **Docker安装** 🐳
   - 自动安装Docker和Docker Compose
   - 配置国内镜像加速

3. **代码部署** 📦
   - 从Gitee克隆最新代码
   - 自动配置环境变量

4. **服务启动** 🔄
   - 启动PostgreSQL数据库
   - 启动Redis缓存
   - 启动Next.js应用
   - 启动Adminer管理工具

5. **网站配置** 🌐
   - 配置Nginx反向代理
   - 申请SSL证书
   - 启用HTTPS

6. **管理工具** 🛠️
   - 创建重启脚本
   - 创建备份脚本
   - 创建更新脚本

## 📋 部署配置

### 运行时需要提供的信息

| 配置项 | 说明 | 示例 |
|--------|------|------|
| Gitee仓库地址 | 项目的Gitee仓库URL | `https://gitee.com/username/linghua-enamel-gallery.git` |
| 域名 | 网站访问域名 | `linghua.example.com` |
| 部署目录 | 项目文件存放目录 | `/www/wwwroot/linghua-app` |

### 自动生成的配置

- **数据库密码**: 自动生成安全密码
- **Redis密码**: 自动生成安全密码
- **JWT密钥**: 自动生成随机密钥
- **SSL证书**: 自动申请Let's Encrypt证书

## 🔧 部署后管理

### 服务管理

```bash
# 查看服务状态
cd /www/wwwroot/linghua-app
docker-compose -f docker-compose.production.yml ps

# 重启所有服务
./restart-services.sh

# 查看服务日志
docker-compose -f docker-compose.production.yml logs -f
```

### 数据管理

```bash
# 备份数据
./backup-data.sh

# 查看备份文件
ls -la /www/backup/linghua/
```

### 代码更新

```bash
# 从Gitee更新代码
./update-from-gitee.sh
```

## 🌐 访问地址

部署完成后，您可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 主应用 | `https://your-domain.com` | 灵华珐琅馆管理系统 |
| 数据库管理 | `http://your-domain.com:8081` | Adminer数据库管理工具 |
| 宝塔面板 | `http://your-server-ip:8888` | 服务器管理面板 |

## 🔐 安全信息

### 数据库连接信息

```
服务器: localhost:5434
数据库: linghua_enamel_gallery_isolated
用户名: postgres
密码: [自动生成，部署完成后显示]
```

### 重要文件位置

```
项目文件: /www/wwwroot/linghua-app/
数据库数据: /www/server/data/postgres/
Redis数据: /www/server/data/redis/
备份文件: /www/backup/linghua/
SSL证书: /www/server/panel/vhost/cert/your-domain.com/
```

## 🛠️ 故障排除

### 常见问题

#### 1. 脚本执行失败
```bash
# 检查脚本权限
ls -la deploy-baota-auto.sh

# 重新设置权限
chmod +x deploy-baota-auto.sh
```

#### 2. Docker服务启动失败
```bash
# 查看Docker状态
systemctl status docker

# 重启Docker服务
systemctl restart docker
```

#### 3. 域名无法访问
```bash
# 检查Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx

# 检查防火墙
ufw status
```

#### 4. SSL证书申请失败
```bash
# 手动申请证书
certbot certonly --webroot -w /www/wwwroot/default -d your-domain.com

# 检查域名解析
nslookup your-domain.com
```

### 日志查看

```bash
# 查看应用日志
docker logs linghua-bt-app

# 查看数据库日志
docker logs linghua-bt-postgres

# 查看Nginx日志
tail -f /www/wwwlogs/your-domain.com.log
```

## 📞 技术支持

### 自助检查清单

- [ ] 宝塔面板正常运行
- [ ] 域名已正确解析
- [ ] 防火墙已开放必要端口 (80, 443, 8888)
- [ ] Gitee仓库可正常访问
- [ ] 服务器磁盘空间充足

### 联系方式

如果遇到问题，请：

1. 查看部署日志
2. 检查服务状态
3. 确认网络连接
4. 验证配置文件

---

## 🎯 一键部署优势

✅ **简单易用**: 一条命令完成所有部署  
✅ **自动化**: 无需手动配置复杂环境  
✅ **安全可靠**: 自动生成安全密码和证书  
✅ **完整功能**: 包含数据库、缓存、应用、管理工具  
✅ **易于维护**: 提供完整的管理脚本  
✅ **国内优化**: 使用Gitee和国内镜像源  

**开始部署，只需要3分钟！** 🚀
