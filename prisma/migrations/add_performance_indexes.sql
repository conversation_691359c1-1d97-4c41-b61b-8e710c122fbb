-- 性能优化索引
-- 为经常查询的字段添加索引

-- Product表索引
CREATE INDEX IF NOT EXISTS idx_product_category_id ON "Product"("categoryId");
CREATE INDEX IF NOT EXISTS idx_product_created_at ON "Product"("createdAt");
CREATE INDEX IF NOT EXISTS idx_product_name ON "Product"(name);
CREATE INDEX IF NOT EXISTS idx_product_type ON "Product"(type);
CREATE INDEX IF NOT EXISTS idx_product_price ON "Product"(price);
CREATE INDEX IF NOT EXISTS idx_product_inventory ON "Product"(inventory);

-- 复合索引用于常见查询组合
CREATE INDEX IF NOT EXISTS idx_product_category_created ON "Product"("categoryId", "createdAt");
CREATE INDEX IF NOT EXISTS idx_product_type_category ON "Product"(type, "categoryId");

-- ProductCategory表索引
CREATE INDEX IF NOT EXISTS idx_product_category_name ON "ProductCategory"(name);

-- ProductTagsOnProducts表索引
CREATE INDEX IF NOT EXISTS idx_product_tags_product_id ON "ProductTagsOnProducts"("productId");
CREATE INDEX IF NOT EXISTS idx_product_tags_tag_id ON "ProductTagsOnProducts"("tagId");

-- Order表索引
CREATE INDEX IF NOT EXISTS idx_order_status ON "Order"(status);
CREATE INDEX IF NOT EXISTS idx_order_created_at ON "Order"("createdAt");
CREATE INDEX IF NOT EXISTS idx_order_customer_id ON "Order"("customerId");
CREATE INDEX IF NOT EXISTS idx_order_employee_id ON "Order"("employeeId");

-- OrderItem表索引
CREATE INDEX IF NOT EXISTS idx_order_item_order_id ON "OrderItem"("orderId");
CREATE INDEX IF NOT EXISTS idx_order_item_product_id ON "OrderItem"("productId");

-- InventoryItem表索引
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON "InventoryItem"("productId");
CREATE INDEX IF NOT EXISTS idx_inventory_quantity ON "InventoryItem"(quantity);

-- InventoryTransaction表索引
CREATE INDEX IF NOT EXISTS idx_inventory_transaction_product_id ON "InventoryTransaction"("productId");
CREATE INDEX IF NOT EXISTS idx_inventory_transaction_created_at ON "InventoryTransaction"("createdAt");
CREATE INDEX IF NOT EXISTS idx_inventory_transaction_type ON "InventoryTransaction"(type);

-- User表索引
CREATE INDEX IF NOT EXISTS idx_user_email ON "User"(email);
CREATE INDEX IF NOT EXISTS idx_user_role ON "User"(role);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON "User"("createdAt");

-- Employee表索引
CREATE INDEX IF NOT EXISTS idx_employee_status ON "Employee"(status);
CREATE INDEX IF NOT EXISTS idx_employee_position ON "Employee"(position);

-- Customer表索引（如果存在）
CREATE INDEX IF NOT EXISTS idx_customer_phone ON "Customer"(phone);
CREATE INDEX IF NOT EXISTS idx_customer_created_at ON "Customer"("createdAt");

-- 全文搜索索引（PostgreSQL特有）
CREATE INDEX IF NOT EXISTS idx_product_name_gin ON "Product" USING gin(to_tsvector('simple', name));
CREATE INDEX IF NOT EXISTS idx_product_description_gin ON "Product" USING gin(to_tsvector('simple', COALESCE(description, '')));

-- 部分索引（只索引有效数据）
CREATE INDEX IF NOT EXISTS idx_product_active ON "Product"(id) WHERE type != 'category_placeholder' AND type != 'unit_placeholder' AND type != 'material_placeholder';
CREATE INDEX IF NOT EXISTS idx_product_with_inventory ON "Product"(id) WHERE inventory IS NOT NULL AND inventory > 0;

-- 统计信息更新
ANALYZE "Product";
ANALYZE "ProductCategory";
ANALYZE "ProductTagsOnProducts";
ANALYZE "Order";
ANALYZE "OrderItem";
ANALYZE "InventoryItem";
ANALYZE "InventoryTransaction";