# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development
```bash
# Start development server
npm run dev

# Start with Turbo (faster builds)
npm run dev:turbo

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix
```

### Database Operations
```bash
# Generate Prisma client
npm run db:generate

# Push schema changes to database
npm run db:push

# Open Prisma Studio
npm run db:studio

# Seed database
npm run db:seed
```

### Testing
```bash
# Run unit tests
npm run test
npm run test:watch
npm run test:coverage

# Run E2E tests
npm run test:e2e
npm run test:e2e:ui
```

### Build & Deployment
```bash
# Build for production
npm run build

# Build with bundle analysis
npm run build:analyze

# Start production server
npm run start
npm run start:prod

# Clean build artifacts
npm run clean
npm run clean:all
```

### Model Synchronization
```bash
# Check model synchronization
npm run check:model-sync

# Fix model synchronization
npm run fix:model-sync
```

## High-Level Architecture

### Tech Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js v4
- **UI Components**: Radix UI + Tailwind CSS
- **State Management**: React Server Components + Server Actions
- **Testing**: Vitest + Playwright

### Project Structure

#### App Router (app/)
- `(auth)/` - Authentication pages (login, logout, etc.)
- `(main)/` - Main application pages with authenticated layout
- `(mobile)/` - Mobile-optimized pages
- `(fullscreen)/` - Fullscreen layout pages
- `api/` - API routes organized by feature

#### Key Directories
- `components/` - React components organized by feature
- `lib/` - Utilities, actions, and configurations
- `lib/actions/` - Server Actions organized by domain
- `prisma/` - Database schema and migrations

### Database Architecture

The application uses a comprehensive PostgreSQL schema with 80+ models covering:

#### Core Business Entities
- **Products & Inventory**: Products, ProductCategories, ProductTags, InventoryItems, Warehouses
- **Sales & Orders**: Orders, OrderItems, PosSales, GallerySales, ChannelSales
- **Employees & Users**: User, Employee, Role, Permission (RBAC system)
- **Financial Management**: FinancialAccounts, FinancialTransactions, SalaryRecords
- **Production**: ProductionOrders, ProductionBases, QualityRecords
- **Workshops**: Workshop, WorkshopActivities, WorkshopTeamMembers

#### System Features
- **Audit Logging**: AuditLog, SystemLog for all operations
- **Workflow Engine**: Workflow, WorkflowInstance, WorkflowApproval
- **Personalization**: UserPreferences, DashboardLayout, UserFavorites
- **Data Management**: DataDictionary, SystemParameter, DataBackup

### Server Actions Pattern

Server Actions are organized by domain in `lib/actions/`:
- Each action file handles a specific business area
- Actions use Prisma for database operations
- Authentication and permission checks are enforced
- Actions return typed results with error handling

Example pattern:
```typescript
export async function createProduct(data: ProductCreateInput) {
  const session = await getSession()
  if (!session) throw new Error('Unauthorized')
  
  // Permission check
  await checkPermission('products.create')
  
  // Business logic
  const product = await prisma.product.create({ data })
  
  // Audit logging
  await createAuditLog('product', 'create', product.id)
  
  return product
}
```

### Authentication & Authorization

- **Authentication**: NextAuth.js with database sessions
- **Authorization**: Role-Based Access Control (RBAC)
- **Permission System**: Granular permissions per module/action
- **Middleware**: Route protection and permission checks

### API Design

API routes follow RESTful conventions:
- `GET /api/products` - List products
- `POST /api/products` - Create product
- `GET /api/products/[id]` - Get product
- `PATCH/PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

### Component Architecture

Components are organized by feature with consistent patterns:
- **UI Components**: Base components in `components/ui/`
- **Feature Components**: Business logic components by domain
- **Form Components**: Reusable form patterns with react-hook-form
- **Data Components**: Tables, lists with pagination and filtering

### Performance Optimizations

The application includes several performance optimizations:
- **Memory-optimized Next.js config** with chunk splitting
- **Image optimization** with WebP/AVIF formats
- **Code splitting** by route and feature
- **Caching strategies** for API responses
- **Database query optimization** with Prisma

### Development Guidelines

#### Server Actions
- Always validate user permissions
- Use TypeScript for type safety
- Include proper error handling
- Add audit logging for significant operations
- Follow the established pattern in existing actions

#### API Routes
- Implement proper HTTP status codes
- Use consistent error response format
- Include request validation
- Add rate limiting where appropriate

#### Database Operations
- Use Prisma transactions for multi-table operations
- Include proper indexes for query performance
- Follow the established schema patterns
- Use database migrations for schema changes

#### Component Development
- Use Server Components where possible
- Implement proper loading states
- Add error boundaries for error handling
- Follow the established styling patterns with Tailwind

### Key Configuration Files

- `next.config.mjs` - Next.js configuration with memory optimization
- `prisma/schema.prisma` - Database schema and models
- `auth.config.ts` & `auth.ts` - Authentication configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `package.json` - Dependencies and scripts

### Testing Strategy

- **Unit Tests**: Component and utility testing with Vitest
- **Integration Tests**: API route testing
- **E2E Tests**: User workflow testing with Playwright
- **Type Safety**: TypeScript compilation checks

This enterprise management system handles complex business workflows for a cloisonné enamel workshop, including production management, sales tracking, employee management, and financial operations.