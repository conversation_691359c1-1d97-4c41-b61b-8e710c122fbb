import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('channelId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const skip = (page - 1) * limit;

    const where: any = {};

    if (channelId) {
      where.channelId = parseInt(channelId);
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.settlementDate = {};
      if (startDate) {
        where.settlementDate.gte = new Date(startDate);
      }
      if (endDate) {
        where.settlementDate.lte = new Date(endDate);
      }
    }

    const [settlements, total] = await Promise.all([
      prisma.channelSettlement.findMany({
        where,
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: {
          settlementDate: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.channelSettlement.count({ where }),
    ]);

    // 计算统计数据
    const stats = await prisma.channelSettlement.aggregate({
      where,
      _sum: {
        settlementAmount: true,
        commissionAmount: true,
        adjustmentAmount: true,
      },
      _count: true,
    });

    return NextResponse.json({
      data: settlements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats: {
        totalSettlements: stats._count,
        totalSettlementAmount: stats._sum.settlementAmount || 0,
        totalCommissionAmount: stats._sum.commissionAmount || 0,
        totalAdjustmentAmount: stats._sum.adjustmentAmount || 0,
      },
    });
  } catch (error) {
    console.error('Error fetching channel settlements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch channel settlements' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // 验证必填字段
    if (!data.channelId || !data.settlementDate || data.settlementAmount === undefined) {
      return NextResponse.json(
        { error: '渠道、结算日期和结算金额为必填项' },
        { status: 400 }
      );
    }

    // 检查渠道是否存在
    const channel = await prisma.channel.findUnique({
      where: { id: parseInt(data.channelId) },
    });

    if (!channel) {
      return NextResponse.json(
        { error: '渠道不存在' },
        { status: 404 }
      );
    }

    // 生成结算单号
    const settlementNumber = data.settlementNumber || `SET-${Date.now()}`;

    // 检查结算单号是否已存在
    if (data.settlementNumber) {
      const existingSettlement = await prisma.channelSettlement.findUnique({
        where: { settlementNumber: data.settlementNumber },
      });

      if (existingSettlement) {
        return NextResponse.json(
          { error: '结算单号已存在' },
          { status: 400 }
        );
      }
    }

    const settlement = await prisma.channelSettlement.create({
      data: {
        channelId: parseInt(data.channelId),
        settlementNumber,
        settlementDate: new Date(data.settlementDate),
        startDate: data.startDate ? new Date(data.startDate) : undefined,
        endDate: data.endDate ? new Date(data.endDate) : undefined,
        settlementAmount: parseFloat(data.settlementAmount),
        commissionAmount: data.commissionAmount ? parseFloat(data.commissionAmount) : 0,
        adjustmentAmount: data.adjustmentAmount ? parseFloat(data.adjustmentAmount) : 0,
        adjustmentReason: data.adjustmentReason,
        status: data.status || 'PENDING',
        paymentMethod: data.paymentMethod,
        bankAccount: data.bankAccount,
        notes: data.notes,
      },
      include: {
        channel: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json(settlement, { status: 201 });
  } catch (error) {
    console.error('Error creating channel settlement:', error);
    return NextResponse.json(
      { error: 'Failed to create channel settlement' },
      { status: 500 }
    );
  }
}

// 自动计算结算金额
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    if (!data.channelId || !data.startDate || !data.endDate) {
      return NextResponse.json(
        { error: '渠道、开始日期和结束日期为必填项' },
        { status: 400 }
      );
    }

    const channelId = parseInt(data.channelId);
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);

    // 检查渠道是否存在
    const channel = await prisma.channel.findUnique({
      where: { id: channelId },
    });

    if (!channel) {
      return NextResponse.json(
        { error: '渠道不存在' },
        { status: 404 }
      );
    }

    // 计算期间内的销售数据
    const salesData = await prisma.channelSale.aggregate({
      where: {
        channelId,
        saleDate: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          in: ['COMPLETED', 'SHIPPED'],
        },
      },
      _sum: {
        totalAmount: true,
        commissionAmount: true,
      },
      _count: true,
    });

    // 检查是否有已存在的结算记录
    const existingSettlement = await prisma.channelSettlement.findFirst({
      where: {
        channelId,
        startDate: {
          lte: endDate,
        },
        endDate: {
          gte: startDate,
        },
      },
    });

    if (existingSettlement) {
      return NextResponse.json(
        { error: '该时间段已存在结算记录' },
        { status: 400 }
      );
    }

    const calculatedSettlement = {
      channelId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      totalSales: salesData._count,
      totalSalesAmount: salesData._sum.totalAmount || 0,
      totalCommissionAmount: salesData._sum.commissionAmount || 0,
      suggestedSettlementAmount: (salesData._sum.totalAmount || 0) - (salesData._sum.commissionAmount || 0),
    };

    return NextResponse.json(calculatedSettlement);
  } catch (error) {
    console.error('Error calculating settlement:', error);
    return NextResponse.json(
      { error: 'Failed to calculate settlement' },
      { status: 500 }
    );
  }
}