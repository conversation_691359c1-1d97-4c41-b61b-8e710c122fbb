/**
 * 统一薪酬计算引擎
 * 整合所有薪酬相关的计算逻辑，提供一致的API接口
 */

import prisma from "@/lib/db"
import { format, eachDayOfInterval, isWeekend, isWithinInterval, startOfMonth, endOfMonth } from "date-fns"

export interface SalaryCalculationInput {
  employeeId: number
  year: number
  month: number
  includeDetails?: boolean
}

export interface SalaryComponent {
  name: string
  amount: number
  rate?: number
  count?: number
  unit?: string
  description?: string
}

export interface SalaryCalculationResult {
  employeeId: number
  employeeName: string
  employeePosition: string
  year: number
  month: number
  
  // 主要薪酬组成
  baseSalary: number           // 基本工资
  attendanceSalary: number     // 出勤工资
  salesCommission: number      // 销售提成
  pieceWorkIncome: number      // 计件收入
  workshopIncome: number       // 工作坊收入
  coffeeShopCommission: number // 咖啡店提成
  overtimePay: number          // 加班费
  bonus: number                // 奖金
  allowances: number           // 津贴
  
  // 扣除项
  deductions: number           // 扣款
  socialInsurance: number      // 社保
  tax: number                  // 个税
  otherDeductions: number      // 其他扣除
  
  // 合计
  totalIncome: number          // 应发工资
  totalDeductions: number      // 总扣除
  netIncome: number            // 实发工资
  
  // 详细信息（可选）
  details?: {
    components: SalaryComponent[]
    deductionComponents: SalaryComponent[]
    attendanceDetails: {
      scheduledDays: number
      actualDays: number
      weekendDays: number
      overtimeDays: number
    }
    salesDetails: {
      gallerySales: number
      posSales: number
      channelSales: number
      coffeeShopSales: number
    }
    pieceWorkDetails: {
      accessoryWork: number
      enamellingWork: number
      otherWork: number
    }
    workshopDetails: {
      teacherSessions: number
      assistantSessions: number
    }
  }
}

export class SalaryCalculationEngine {
  constructor(private db = prisma) {}

  /**
   * 计算员工月度薪酬
   */
  async calculateMonthlySalary(input: SalaryCalculationInput): Promise<SalaryCalculationResult> {
    const { employeeId, year, month, includeDetails = false } = input

    // 获取员工信息
    const employee = await this.db.employee.findUnique({
      where: { id: employeeId },
      include: {
        user: {
          select: { id: true, name: true }
        }
      }
    })

    if (!employee) {
      throw new Error(`员工ID ${employeeId} 不存在`)
    }

    // 计算月份的开始和结束日期
    const startDate = startOfMonth(new Date(year, month - 1))
    const endDate = endOfMonth(new Date(year, month - 1))

    // 获取系统设置（模拟）
    const settings = await this.getSystemSettings()

    // 计算各项薪酬组件
    const baseSalary = await this.calculateBaseSalary(employee, settings)
    const attendanceResult = await this.calculateAttendanceSalary(employeeId, startDate, endDate, employee, settings)
    const salesResult = await this.calculateSalesCommission(employeeId, startDate, endDate, settings)
    const pieceWorkResult = await this.calculatePieceWorkIncome(employeeId, startDate, endDate)
    const workshopResult = await this.calculateWorkshopIncome(employeeId, startDate, endDate, settings)
    const coffeeShopResult = await this.calculateCoffeeShopCommission(employeeId, startDate, endDate, settings)
    
    // 计算加班费、奖金、津贴等
    const overtimePay = attendanceResult.overtimePay
    const bonus = 0 // 需要从其他系统获取
    const allowances = 0 // 需要从其他系统获取

    // 计算总收入
    const totalIncome = baseSalary + attendanceResult.salary + salesResult.total + 
                       pieceWorkResult.total + workshopResult.total + coffeeShopResult.total + 
                       overtimePay + bonus + allowances

    // 计算扣除项
    const deductionResult = await this.calculateDeductions(totalIncome, settings)

    const result: SalaryCalculationResult = {
      employeeId,
      employeeName: employee.name,
      employeePosition: employee.position || '未设置',
      year,
      month,
      baseSalary,
      attendanceSalary: attendanceResult.salary,
      salesCommission: salesResult.total,
      pieceWorkIncome: pieceWorkResult.total,
      workshopIncome: workshopResult.total,
      coffeeShopCommission: coffeeShopResult.total,
      overtimePay,
      bonus,
      allowances,
      deductions: deductionResult.deductions,
      socialInsurance: deductionResult.socialInsurance,
      tax: deductionResult.tax,
      otherDeductions: deductionResult.otherDeductions,
      totalIncome,
      totalDeductions: deductionResult.total,
      netIncome: totalIncome - deductionResult.total
    }

    // 如果需要详细信息
    if (includeDetails) {
      result.details = {
        components: [
          { name: '基本工资', amount: baseSalary, description: '固定基础工资' },
          { name: '出勤工资', amount: attendanceResult.salary, count: attendanceResult.details.actualDays, unit: '天' },
          { name: '销售提成', amount: salesResult.total, rate: settings.salesCommissionRate, unit: '%' },
          { name: '计件收入', amount: pieceWorkResult.total, count: pieceWorkResult.details.totalPieces, unit: '件' },
          { name: '工作坊收入', amount: workshopResult.total, count: workshopResult.details.totalSessions, unit: '场' },
          { name: '咖啡店提成', amount: coffeeShopResult.total, rate: settings.coffeeCommissionRate, unit: '%' },
          { name: '加班费', amount: overtimePay, count: attendanceResult.details.overtimeDays, unit: '天' }
        ],
        deductionComponents: [
          { name: '社会保险', amount: deductionResult.socialInsurance, rate: settings.socialInsuranceRate, unit: '%' },
          { name: '个人所得税', amount: deductionResult.tax, rate: settings.taxRate, unit: '%' },
          { name: '其他扣除', amount: deductionResult.otherDeductions }
        ],
        attendanceDetails: attendanceResult.details,
        salesDetails: salesResult.details,
        pieceWorkDetails: pieceWorkResult.details,
        workshopDetails: workshopResult.details
      }
    }

    return result
  }

  /**
   * 批量计算多个员工的薪酬
   */
  async calculateBatchSalary(inputs: SalaryCalculationInput[]): Promise<SalaryCalculationResult[]> {
    const results = await Promise.all(
      inputs.map(input => this.calculateMonthlySalary(input))
    )
    return results
  }

  /**
   * 计算基本工资
   */
  private async calculateBaseSalary(employee: any, settings: any): Promise<number> {
    return employee.baseSalary || settings.defaultBaseSalary || 0
  }

  /**
   * 计算出勤工资
   */
  private async calculateAttendanceSalary(
    employeeId: number, 
    startDate: Date, 
    endDate: Date, 
    employee: any, 
    settings: any
  ) {
    const schedules = await this.db.schedule.findMany({
      where: {
        employeeId,
        date: { gte: startDate, lte: endDate }
      }
    })

    let regularDays = 0
    let weekendDays = 0
    let overtimeDays = 0

    schedules.forEach(schedule => {
      const scheduleDate = new Date(schedule.date)
      if (isWeekend(scheduleDate)) {
        weekendDays++
      } else {
        regularDays++
      }
    })

    overtimeDays = weekendDays // 简化：周末工作视为加班

    const dailySalary = employee.dailySalary || settings.defaultDailySalary || 0
    const regularSalary = regularDays * dailySalary
    const weekendSalary = weekendDays * dailySalary * (settings.weekendOvertimeRate || 1.5)
    const overtimePay = weekendSalary - (weekendDays * dailySalary)

    return {
      salary: regularSalary + weekendSalary,
      overtimePay,
      details: {
        scheduledDays: schedules.length,
        actualDays: regularDays + weekendDays,
        weekendDays,
        overtimeDays
      }
    }
  }

  /**
   * 计算销售提成
   */
  private async calculateSalesCommission(
    employeeId: number, 
    startDate: Date, 
    endDate: Date, 
    settings: any
  ) {
    const [gallerySales, posSales, channelSales] = await Promise.all([
      this.db.gallerySale.findMany({
        where: { employeeId, date: { gte: startDate, lte: endDate } }
      }),
      this.db.posSale.findMany({
        where: { employeeId, date: { gte: startDate, lte: endDate } }
      }),
      this.db.channelSale.findMany({
        where: { 
          createdAt: { gte: startDate, lte: endDate }
          // 注意：这里需要根据实际业务逻辑调整员工关联
        }
      })
    ])

    const galleryTotal = gallerySales.reduce((sum, sale) => sum + sale.totalAmount, 0)
    const posTotal = posSales.reduce((sum, sale) => sum + sale.totalAmount, 0)
    const channelTotal = 0 // 渠道销售提成需要特殊计算

    const galleryCommission = galleryTotal * (settings.gallerySalesCommissionRate || 0) / 100
    const posCommission = posTotal * (settings.posSalesCommissionRate || 0) / 100
    const channelCommission = channelTotal * (settings.channelSalesCommissionRate || 0) / 100

    return {
      total: galleryCommission + posCommission + channelCommission,
      details: {
        gallerySales: galleryTotal,
        posSales: posTotal,
        channelSales: channelTotal,
        coffeeShopSales: 0
      }
    }
  }

  /**
   * 计算计件收入
   */
  private async calculatePieceWorkIncome(
    employeeId: number, 
    startDate: Date, 
    endDate: Date
  ) {
    const pieceWorks = await this.db.pieceWork.findMany({
      where: {
        employeeId,
        date: { gte: startDate, lte: endDate }
      }
    })

    let accessoryWork = 0
    let enamellingWork = 0
    let otherWork = 0

    pieceWorks.forEach(work => {
      switch (work.workType) {
        case 'accessory':
          accessoryWork += work.totalAmount
          break
        case 'enamelling':
          enamellingWork += work.totalAmount
          break
        default:
          otherWork += work.totalAmount
      }
    })

    const totalPieces = pieceWorks.reduce((sum, work) => sum + (work.pieceCount || 0), 0)

    return {
      total: accessoryWork + enamellingWork + otherWork,
      details: {
        accessoryWork,
        enamellingWork,
        otherWork,
        totalPieces
      }
    }
  }

  /**
   * 计算工作坊收入
   */
  private async calculateWorkshopIncome(
    employeeId: number, 
    startDate: Date, 
    endDate: Date, 
    settings: any
  ) {
    const [teacherWorkshops, assistantWorkshops] = await Promise.all([
      this.db.workshop.findMany({
        where: {
          teacherId: employeeId,
          date: { gte: startDate, lte: endDate }
        }
      }),
      this.db.workshop.findMany({
        where: {
          assistantId: employeeId,
          date: { gte: startDate, lte: endDate }
        }
      })
    ])

    const teacherIncome = teacherWorkshops.length * (settings.teacherWorkshopFee || 0)
    const assistantIncome = assistantWorkshops.length * (settings.assistantWorkshopFee || 0)

    return {
      total: teacherIncome + assistantIncome,
      details: {
        teacherSessions: teacherWorkshops.length,
        assistantSessions: assistantWorkshops.length,
        totalSessions: teacherWorkshops.length + assistantWorkshops.length
      }
    }
  }

  /**
   * 计算咖啡店提成
   */
  private async calculateCoffeeShopCommission(
    employeeId: number, 
    startDate: Date, 
    endDate: Date, 
    settings: any
  ) {
    const coffeeShopSales = await this.db.coffeeShopSale.findMany({
      where: {
        employeeId,
        date: { gte: startDate, lte: endDate }
      }
    })

    const totalSales = coffeeShopSales.reduce((sum, sale) => sum + sale.totalSales, 0)
    const commission = totalSales * (settings.coffeeSalesCommissionRate || 0) / 100

    return {
      total: commission,
      details: {
        totalSales,
        salesCount: coffeeShopSales.length
      }
    }
  }

  /**
   * 计算扣除项
   */
  private async calculateDeductions(totalIncome: number, settings: any) {
    const socialInsurance = totalIncome * (settings.socialInsuranceRate || 0) / 100
    const tax = this.calculatePersonalTax(totalIncome, settings)
    const deductions = 0 // 其他扣款
    const otherDeductions = 0

    return {
      socialInsurance,
      tax,
      deductions,
      otherDeductions,
      total: socialInsurance + tax + deductions + otherDeductions
    }
  }

  /**
   * 计算个人所得税（简化版）
   */
  private calculatePersonalTax(income: number, settings: any): number {
    // 简化的个税计算，实际应该按照个税法计算
    const threshold = settings.taxThreshold || 5000
    if (income <= threshold) return 0
    
    const taxableIncome = income - threshold
    return taxableIncome * (settings.taxRate || 0.1) // 简化为固定税率
  }

  /**
   * 获取系统设置（模拟）
   */
  private async getSystemSettings() {
    // 这里应该从数据库或配置中获取实际设置
    // 暂时返回默认值
    return {
      defaultBaseSalary: 3000,
      defaultDailySalary: 200,
      weekendOvertimeRate: 1.5,
      gallerySalesCommissionRate: 5,
      posSalesCommissionRate: 3,
      channelSalesCommissionRate: 2,
      coffeeSalesCommissionRate: 10,
      teacherWorkshopFee: 300,
      assistantWorkshopFee: 150,
      socialInsuranceRate: 22,
      taxRate: 10,
      taxThreshold: 5000,
      salesCommissionRate: 5,
      coffeeCommissionRate: 10
    }
  }

  /**
   * 保存薪酬计算结果
   */
  async saveSalaryRecord(calculation: SalaryCalculationResult): Promise<any> {
    return await this.db.salaryRecord.create({
      data: {
        employeeId: calculation.employeeId,
        year: calculation.year,
        month: calculation.month,
        baseSalary: calculation.baseSalary,
        attendanceSalary: calculation.attendanceSalary,
        salesCommission: calculation.salesCommission,
        pieceWorkIncome: calculation.pieceWorkIncome,
        workshopIncome: calculation.workshopIncome,
        coffeeShiftCommission: calculation.coffeeShopCommission,
        overtimePay: calculation.overtimePay,
        bonus: calculation.bonus,
        totalIncome: calculation.totalIncome,
        deductions: calculation.deductions,
        socialInsurance: calculation.socialInsurance,
        tax: calculation.tax,
        netIncome: calculation.netIncome,
        status: 'CALCULATED'
      }
    })
  }
}

// 导出单例实例
export const salaryCalculationEngine = new SalaryCalculationEngine()

// 便捷函数
export const calculateMonthlySalary = (input: SalaryCalculationInput) => 
  salaryCalculationEngine.calculateMonthlySalary(input)

export const calculateBatchSalary = (inputs: SalaryCalculationInput[]) => 
  salaryCalculationEngine.calculateBatchSalary(inputs)