"use client"

import { useState, useEffect, useRef } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  PlusIcon,
  PencilIcon,
  ArrowRightIcon,
  ImageIcon,
  SearchIcon,
  FilterIcon,
  AlertTriangleIcon,
  DownloadIcon,
  RefreshCwIcon,
  TrashIcon,
  XIcon,
  CheckIcon,
  UploadIcon,
  FileIcon,
  FileTextIcon,
  PackageIcon
} from "lucide-react"
import { SmartInput } from "@/components/ui/smart-input"
import { SmartTooltip } from "@/components/ui/tooltip"
import { TooltipProvider } from "@/components/ui/tooltip"
import {
  getWarehouses,
  getInventory,
  updateInventory,
  createInventory,
  batchUpdateInventory,
  batchDeleteInventory,
  transferInventory,
  exportInventory,
  importInventory
} from "@/lib/api/inventory-api";
import { getProducts } from "@/lib/actions/product-actions";
import { toast } from "@/components/ui/use-toast"
import { InventoryAuditLog } from "./inventory/inventory-audit-log"
import { EditableInventoryTable } from "./inventory/editable-inventory-table"

// 导入增强操作系统
import { useEnhancedOperations } from "@/lib/enhanced-operations-integration"

// 类型定义
interface Warehouse {
  id: number
  name: string
}

interface Product {
  id: number
  name: string
  sku?: string
  barcode?: string
  category?: string | { name: string }
  imageUrl?: string
  price: number
  cost?: number
}

interface InventoryItem {
  id: number
  productId: number
  warehouseId: number
  quantity: number
  minQuantity?: number
  product: Product
}

interface EditingInventory {
  warehouseId: string
  productId: number
  productName: string
  quantity: number
  minQuantity: number
}

interface TransferData {
  sourceWarehouseId: string
  targetWarehouseId: string
  productId: string
  productName?: string
  currentQuantity?: number
  quantity: number
  notes: string
}

interface ImportPreview {
  headers: string[]
  rows: any[]
  totalRows: number
}

export function InventoryManagement() {
  // 增强操作系统
  const { executeOperation, executeFormOperation, executeBatchOperation, isInitialized } = useEnhancedOperations()
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([])
  const [selectedWarehouse, setSelectedWarehouse] = useState("")
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  // DOM引用
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 智能建议数据
  const productSearchSuggestions = [
    { id: '1', value: '掐丝珐琅手镯', label: '掐丝珐琅手镯', category: '热门产品', frequency: 15 },
    { id: '2', value: '珐琅耳环', label: '珐琅耳环', category: '热门产品', frequency: 12 },
    { id: '3', value: '景泰蓝花瓶', label: '景泰蓝花瓶', category: '装饰品', frequency: 8 },
    { id: '4', value: 'SKU001', label: 'SKU001', category: 'SKU搜索', frequency: 6 },
    { id: '5', value: '条码123456', label: '条码123456', category: '条码搜索', frequency: 4 },
  ]
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false)
  const [isBatchUpdateDialogOpen, setIsBatchUpdateDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isAuditLogOpen, setIsAuditLogOpen] = useState(false)
  const [selectedInventoryItem, setSelectedInventoryItem] = useState<InventoryItem | null>(null)
  const [editingInventory, setEditingInventory] = useState<EditingInventory | null>(null)
  const [transferData, setTransferData] = useState<TransferData>({
    sourceWarehouseId: "",
    targetWarehouseId: "",
    productId: "",
    quantity: 0,
    notes: "",
  })
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importMode, setImportMode] = useState<"replace" | "add">("replace")
  const [importPreview, setImportPreview] = useState<ImportPreview | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockFilter, setStockFilter] = useState("all")
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    const initializeData = async () => {
      setIsInitialLoading(true)
      try {
        await Promise.all([
          loadWarehouses(),
          loadProducts()
        ])
      } finally {
        setIsInitialLoading(false)
      }
    }

    initializeData()
  }, [])

  useEffect(() => {
    if (selectedWarehouse) {
      loadInventory(selectedWarehouse)
    }
  }, [selectedWarehouse])

  const loadWarehouses = async () => {
    try {
      const data = await getWarehouses()
      setWarehouses(data)
      if (data.length > 0 && !selectedWarehouse) {
        setSelectedWarehouse(data[0].id.toString())
      }
    } catch (error) {
      console.error("Error loading warehouses:", error)
      toast({
        title: "错误",
        description: "加载仓库列表失败",
      })
    }
  }

  const loadProducts = async () => {
    try {
      const data = await getProducts()
      setProducts(data as any) // 暂时使用any类型避免类型错误

      // 提取所有产品类别
      const uniqueCategories = Array.from(new Set(data.map((product: any) => {
        if (!product.productCategory) return "未分类";
        return typeof product.productCategory === 'string'
          ? product.productCategory
          : (product.productCategory.name || "未分类");
      })))
      setCategories(uniqueCategories as any) // 暂时使用any类型避免类型错误
    } catch (error) {
      console.error("Error loading products:", error)
      toast({
        title: "错误",
        description: "加载产品列表失败",
      })
    }
  }

  const loadInventory = async (warehouseId: string) => {
    try {
      // 获取所有库存数据
      const data = await getInventory()

      // 如果指定了仓库ID，则筛选该仓库的库存
      const filteredData = warehouseId
        ? data.filter((item: any) => item.warehouseId === Number(warehouseId))
        : data

      setInventory(filteredData)
      setFilteredInventory(filteredData)
      setSelectedItems([]) // 重置选中项
    } catch (error) {
      console.error("Error loading inventory:", error)
      toast({
        title: "错误",
        description: "加载库存数据失败",
      } as any)
    }
  }

  // 应用筛选条件
  useEffect(() => {
    let result = inventory

    // 应用搜索词筛选
    if (searchTerm) {
      result = result.filter(item =>
        item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.product.barcode?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 应用类别筛选
    if (categoryFilter !== "all") {
      result = result.filter(item =>
        (item.product.category || "未分类") === categoryFilter
      )
    }

    // 应用库存状态筛选
    if (stockFilter === "low") {
      result = result.filter(item => {
        const minQuantity = item.minQuantity || 10
        return item.quantity < minQuantity
      })
    } else if (stockFilter === "out") {
      result = result.filter(item => item.quantity === 0)
    }

    setFilteredInventory(result)
  }, [inventory, searchTerm, categoryFilter, stockFilter])

  const handleUpdateInventory = (product: Product) => {
    // 查找现有库存
    const existingInventory = inventory.find(
      (item) => item.productId === product.id && item.warehouseId === Number(selectedWarehouse)
    )

    if (existingInventory) {
      setEditingInventory({
        warehouseId: selectedWarehouse,
        productId: product.id,
        productName: product.name,
        quantity: existingInventory.quantity,
        minQuantity: existingInventory.minQuantity || 0,
      })
    } else {
      setEditingInventory({
        warehouseId: selectedWarehouse,
        productId: product.id,
        productName: product.name,
        quantity: 0,
        minQuantity: 0,
      })
    }

    setIsUpdateDialogOpen(true)
  }

  const handleTransferInventory = (inventoryItem: InventoryItem) => {
    setTransferData({
      sourceWarehouseId: selectedWarehouse,
      targetWarehouseId: "",
      productId: inventoryItem.productId.toString(),
      productName: inventoryItem.product.name,
      currentQuantity: inventoryItem.quantity,
      quantity: 1,
      notes: "",
    })
    setIsTransferDialogOpen(true)
  }

  // 查看库存审计日志
  const handleViewAuditLog = (inventoryItem: InventoryItem) => {
    setSelectedInventoryItem(inventoryItem)
    setIsAuditLogOpen(true)
  }

  const handleSaveInventory = async () => {
    if (!editingInventory || !editingInventory.warehouseId || !editingInventory.productId || editingInventory.quantity === undefined) {
      toast({
        title: "错误",
        description: "仓库、产品和数量为必填项",
      } as any)
      return
    }

    setIsLoading(true)

    try {
      // 查找现有库存
      const existingInventory = inventory.find(
        (item) => item.productId === editingInventory.productId && item.warehouseId === Number(editingInventory.warehouseId)
      )

      const beforeData = existingInventory ? {
        quantity: existingInventory.quantity,
        minQuantity: existingInventory.minQuantity
      } : null

      const afterData = {
        quantity: Number(editingInventory.quantity),
        minQuantity: Number(editingInventory.minQuantity)
      }

      if (existingInventory) {
        // 更新现有库存
        await executeFormOperation(
          async () => {
            return await updateInventory(existingInventory.id, {
              quantity: Number(editingInventory.quantity),
              minQuantity: Number(editingInventory.minQuantity),
              notes: "手动更新库存"
            })
          },
          beforeData,
          afterData,
          '更新库存',
          'inventory',
          {
            playSound: true,
            soundType: 'success',
            enableUndo: true,
            undoTags: ['update', 'inventory'],
            undoPriority: 5
          }
        )
      } else {
        // 创建新库存
        await executeFormOperation(
          async () => {
            return await createInventory({
              productId: Number(editingInventory.productId),
              warehouseId: Number(editingInventory.warehouseId),
              quantity: Number(editingInventory.quantity),
              minQuantity: Number(editingInventory.minQuantity)
            })
          },
          null,
          afterData,
          '创建库存',
          'inventory',
          {
            playSound: true,
            soundType: 'success',
            enableUndo: true,
            undoTags: ['create', 'inventory'],
            undoPriority: 5
          }
        )
      }

      setIsUpdateDialogOpen(false)
      loadInventory(selectedWarehouse)
    } catch (error) {
      console.error("Error updating inventory:", error)
      // 错误已由增强操作系统处理
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveTransfer = async () => {
    if (
      !transferData.sourceWarehouseId ||
      !transferData.targetWarehouseId ||
      !transferData.productId ||
      !transferData.quantity
    ) {
      toast({
        title: "错误",
        description: "源仓库、目标仓库、产品和数量为必填项",
      })
      return
    }

    if (transferData.sourceWarehouseId === transferData.targetWarehouseId) {
      toast({
        title: "错误",
        description: "源仓库和目标仓库不能相同",
      })
      return
    }

    if (transferData.quantity <= 0) {
      toast({
        title: "错误",
        description: "转移数量必须大于0",
      })
      return
    }

    if (transferData.quantity > transferData.currentQuantity) {
      toast({
        title: "错误",
        description: "转移数量不能大于当前库存",
      })
      return
    }

    setIsLoading(true)

    try {
      const transferInfo = {
        productId: Number(transferData.productId),
        quantity: Number(transferData.quantity),
        fromLocationId: Number(transferData.sourceWarehouseId),
        toLocationId: Number(transferData.targetWarehouseId),
        notes: transferData.notes || "手动转移"
      }

      await executeFormOperation(
        async () => {
          return await transferInventory(transferInfo)
        },
        null,
        transferInfo,
        '库存转移',
        'inventory',
        {
          playSound: true,
          soundType: 'success',
          enableUndo: true,
          undoTags: ['transfer', 'inventory'],
          undoPriority: 6
        }
      )

      setIsTransferDialogOpen(false)
      loadInventory(selectedWarehouse)
    } catch (error) {
      console.error("Error transferring inventory:", error)
      // 错误已由增强操作系统处理
    } finally {
      setIsLoading(false)
    }
  }

  // 处理选择/取消选择所有项目
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(filteredInventory.map(item => item.id))
    } else {
      setSelectedItems([])
    }
  }

  // 处理选择/取消选择单个项目
  const handleSelectItem = (checked, itemId) => {
    if (checked) {
      setSelectedItems([...selectedItems, itemId])
    } else {
      setSelectedItems(selectedItems.filter(id => id !== itemId))
    }
  }

  // 处理批量更新
  const handleBatchUpdate = () => {
    setIsBatchUpdateDialogOpen(true)
  }

  // 执行批量更新
  const executeBatchUpdate = async (actionType, quantity, notes) => {
    if (!selectedItems.length) return

    setIsLoading(true)
    try {
      const updateData = {
        inventoryIds: selectedItems,
        actionType,
        quantity: quantity ? Number(quantity) : undefined,
        notes,
        warehouseId: selectedWarehouse ? Number(selectedWarehouse) : undefined
      }

      await executeBatchOperation(
        selectedItems,
        async (itemId, index) => {
          console.log(`Processing inventory item ${index + 1}:`, itemId)
          return await batchUpdateInventory(updateData)
        },
        '批量更新库存',
        'inventory',
        {
          playSound: true,
          soundType: 'success',
          enableUndo: true,
          undoTags: ['batch-update', 'inventory'],
          undoGroupId: `batch-update-${Date.now()}`
        }
      )

      setSelectedItems([])
      loadInventory(selectedWarehouse)
      setIsBatchUpdateDialogOpen(false)
    } catch (error) {
      console.error("Error updating inventory:", error)
      // 错误已由增强操作系统处理
    } finally {
      setIsLoading(false)
    }
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    setIsLoading(true)
    try {
      await executeOperation(
        async () => {
          return await batchDeleteInventory(selectedItems)
        },
        {
          playSound: true,
          soundType: 'warning',
          feedbackMessage: `已删除 ${selectedItems.length} 个库存项`,
          enableUndo: true,
          undoTags: ['batch-delete', 'inventory'],
          undoPriority: 8
        }
      )

      setSelectedItems([])
      loadInventory(selectedWarehouse)
    } catch (error) {
      console.error("Error deleting inventory:", error)
      // 错误已由增强操作系统处理
    } finally {
      setIsLoading(false)
    }
  }

  // 导出库存数据
  const handleExportInventory = async (format = 'csv') => {
    setIsLoading(true)
    try {
      await executeOperation(
        async () => {
          const result = await exportInventory(format, selectedWarehouse ? Number(selectedWarehouse) : undefined)

          // 确保内容是字符串
          let content = result.data
          if (typeof content !== 'string') {
            content = JSON.stringify(content)
          }

          // 创建下载链接
          const contentType = format === 'csv' ? 'text/csv;charset=utf-8' : 'application/json;charset=utf-8'
          const blob = new Blob([content], { type: contentType })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = result.filename
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)

          return result
        },
        {
          playSound: true,
          soundType: 'success',
          showProgress: true,
          progressTitle: '导出库存数据',
          feedbackMessage: `库存数据已成功导出为 ${format.toUpperCase()} 格式`,
          enableUndo: false // 导出操作不需要撤销
        }
      )
    } catch (error) {
      console.error("Error exporting inventory:", error)
      // 错误已由增强操作系统处理
    } finally {
      setIsLoading(false)
    }
  }

  // 处理文件选择
  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    setImportFile(file)

    // 读取文件预览
    const reader = new FileReader()
    reader.onload = (event) => {
      try {
        const content = event.target.result

        // 解析CSV文件
        if (file.name.endsWith('.csv')) {
          const lines = content.split('\n')
          const headers = lines[0].split(',')

          // 只预览前5行
          const previewRows = []
          for (let i = 1; i < Math.min(lines.length, 6); i++) {
            if (!lines[i].trim()) continue

            const values = lines[i].split(',')
            const row = {}

            headers.forEach((header, index) => {
              row[header.trim()] = values[index]?.trim() || ''
            })

            previewRows.push(row)
          }

          setImportPreview({
            headers,
            rows: previewRows,
            totalRows: lines.length - 1
          })
        }
        // 解析JSON文件
        else if (file.name.endsWith('.json')) {
          const data = JSON.parse(content)
          if (Array.isArray(data) && data.length > 0) {
            const headers = Object.keys(data[0])
            const previewRows = data.slice(0, 5)

            setImportPreview({
              headers,
              rows: previewRows,
              totalRows: data.length
            })
          }
        }
      } catch (error) {
        console.error("Error parsing file:", error)
        toast({
          title: "解析失败",
          description: "无法解析文件，请确保文件格式正确",
          })
      }
    }

    if (file.name.endsWith('.csv')) {
      reader.readAsText(file)
    } else if (file.name.endsWith('.json')) {
      reader.readAsText(file)
    } else {
      toast({
        title: "不支持的文件格式",
        description: "请上传CSV或JSON格式的文件",
      })
      setImportFile(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  // 处理导入
  const handleImport = async () => {
    if (!importFile || !selectedWarehouse) {
      toast({
        title: "错误",
        description: "请选择文件和仓库",
      })
      return
    }

    setIsLoading(true)
    try {
      const reader = new FileReader()

      reader.onload = async (event) => {
        try {
          const content = event.target.result
          let importData = []

          // 解析CSV文件
          if (importFile.name.endsWith('.csv')) {
            const lines = content.split('\n')
            const headers = lines[0].split(',').map(h => h.trim())

            for (let i = 1; i < lines.length; i++) {
              if (!lines[i].trim()) continue

              const values = lines[i].split(',')
              const item = {}

              headers.forEach((header, index) => {
                item[header] = values[index]?.trim() || ''
              })

              importData.push(item)
            }
          }
          // 解析JSON文件
          else if (importFile.name.endsWith('.json')) {
            importData = JSON.parse(content)
          }

          // 发送导入请求
          const result = await importInventory({
            warehouseId: selectedWarehouse,
            items: importData,
            updateMode: importMode,
            notes: "通过文件导入"
          })

          toast({
            title: "导入成功",
            description: `成功导入 ${result.success} 项，失败 ${result.failed} 项`,
          })

          // 重新加载库存
          loadInventory(selectedWarehouse)
          setIsImportDialogOpen(false)
          setImportFile(null)
          setImportPreview(null)
          if (fileInputRef.current) {
            fileInputRef.current.value = ""
          }
        } catch (error) {
          console.error("Error importing inventory:", error)
          toast({
            title: "导入失败",
            description: error.message || "导入库存数据失败",
              })
        } finally {
          setIsLoading(false)
        }
      }

      if (importFile.name.endsWith('.csv')) {
        reader.readAsText(importFile)
      } else if (importFile.name.endsWith('.json')) {
        reader.readAsText(importFile)
      }
    } catch (error) {
      console.error("Error reading file:", error)
      toast({
        title: "读取失败",
        description: "无法读取文件",
      })
      setIsLoading(false)
    }
  }

  // 清除所有筛选条件
  const clearAllFilters = () => {
    setSearchTerm("")
    setCategoryFilter("all")
    setStockFilter("all")
  }

  // 如果系统还在初始化或数据还在加载，显示加载状态
  if (!isInitialized || isInitialLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              {!isInitialized ? "正在初始化增强操作系统..." : "正在加载库存数据..."}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-4">
        <Tabs defaultValue="products" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="inventory">库存概览</TabsTrigger>
          <TabsTrigger value="products">产品库存编辑</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle>库存概览</CardTitle>
                  <CardDescription>查看库存状态、执行批量操作和库存转移</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Select value={selectedWarehouse} onValueChange={setSelectedWarehouse}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="选择仓库" />
                    </SelectTrigger>
                    <SelectContent>
                      {warehouses.map((warehouse) => (
                        <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                          {warehouse.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
                      <UploadIcon className="h-4 w-4 mr-2" />
                      导入
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline">
                          <DownloadIcon className="h-4 w-4 mr-2" />
                          导出
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleExportInventory('csv')}>
                          导出为CSV
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleExportInventory('json')}>
                          导出为JSON
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {/* 搜索和筛选 */}
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="relative flex-1">
                  <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <SmartTooltip
                    content="搜索产品名称、SKU或条码，支持智能建议和历史搜索"
                    type="help"
                    title="产品搜索"
                  >
                    <SmartInput
                      suggestions={productSearchSuggestions}
                      value={searchTerm}
                      onChange={setSearchTerm}
                      onSuggestionSelect={(suggestion) => {
                        setSearchTerm(suggestion.value)
                      }}
                      placeholder="搜索产品名称、SKU或条码..."
                      showHistory={true}
                      showFrequent={true}
                      className="pl-8"
                    />
                  </SmartTooltip>
                </div>

                <div className="flex flex-wrap gap-2">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="所有类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有类别</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={stockFilter} onValueChange={setStockFilter}>
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="库存状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有状态</SelectItem>
                      <SelectItem value="low">低库存</SelectItem>
                      <SelectItem value="out">缺货</SelectItem>
                    </SelectContent>
                  </Select>

                  {(searchTerm || categoryFilter !== "all" || stockFilter !== "all") && (
                    <Button variant="ghost" size="icon" onClick={clearAllFilters}>
                      <XIcon className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {/* 批量操作 */}
              {selectedItems.length > 0 && (
                <div className="bg-muted/50 p-2 rounded-md mb-4 flex items-center justify-between">
                  <div className="text-sm">已选择 {selectedItems.length} 项</div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={handleBatchUpdate}>
                      <PencilIcon className="h-4 w-4 mr-2" />
                      批量更新
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleBatchDelete}>
                      <TrashIcon className="h-4 w-4 mr-2" />
                      批量删除
                    </Button>
                  </div>
                </div>
              )}

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40px]">
                        <Checkbox
                          checked={selectedItems.length > 0 && selectedItems.length === filteredInventory.length}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>图片</TableHead>
                      <TableHead>产品信息</TableHead>
                      <TableHead>类别</TableHead>
                      <TableHead className="text-right">库存状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInventory.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          {inventory.length === 0 ? (
                            <div className="flex flex-col items-center justify-center text-muted-foreground">
                              <PackageIcon className="h-12 w-12 mb-2 opacity-20" />
                              <p>暂无库存数据</p>
                              <Button variant="link" onClick={() => {
                                // 安全地切换到产品标签页
                                const element = document.querySelector('[value="products"]') as HTMLElement
                                if (element && typeof element.click === 'function') {
                                  element.click()
                                } else {
                                  // 备用方案：直接触发状态更新
                                  console.warn('无法找到产品标签页元素，使用备用方案')
                                }
                              }}>
                                添加库存
                              </Button>
                            </div>
                          ) : (
                            <div className="flex flex-col items-center justify-center text-muted-foreground">
                              <FilterIcon className="h-12 w-12 mb-2 opacity-20" />
                              <p>没有符合筛选条件的库存项</p>
                              <Button variant="link" onClick={clearAllFilters}>
                                清除筛选条件
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredInventory.map((item) => {
                        const isLowStock = item.minQuantity && item.quantity < item.minQuantity
                        const isOutOfStock = item.quantity === 0

                        return (
                          <TableRow key={item.id} className={isOutOfStock ? "bg-red-50/50" : isLowStock ? "bg-amber-50/50" : ""}>
                            <TableCell>
                              <Checkbox
                                checked={selectedItems.includes(item.id)}
                                onCheckedChange={(checked) => handleSelectItem(checked, item.id)}
                              />
                            </TableCell>
                            <TableCell>
                              {item.product.imageUrl ? (
                                <div className="h-12 w-12 rounded-md overflow-hidden">
                                  <img
                                    src={item.product.imageUrl}
                                    alt={item.product.name}
                                    className="h-full w-full object-cover"
                                    onError={(e) => {
                                      e.currentTarget.src = "/placeholder.svg"
                                    }}
                                  />
                                </div>
                              ) : (
                                <div className="h-12 w-12 rounded-md bg-gray-100 flex items-center justify-center">
                                  <ImageIcon className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="font-medium">{item.product.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {item.product.sku && <span className="mr-2">SKU: {item.product.sku}</span>}
                                {item.product.barcode && <span>条码: {item.product.barcode}</span>}
                              </div>
                            </TableCell>
                            <TableCell>
                              {item.product.category ? (
                                <Badge variant="outline">{typeof item.product.category === 'string' ? item.product.category : item.product.category.name || '未分类'}</Badge>
                              ) : (
                                <span className="text-muted-foreground text-sm">未分类</span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex flex-col items-end">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">{item.quantity}</span>
                                  {isOutOfStock && (
                                    <Badge variant="destructive">缺货</Badge>
                                  )}
                                  {isLowStock && !isOutOfStock && (
                                    <Badge variant="warning" className="bg-amber-500">低库存</Badge>
                                  )}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  最低库存: {item.minQuantity || "-"}
                                </div>
                                {(item.minQuantity || 0) > 0 && (
                                  <Progress
                                    value={((item.quantity / (item.minQuantity || 1)) * 100)}
                                    className={`h-1 w-24 mt-1 ${isOutOfStock ? "bg-red-500" : isLowStock ? "bg-amber-500" : ""}`}
                                  />
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <SmartTooltip
                                  content="更新库存数量和最低库存设置"
                                  type="info"
                                  title="更新库存"
                                >
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleUpdateInventory(item.product)}
                                  >
                                    <PencilIcon className="h-4 w-4" />
                                  </Button>
                                </SmartTooltip>
                                <SmartTooltip
                                  content="将库存转移到其他仓库"
                                  type="info"
                                  title="库存转移"
                                >
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleTransferInventory(item)}
                                    disabled={item.quantity <= 0}
                                  >
                                    <ArrowRightIcon className="h-4 w-4" />
                                  </Button>
                                </SmartTooltip>
                                <SmartTooltip
                                  content="查看库存变动历史记录"
                                  type="info"
                                  title="审计日志"
                                >
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleViewAuditLog(item)}
                                  >
                                    <FileTextIcon className="h-4 w-4" />
                                  </Button>
                                </SmartTooltip>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                显示 {filteredInventory.length} 个库存项 (共 {inventory.length} 个)
              </div>
              <Button variant="outline" size="sm" onClick={() => loadInventory(selectedWarehouse)}>
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="products">
          {selectedWarehouse ? (
            <EditableInventoryTable
              warehouseId={selectedWarehouse}
              onDataChange={() => loadInventory(selectedWarehouse)}
            />
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <PackageIcon className="h-12 w-12 mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">请选择仓库</h3>
                <p className="text-muted-foreground text-center">
                  请先在库存概览页面选择一个仓库，然后返回此页面进行产品库存编辑
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* 批量更新对话框 */}
      <Dialog open={isBatchUpdateDialogOpen} onOpenChange={setIsBatchUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>批量更新库存</DialogTitle>
            <DialogDescription>
              更新选中的 {selectedItems.length} 个库存项
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const actionType = formData.get("actionType") as string
            const quantity = Number(formData.get("quantity"))
            const notes = formData.get("notes") as string

            if (!actionType || !quantity) {
              toast({
                title: "错误",
                description: "操作类型和数量为必填项",
                      })
              return
            }

            executeBatchUpdate(actionType, quantity, notes)
          }}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="actionType" className="text-right">
                  操作类型
                </Label>
                <Select name="actionType" defaultValue="add">
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择操作类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="add">增加库存</SelectItem>
                    <SelectItem value="subtract">减少库存</SelectItem>
                    <SelectItem value="set">设置库存</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="quantity" className="text-right">
                  数量
                </Label>
                <Input
                  id="quantity"
                  name="quantity"
                  type="number"
                  defaultValue={1}
                  min={1}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="notes" className="text-right">
                  备注
                </Label>
                <Input
                  id="notes"
                  name="notes"
                  placeholder="批量更新原因"
                  className="col-span-3"
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsBatchUpdateDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "更新中..." : "更新"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 更新库存对话框 */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>更新库存</DialogTitle>
            <DialogDescription>
              更新 {editingInventory?.productName} 在当前仓库的库存数量
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                库存数量
              </Label>
              <Input
                id="quantity"
                type="number"
                value={editingInventory?.quantity || 0}
                onChange={(e) =>
                  setEditingInventory(prev => prev ? {
                    ...prev,
                    quantity: parseInt(e.target.value) || 0,
                  } : null)
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="minQuantity" className="text-right">
                最低库存
              </Label>
              <Input
                id="minQuantity"
                type="number"
                value={editingInventory?.minQuantity || 0}
                onChange={(e) =>
                  setEditingInventory(prev => prev ? {
                    ...prev,
                    minQuantity: parseInt(e.target.value) || 0,
                  } : null)
                }
                className="col-span-3"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveInventory} disabled={isLoading}>
              {isLoading ? "保存中..." : "保存"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 转移库存对话框 */}
      <Dialog open={isTransferDialogOpen} onOpenChange={setIsTransferDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>转移库存</DialogTitle>
            <DialogDescription>
              将 {transferData?.productName} 从当前仓库转移到其他仓库
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sourceWarehouse" className="text-right">
                源仓库
              </Label>
              <div className="col-span-3">
                {warehouses.find((w) => w.id.toString() === transferData.sourceWarehouseId)?.name || ""}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="targetWarehouse" className="text-right">
                目标仓库
              </Label>
              <Select
                value={transferData.targetWarehouseId}
                onValueChange={(value) => setTransferData({ ...transferData, targetWarehouseId: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择目标仓库" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses
                    .filter((w) => w.id.toString() !== transferData.sourceWarehouseId)
                    .map((warehouse) => (
                      <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                        {warehouse.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="currentQuantity" className="text-right">
                当前库存
              </Label>
              <div className="col-span-3">{transferData.currentQuantity}</div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="quantity" className="text-right">
                转移数量
              </Label>
              <Input
                id="quantity"
                type="number"
                value={transferData.quantity}
                onChange={(e) =>
                  setTransferData({
                    ...transferData,
                    quantity: parseInt(e.target.value) || 0,
                  })
                }
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                备注
              </Label>
              <Input
                id="notes"
                value={transferData.notes}
                onChange={(e) => setTransferData({ ...transferData, notes: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTransferDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveTransfer} disabled={isLoading}>
              {isLoading ? "转移中..." : "转移"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入库存对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>导入库存</DialogTitle>
            <DialogDescription>
              通过CSV或JSON文件批量导入库存数据
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="grid gap-2">
              <Label htmlFor="warehouse-select">选择目标仓库</Label>
              <Select value={selectedWarehouse} onValueChange={setSelectedWarehouse}>
                <SelectTrigger>
                  <SelectValue placeholder="选择仓库" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((warehouse) => (
                    <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                      {warehouse.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="import-mode">导入模式</Label>
              <Select value={importMode} onValueChange={(value: "replace" | "add") => setImportMode(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择导入模式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="replace">替换模式 - 覆盖现有库存</SelectItem>
                  <SelectItem value="add">添加模式 - 累加到现有库存</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="file-upload">上传文件</Label>
              <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                <input
                  type="file"
                  id="file-upload"
                  className="hidden"
                  accept=".csv,.json"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                />

                {!importFile ? (
                  <div className="text-center">
                    <FileIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                    <div className="mt-4 flex text-sm leading-6 text-muted-foreground">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer rounded-md bg-background font-semibold text-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-primary"
                      >
                        <span>上传文件</span>
                      </label>
                      <p className="pl-1">或拖放文件到此处</p>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      支持CSV和JSON格式，文件大小不超过10MB
                    </p>
                  </div>
                ) : (
                  <div className="w-full">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileTextIcon className="h-8 w-8 mr-2 text-primary" />
                        <div>
                          <p className="font-medium">{importFile.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {(importFile.size / 1024).toFixed(2)} KB
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setImportFile(null)
                          setImportPreview(null)
                          if (fileInputRef.current) {
                            fileInputRef.current.value = ""
                          }
                        }}
                      >
                        <XIcon className="h-4 w-4" />
                      </Button>
                    </div>

                    {importPreview && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">文件预览 (共 {importPreview.totalRows} 行)</h4>
                        <div className="border rounded-md overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="bg-muted">
                                {importPreview.headers.map((header, index) => (
                                  <th key={index} className="px-4 py-2 text-left font-medium">{header}</th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {importPreview.rows.map((row, rowIndex) => (
                                <tr key={rowIndex} className="border-t">
                                  {importPreview.headers.map((header, colIndex) => (
                                    <td key={colIndex} className="px-4 py-2">{row[header] || ''}</td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-muted/50 rounded-md p-4">
              <h4 className="text-sm font-medium mb-2">文件格式说明</h4>
              <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
                <li>CSV文件第一行必须是标题行，包含字段名</li>
                <li>必须包含以下字段之一：productId、sku或barcode</li>
                <li>必须包含quantity字段表示库存数量</li>
                <li>可选包含minQuantity字段表示最低库存</li>
                <li>JSON文件必须是对象数组，每个对象包含上述字段</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleImport} disabled={!importFile || isLoading}>
              {isLoading ? "导入中..." : "导入"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 库存审计日志对话框 */}
      {selectedInventoryItem && (
        <InventoryAuditLog
          open={isAuditLogOpen}
          onOpenChange={setIsAuditLogOpen}
          productId={selectedInventoryItem.productId}
          productName={selectedInventoryItem.product?.name}
          inventoryId={selectedInventoryItem.id}
        />
      )}
      </div>
    </TooltipProvider>
  )
}
