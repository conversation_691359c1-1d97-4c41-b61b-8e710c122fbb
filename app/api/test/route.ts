import { NextResponse } from 'next/server'
import prisma from '@/lib/db'

export async function GET() {
  try {
    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1 as test`
    
    // 获取基本统计
    const stats = {
      users: await prisma.user.count(),
      products: await prisma.product.count(),
      inventory: await prisma.inventoryItem.count(),
      timestamp: new Date().toISOString()
    }
    
    return NextResponse.json({
      status: 'success',
      message: '数据库连接正常',
      stats
    })
  } catch (error) {
    console.error('API测试失败:', error)
    return NextResponse.json({
      status: 'error',
      message: '数据库连接失败',
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
