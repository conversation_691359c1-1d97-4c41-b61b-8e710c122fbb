# 完全独立的Docker环境配置
# 用于避免与其他ERP测试项目的数据库冲突
# 使用独立的端口、容器名称、网络和数据卷

services:
  # 独立PostgreSQL数据库服务
  linghua-isolated-postgres:
    image: postgres:15-alpine
    container_name: linghua-isolated-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: linghua_enamel_gallery_isolated
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: isolated_password_2024
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5434:5432"
    volumes:
      - isolated_postgres_data:/var/lib/postgresql/data
      - ./prisma/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - isolated-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d linghua_enamel_gallery_isolated"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    # 内存优化配置
    command: >
      postgres
      -c shared_buffers=128MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
      -c max_connections=50
      -c random_page_cost=1.1
      -c effective_io_concurrency=200

  # 独立Redis缓存服务
  linghua-isolated-redis:
    image: redis:7-alpine
    container_name: linghua-isolated-redis
    restart: unless-stopped
    ports:
      - "6381:6379"
    volumes:
      - isolated_redis_data:/data
    networks:
      - isolated-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # 独立应用服务
  linghua-isolated-app:
    image: node:18-alpine
    container_name: linghua-isolated-app
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install --legacy-peer-deps && npm run dev"
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=***************************************************************************/linghua_enamel_gallery_isolated?schema=public
      - NEXTAUTH_URL=http://localhost:3002
      - NEXTAUTH_SECRET=linghua-isolated-secret-key-2024
      - AUTH_SECRET=linghua-isolated-secret-key-2024
      - AUTH_TRUST_HOST=true
      - REDIS_URL=redis://linghua-isolated-redis:6379
    depends_on:
      linghua-isolated-postgres:
        condition: service_healthy
      linghua-isolated-redis:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - isolated_app_cache:/app/.next
    networks:
      - isolated-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 独立数据库管理工具
  linghua-isolated-adminer:
    image: adminer:latest
    container_name: linghua-isolated-adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=linghua-isolated-postgres
      - ADMINER_DESIGN=pepa-linha
    depends_on:
      - linghua-isolated-postgres
    networks:
      - isolated-network

# 独立数据卷
volumes:
  isolated_postgres_data:
    driver: local
    name: linghua_isolated_postgres_data
  isolated_redis_data:
    driver: local
    name: linghua_isolated_redis_data
  isolated_app_cache:
    driver: local
    name: linghua_isolated_app_cache

# 独立网络
networks:
  isolated-network:
    driver: bridge
    name: linghua-isolated-network
