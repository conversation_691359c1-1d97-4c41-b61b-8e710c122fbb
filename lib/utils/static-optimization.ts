/**
 * 静态资源优化工具
 * 提供图片、字体、CSS等静态资源的优化功能
 */

// 图片优化配置
export const imageOptimization = {
  // 支持的图片格式
  supportedFormats: ['webp', 'avif', 'jpeg', 'png', 'gif'],
  
  // 响应式图片尺寸
  breakpoints: {
    mobile: 640,
    tablet: 768,
    desktop: 1024,
    large: 1280,
    xlarge: 1536
  },

  // 图片质量设置
  quality: {
    webp: 85,
    avif: 80,
    jpeg: 85,
    png: 95
  },

  // 懒加载配置
  lazyLoading: {
    rootMargin: '50px',
    threshold: 0.1,
    placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+'
  }
}

// 字体优化配置
export const fontOptimization = {
  // 字体预加载
  preloadFonts: [
    {
      href: '/fonts/inter-var.woff2',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    }
  ],

  // 字体显示策略
  fontDisplay: 'swap',

  // 字体子集
  subsets: ['latin', 'latin-ext'],

  // 字体变体
  weights: ['400', '500', '600', '700'],
  styles: ['normal', 'italic']
}

// CSS优化配置
export const cssOptimization = {
  // 关键CSS内联
  inlineCritical: true,
  
  // CSS压缩
  minify: true,
  
  // 移除未使用的CSS
  purgeUnused: true,
  
  // CSS模块化
  modules: true
}

/**
 * 生成响应式图片srcSet
 */
export function generateSrcSet(src: string, sizes: number[] = [640, 768, 1024, 1280]): string {
  return sizes
    .map(size => `${src}?w=${size}&q=85 ${size}w`)
    .join(', ')
}

/**
 * 生成图片sizes属性
 */
export function generateSizes(breakpoints: Record<string, number> = imageOptimization.breakpoints): string {
  const entries = Object.entries(breakpoints).sort(([,a], [,b]) => a - b)
  
  return entries
    .map(([name, width], index) => {
      if (index === entries.length - 1) {
        return `${width}px`
      }
      return `(max-width: ${width}px) ${width}px`
    })
    .join(', ')
}

/**
 * 获取优化的图片URL
 */
export function getOptimizedImageUrl(
  src: string, 
  options: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'avif' | 'jpeg' | 'png'
  } = {}
): string {
  if (!src) return ''
  
  const params = new URLSearchParams()
  
  if (options.width) params.set('w', options.width.toString())
  if (options.height) params.set('h', options.height.toString())
  if (options.quality) params.set('q', options.quality.toString())
  if (options.format) params.set('f', options.format)
  
  const queryString = params.toString()
  return queryString ? `${src}?${queryString}` : src
}

/**
 * 检测浏览器支持的图片格式
 */
export function detectSupportedImageFormat(): Promise<'avif' | 'webp' | 'jpeg'> {
  return new Promise((resolve) => {
    // 检测AVIF支持
    const avifImage = new Image()
    avifImage.onload = () => resolve('avif')
    avifImage.onerror = () => {
      // 检测WebP支持
      const webpImage = new Image()
      webpImage.onload = () => resolve('webp')
      webpImage.onerror = () => resolve('jpeg')
      webpImage.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
    }
    avifImage.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A='
  })
}

/**
 * 预加载关键资源
 */
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return

  // 预加载字体
  fontOptimization.preloadFonts.forEach(font => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = font.type
    link.href = font.href
    link.crossOrigin = font.crossOrigin
    document.head.appendChild(link)
  })

  // 预加载关键图片
  const criticalImages = document.querySelectorAll('img[data-priority="high"]')
  criticalImages.forEach(img => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = img.getAttribute('src') || ''
    document.head.appendChild(link)
  })
}

/**
 * 实施懒加载
 */
export function setupLazyLoading() {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return

  const imageObserver = new IntersectionObserver(
    (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          const srcset = img.dataset.srcset
          
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
          }
          
          if (srcset) {
            img.srcset = srcset
            img.removeAttribute('data-srcset')
          }
          
          img.classList.remove('lazy')
          img.classList.add('loaded')
          observer.unobserve(img)
        }
      })
    },
    {
      rootMargin: imageOptimization.lazyLoading.rootMargin,
      threshold: imageOptimization.lazyLoading.threshold
    }
  )

  // 观察所有懒加载图片
  const lazyImages = document.querySelectorAll('img[data-src]')
  lazyImages.forEach(img => imageObserver.observe(img))
}

/**
 * 优化CSS加载
 */
export function optimizeCSSLoading() {
  if (typeof window === 'undefined') return

  // 异步加载非关键CSS
  const nonCriticalCSS = document.querySelectorAll('link[rel="preload"][as="style"]')
  nonCriticalCSS.forEach(link => {
    link.addEventListener('load', () => {
      (link as HTMLLinkElement).rel = 'stylesheet'
    })
  })

  // 内联关键CSS
  if (cssOptimization.inlineCritical) {
    const criticalCSS = `
      /* 关键CSS - 首屏渲染必需 */
      body { margin: 0; font-family: Inter, sans-serif; }
      .loading { opacity: 0; transition: opacity 0.3s; }
      .loaded { opacity: 1; }
      .lazy { filter: blur(5px); transition: filter 0.3s; }
    `
    
    const style = document.createElement('style')
    style.textContent = criticalCSS
    document.head.appendChild(style)
  }
}

/**
 * 资源提示优化
 */
export function addResourceHints() {
  if (typeof window === 'undefined') return

  // DNS预解析
  const domains = [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    'cdn.jsdelivr.net'
  ]

  domains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = `//${domain}`
    document.head.appendChild(link)
  })

  // 预连接关键域名
  const criticalDomains = ['fonts.gstatic.com']
  criticalDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = `//${domain}`
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

/**
 * 初始化所有静态资源优化
 */
export function initializeStaticOptimization() {
  if (typeof window === 'undefined') return

  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      preloadCriticalResources()
      setupLazyLoading()
      optimizeCSSLoading()
      addResourceHints()
    })
  } else {
    preloadCriticalResources()
    setupLazyLoading()
    optimizeCSSLoading()
    addResourceHints()
  }
}

/**
 * 图片压缩工具（客户端）
 */
export async function compressImage(
  file: File,
  options: {
    maxWidth?: number
    maxHeight?: number
    quality?: number
    format?: 'webp' | 'jpeg' | 'png'
  } = {}
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      const { maxWidth = 1920, maxHeight = 1080, quality = 0.85, format = 'webp' } = options

      // 计算新尺寸
      let { width, height } = img
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      canvas.width = width
      canvas.height = height

      // 绘制图片
      ctx?.drawImage(img, 0, 0, width, height)

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        `image/${format}`,
        quality
      )
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}