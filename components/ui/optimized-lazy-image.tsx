"use client"

import React, { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { 
  generateSrcSet, 
  generateSizes, 
  getOptimizedImageUrl,
  imageOptimization 
} from '@/lib/utils/static-optimization'

interface OptimizedLazyImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  sizes?: string
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
  responsive?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  loading?: 'lazy' | 'eager'
}

export function OptimizedLazyImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  sizes,
  placeholder = 'blur',
  blurDataURL,
  onLoad,
  onError,
  responsive = true,
  objectFit = 'cover',
  loading = 'lazy'
}: OptimizedLazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLDivElement>(null)

  // 交叉观察器用于懒加载
  useEffect(() => {
    if (priority || typeof window === 'undefined') return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: imageOptimization.lazyLoading.rootMargin,
        threshold: imageOptimization.lazyLoading.threshold
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [priority])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  // 生成优化的图片URL
  const optimizedSrc = getOptimizedImageUrl(src, {
    width,
    height,
    quality,
    format: 'webp'
  })

  // 生成响应式sizes
  const responsiveSizes = sizes || (responsive ? generateSizes() : undefined)

  // 默认模糊占位符
  const defaultBlurDataURL = blurDataURL || imageOptimization.lazyLoading.placeholder

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      style={{ width, height }}
    >
      {/* 错误状态 */}
      {hasError ? (
        <div className="flex items-center justify-center w-full h-full bg-muted text-muted-foreground">
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        </div>
      ) : (
        <>
          {/* 加载占位符 */}
          {!isLoaded && (
            <div className="absolute inset-0 bg-muted animate-pulse" />
          )}

          {/* 实际图片 */}
          {(isInView || priority) && (
            <Image
              src={optimizedSrc}
              alt={alt}
              width={width}
              height={height}
              className={cn(
                'transition-opacity duration-300',
                isLoaded ? 'opacity-100' : 'opacity-0',
                objectFit === 'cover' && 'object-cover',
                objectFit === 'contain' && 'object-contain',
                objectFit === 'fill' && 'object-fill',
                objectFit === 'none' && 'object-none',
                objectFit === 'scale-down' && 'object-scale-down'
              )}
              sizes={responsiveSizes}
              priority={priority}
              quality={quality}
              placeholder={placeholder}
              blurDataURL={defaultBlurDataURL}
              loading={loading}
              onLoad={handleLoad}
              onError={handleError}
              fill={!width && !height}
            />
          )}
        </>
      )}

      {/* 加载指示器 */}
      {!isLoaded && !hasError && (isInView || priority) && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

// 预设配置的图片组件
export function ProductImage({ src, alt, ...props }: Omit<OptimizedLazyImageProps, 'width' | 'height'>) {
  return (
    <OptimizedLazyImage
      src={src}
      alt={alt}
      width={300}
      height={300}
      quality={90}
      objectFit="cover"
      responsive
      {...props}
    />
  )
}

export function AvatarImage({ src, alt, ...props }: Omit<OptimizedLazyImageProps, 'width' | 'height'>) {
  return (
    <OptimizedLazyImage
      src={src}
      alt={alt}
      width={40}
      height={40}
      quality={95}
      objectFit="cover"
      className="rounded-full"
      {...props}
    />
  )
}

export function HeroImage({ src, alt, ...props }: Omit<OptimizedLazyImageProps, 'priority'>) {
  return (
    <OptimizedLazyImage
      src={src}
      alt={alt}
      priority
      quality={90}
      responsive
      {...props}
    />
  )
}

export function ThumbnailImage({ src, alt, ...props }: Omit<OptimizedLazyImageProps, 'width' | 'height'>) {
  return (
    <OptimizedLazyImage
      src={src}
      alt={alt}
      width={150}
      height={150}
      quality={80}
      objectFit="cover"
      {...props}
    />
  )
}