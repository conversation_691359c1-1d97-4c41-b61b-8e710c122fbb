/**
 * 修复Icons导入问题
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 开始修复Icons导入问题...')

// 图标映射
const iconMappings = {
  'Icons.user': 'User',
  'Icons.search': 'Search',
  'Icons.spinner': 'Loader2',
  'Icons.history': 'History',
  'Icons.languages': 'Languages',
  'Icons.refresh': 'RefreshCw',
  'Icons.alertCircle': 'AlertCircle',
  'Icons.dollarSign': 'DollarSign',
  'Icons.shoppingCart': 'ShoppingCart',
  'Icons.users': 'Users',
  'Icons.package': 'Package',
  'Icons.trendingUp': 'TrendingUp',
  'Icons.trendingDown': 'TrendingDown',
  'Icons.creditCard': 'CreditCard'
}

// 需要修复的文件
const filesToFix = [
  'components/user-avatar.tsx',
  'components/ui/enhanced-search.tsx',
  'components/ui/language-switcher.tsx',
  'components/finance/finance-dashboard-new.tsx'
]

function fixIconsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`)
    return
  }

  let content = fs.readFileSync(filePath, 'utf8')
  let hasChanges = false
  
  // 收集需要的图标
  const neededIcons = new Set()
  
  // 替换Icons.xxx为直接的图标名
  Object.entries(iconMappings).forEach(([oldIcon, newIcon]) => {
    if (content.includes(oldIcon)) {
      content = content.replace(new RegExp(oldIcon.replace('.', '\\.'), 'g'), newIcon)
      neededIcons.add(newIcon)
      hasChanges = true
    }
  })
  
  if (hasChanges) {
    // 移除Icons导入
    content = content.replace(/import { Icons } from "@\/components\/icons"\n?/g, '')
    
    // 添加lucide-react导入
    if (neededIcons.size > 0) {
      const iconsImport = `import { ${Array.from(neededIcons).join(', ')} } from "lucide-react"\n`
      
      // 在第一个import后添加
      const importMatch = content.match(/^import.*$/m)
      if (importMatch) {
        content = content.replace(importMatch[0], importMatch[0] + '\n' + iconsImport)
      } else {
        content = iconsImport + content
      }
    }
    
    fs.writeFileSync(filePath, content)
    console.log(`✅ 修复: ${filePath} (${neededIcons.size} 个图标)`)
  }
}

// 执行修复
filesToFix.forEach(fixIconsInFile)

console.log('🎉 Icons导入修复完成！')
