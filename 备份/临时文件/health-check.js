const { PrismaClient } = require('@prisma/client')
const http = require('http')

const prisma = new PrismaClient()

async function checkDatabase() {
  try {
    await prisma.$queryRaw`SELECT 1 as test`
    return { status: 'healthy', message: '数据库连接正常' }
  } catch (error) {
    return { status: 'unhealthy', message: `数据库连接失败: ${error.message}` }
  }
}

function checkAPI(path) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      timeout: 5000
    }

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        resolve({ status: 'healthy', code: res.statusCode })
      } else {
        resolve({ status: 'unhealthy', code: res.statusCode })
      }
    })

    req.on('error', (error) => {
      resolve({ status: 'unhealthy', error: error.message })
    })

    req.on('timeout', () => {
      resolve({ status: 'unhealthy', error: 'timeout' })
    })

    req.end()
  })
}

async function runHealthCheck() {
  console.log('🔄 开始系统健康检查...\n')

  // 1. 数据库检查
  console.log('1. 数据库检查:')
  const dbResult = await checkDatabase()
  console.log(`   状态: ${dbResult.status}`)
  console.log(`   信息: ${dbResult.message}\n`)

  // 2. API端点检查
  console.log('2. API端点检查:')
  const endpoints = [
    '/api/test',
    '/api/health', 
    '/api/products',
    '/api/inventory',
    '/api/users'
  ]

  for (const endpoint of endpoints) {
    const result = await checkAPI(endpoint)
    console.log(`   ${endpoint}: ${result.status} ${result.code ? `(${result.code})` : result.error ? `(${result.error})` : ''}`)
  }

  console.log('\n🎉 健康检查完成!')
  
  await prisma.$disconnect()
}

runHealthCheck().catch(console.error)
