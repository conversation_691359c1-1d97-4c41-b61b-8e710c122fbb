// 监控和日志系统统一导出
export { SystemMonitor } from './system-monitor'
export { Logger, createLogger } from '../logging/logger'

import { SystemMonitor } from './system-monitor'
import { Logger, createLogger } from '../logging/logger'
import { PrismaClient } from '@prisma/client'

// 监控和日志集成管理器
export class MonitoringManager {
  private systemMonitor: SystemMonitor
  private logger: Logger
  private isStarted = false

  constructor(
    private prisma: PrismaClient,
    private config: MonitoringManagerConfig = DEFAULT_MONITORING_MANAGER_CONFIG
  ) {
    // 初始化日志系统
    this.logger = createLogger(this.config.logging, this.prisma)

    // 初始化系统监控
    this.systemMonitor = new SystemMonitor(this.prisma, this.config.monitoring)

    // 设置监控事件处理
    this.setupMonitoringEventHandlers()
  }

  // 启动监控和日志系统
  async start(): Promise<void> {
    if (this.isStarted) {
      this.logger.warn('监控管理器已经启动')
      return
    }

    try {
      // 启动系统监控
      this.systemMonitor.start()

      this.isStarted = true
      this.logger.info('监控和日志系统启动成功', {
        monitoring: this.config.monitoring,
        logging: this.config.logging
      })

      // 记录系统启动事件
      this.logger.business('system_startup', {
        timestamp: new Date(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version
      })

    } catch (error) {
      this.logger.error('监控和日志系统启动失败', error)
      throw error
    }
  }

  // 停止监控和日志系统
  async stop(): Promise<void> {
    if (!this.isStarted) {
      return
    }

    try {
      // 记录系统关闭事件
      this.logger.business('system_shutdown', {
        timestamp: new Date(),
        uptime: process.uptime()
      })

      // 停止系统监控
      this.systemMonitor.stop()

      // 关闭日志系统
      await this.logger.close()

      this.isStarted = false
      console.log('监控和日志系统已停止')
    } catch (error) {
      console.error('停止监控和日志系统失败:', error)
    }
  }

  // 获取系统状态
  getSystemStatus() {
    return this.systemMonitor.getCurrentStatus()
  }

  // 生成健康报告
  async generateHealthReport() {
    return await this.systemMonitor.generateHealthReport()
  }

  // 获取日志统计
  async getLogStats(timeRange: { start: Date; end: Date }) {
    return await this.logger.getLogStats(timeRange)
  }

  // 查询日志
  async queryLogs(options: any) {
    return await this.logger.queryLogs(options)
  }

  // 记录业务操作
  logBusinessOperation(operation: string, details: any, userId?: string) {
    this.logger.business(operation, details, { userId })
  }

  // 记录安全事件
  logSecurityEvent(event: string, details: any, userId?: string, requestId?: string) {
    this.logger.security(event, details, { userId, requestId })
  }

  // 记录性能指标
  logPerformanceMetric(metric: string, value: number, unit: string, context?: any) {
    this.logger.performance(metric, value, unit, context)
  }

  // 记录审计日志
  logAudit(action: string, resource: string, userId?: string, details?: any) {
    this.logger.audit(action, resource, userId, details)
  }

  // 设置监控事件处理器
  private setupMonitoringEventHandlers(): void {
    // 监控启动事件
    this.systemMonitor.on('monitoring_started', () => {
      this.logger.info('系统监控已启动')
    })

    // 监控停止事件
    this.systemMonitor.on('monitoring_stopped', () => {
      this.logger.info('系统监控已停止')
    })

    // 指标收集事件
    this.systemMonitor.on('metrics_collected', (metrics) => {
      // 记录关键性能指标
      this.logPerformanceMetric('memory_usage', metrics.system.memory.usage, '%')
      this.logPerformanceMetric('cpu_usage', metrics.system.cpu.usage, '%')
      this.logPerformanceMetric('db_connection_time', metrics.database.connectionTime, 'ms')
      this.logPerformanceMetric('active_users', metrics.application.activeUsers, 'count')
    })

    // 告警事件
    this.systemMonitor.on('alert', (alert) => {
      this.logger.warn(`系统告警: ${alert.rule}`, {
        alertId: alert.id,
        severity: alert.severity,
        message: alert.message,
        metrics: alert.metrics
      }, {
        category: 'alert',
        alertId: alert.id
      })

      // 对于严重告警，记录错误日志
      if (alert.severity === 'critical') {
        this.logger.error(`严重系统告警: ${alert.message}`, alert)
      }
    })

    // 监控数据收集错误
    this.systemMonitor.on('collection_error', (error) => {
      this.logger.error('监控数据收集失败', error, {
        category: 'monitoring'
      })
    })
  }

  // 创建请求追踪中间件
  createRequestTracker() {
    return (req: any, res: any, next: any) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const startTime = Date.now()

      // 添加请求ID到请求对象
      req.requestId = requestId

      // 记录请求开始
      this.logger.info(`请求开始: ${req.method} ${req.path}`, {
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        query: req.query,
        body: req.method !== 'GET' ? req.body : undefined
      }, {
        requestId,
        userId: req.user?.id
      })

      // 监听响应完成
      res.on('finish', () => {
        const duration = Date.now() - startTime
        const statusCode = res.statusCode

        this.logger.info(`请求完成: ${req.method} ${req.path}`, {
          method: req.method,
          path: req.path,
          statusCode,
          duration,
          contentLength: res.get('Content-Length')
        }, {
          requestId,
          userId: req.user?.id
        })

        // 记录性能指标
        this.logPerformanceMetric('request_duration', duration, 'ms', {
          method: req.method,
          path: req.path,
          statusCode
        })

        // 如果是错误响应，记录详细信息
        if (statusCode >= 400) {
          this.logger.warn(`请求错误: ${req.method} ${req.path}`, {
            statusCode,
            duration,
            error: res.locals.error
          }, {
            requestId,
            userId: req.user?.id
          })
        }
      })

      next()
    }
  }

  // 创建错误处理中间件
  createErrorHandler() {
    return (error: any, req: any, res: any, next: any) => {
      const requestId = req.requestId || 'unknown'

      // 记录错误日志
      this.logger.error(`请求处理错误: ${req.method} ${req.path}`, error, {
        requestId,
        userId: req.user?.id,
        method: req.method,
        path: req.path,
        query: req.query,
        body: req.body
      })

      // 记录安全相关错误
      if (error.status === 401 || error.status === 403) {
        this.logSecurityEvent('unauthorized_access', {
          path: req.path,
          method: req.method,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          error: error.message
        }, req.user?.id, requestId)
      }

      next(error)
    }
  }

  // 健康检查端点处理器
  createHealthCheckHandler() {
    return async (req: any, res: any) => {
      try {
        const status = this.getSystemStatus()
        const healthReport = await this.generateHealthReport()

        res.json({
          status: status.overall,
          timestamp: new Date(),
          uptime: process.uptime(),
          system: status,
          report: healthReport
        })
      } catch (error) {
        this.logger.error('健康检查失败', error)
        res.status(500).json({
          status: 'unhealthy',
          error: 'Health check failed'
        })
      }
    }
  }

  // 日志查询端点处理器
  createLogQueryHandler() {
    return async (req: any, res: any) => {
      try {
        const {
          level,
          category,
          userId,
          startTime,
          endTime,
          message,
          limit = 100,
          offset = 0,
          order = 'desc'
        } = req.query

        const options = {
          level,
          category,
          userId,
          startTime: startTime ? new Date(startTime) : undefined,
          endTime: endTime ? new Date(endTime) : undefined,
          message,
          limit: parseInt(limit),
          offset: parseInt(offset),
          order
        }

        const logs = await this.queryLogs(options)
        
        res.json({
          logs,
          total: logs.length,
          offset: parseInt(offset),
          limit: parseInt(limit)
        })
      } catch (error) {
        this.logger.error('日志查询失败', error)
        res.status(500).json({
          error: 'Log query failed'
        })
      }
    }
  }

  // 指标端点处理器
  createMetricsHandler() {
    return (req: any, res: any) => {
      try {
        const metrics = this.systemMonitor.getHistoricalMetrics(
          new Date(Date.now() - 60 * 60 * 1000), // 最近1小时
          new Date()
        )

        res.json({
          metrics,
          timestamp: new Date()
        })
      } catch (error) {
        this.logger.error('获取指标失败', error)
        res.status(500).json({
          error: 'Metrics retrieval failed'
        })
      }
    }
  }

  // 清理旧数据
  async cleanupOldData(retentionDays: number = 90): Promise<void> {
    try {
      this.logger.info('开始清理旧数据', { retentionDays })

      // 清理旧日志
      const deletedLogs = await this.logger.cleanupOldLogs(retentionDays)

      // 清理旧告警
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      const deletedAlerts = await this.prisma.systemAlert.deleteMany({
        where: {
          createdAt: { lt: cutoffDate }
        }
      })

      // 清理旧指标
      const deletedMetrics = await this.prisma.systemMetrics.deleteMany({
        where: {
          timestamp: { lt: cutoffDate }
        }
      })

      this.logger.info('数据清理完成', {
        deletedLogs,
        deletedAlerts: deletedAlerts.count,
        deletedMetrics: deletedMetrics.count
      })

    } catch (error) {
      this.logger.error('数据清理失败', error)
      throw error
    }
  }

  // 导出监控数据
  async exportMonitoringData(format: 'json' | 'csv' = 'json'): Promise<string> {
    try {
      const endTime = new Date()
      const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000) // 最近24小时

      const [
        healthReport,
        logStats,
        recentLogs,
        metrics
      ] = await Promise.all([
        this.generateHealthReport(),
        this.getLogStats({ start: startTime, end: endTime }),
        this.queryLogs({ startTime, endTime, limit: 1000 }),
        this.systemMonitor.getHistoricalMetrics(startTime, endTime)
      ])

      const exportData = {
        exportTimestamp: new Date(),
        timeRange: { start: startTime, end: endTime },
        healthReport,
        logStats,
        recentLogs,
        metrics
      }

      if (format === 'json') {
        return JSON.stringify(exportData, null, 2)
      } else {
        // 简化的CSV导出
        return this.convertToCSV(exportData)
      }
    } catch (error) {
      this.logger.error('导出监控数据失败', error)
      throw error
    }
  }

  // 转换为CSV格式
  private convertToCSV(data: any): string {
    // 简化的CSV转换实现
    const csvLines = []
    csvLines.push('timestamp,type,level,message,details')

    if (data.recentLogs) {
      for (const log of data.recentLogs) {
        const line = [
          log.timestamp,
          'log',
          log.level,
          log.message.replace(/,/g, ';'),
          JSON.stringify(log.meta).replace(/,/g, ';')
        ].join(',')
        csvLines.push(line)
      }
    }

    return csvLines.join('\n')
  }
}

// 配置接口
export interface MonitoringManagerConfig {
  monitoring: any // SystemMonitor配置
  logging: any    // Logger配置
}

export const DEFAULT_MONITORING_MANAGER_CONFIG: MonitoringManagerConfig = {
  monitoring: {
    collectionInterval: 30000, // 30秒
    persistMetrics: true,
    alerting: {
      enabled: true,
      channels: ['console', 'database']
    },
    retention: {
      metrics: 30,
      alerts: 90
    }
  },
  logging: {
    level: 'info',
    service: 'erp-system',
    flushInterval: 5000,
    console: {
      enabled: true,
      colors: true
    },
    file: {
      enabled: true,
      directory: './logs',
      maxSize: 100 * 1024 * 1024,
      maxFiles: 10
    },
    database: {
      enabled: true
    },
    remote: {
      enabled: false
    }
  }
}

// 工厂函数
export function createMonitoringManager(
  prisma: PrismaClient,
  config?: Partial<MonitoringManagerConfig>
): MonitoringManager {
  const finalConfig = {
    monitoring: { ...DEFAULT_MONITORING_MANAGER_CONFIG.monitoring, ...config?.monitoring },
    logging: { ...DEFAULT_MONITORING_MANAGER_CONFIG.logging, ...config?.logging }
  }
  
  return new MonitoringManager(prisma, finalConfig)
}