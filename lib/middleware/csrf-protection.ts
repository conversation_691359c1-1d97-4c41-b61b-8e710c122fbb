import { NextRequest, NextResponse } from 'next/server'
import { randomBytes, createHmac } from 'crypto'

interface CSRFConfig {
  secret: string
  cookieName?: string
  headerName?: string
  tokenLength?: number
  httpOnly?: boolean
  secure?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  maxAge?: number
  ignoreMethods?: string[]
}

const defaultConfig: Required<Omit<CSRFConfig, 'secret'>> = {
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  tokenLength: 32,
  httpOnly: false, // 需要JavaScript访问来发送请求
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  maxAge: 24 * 60 * 60 * 1000, // 24小时
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS']
}

export class CSRFProtection {
  private config: Required<CSRFConfig>

  constructor(config: CSRFConfig) {
    this.config = { ...defaultConfig, ...config }
  }

  // 生成CSRF token
  generateToken(): string {
    const randomToken = randomBytes(this.config.tokenLength).toString('hex')
    const timestamp = Date.now().toString()
    const payload = `${randomToken}:${timestamp}`
    
    // 使用HMAC签名
    const signature = createHmac('sha256', this.config.secret)
      .update(payload)
      .digest('hex')
    
    return `${payload}:${signature}`
  }

  // 验证CSRF token
  validateToken(token: string): boolean {
    if (!token) return false

    const parts = token.split(':')
    if (parts.length !== 3) return false

    const [randomToken, timestamp, signature] = parts
    const payload = `${randomToken}:${timestamp}`

    // 验证签名
    const expectedSignature = createHmac('sha256', this.config.secret)
      .update(payload)
      .digest('hex')

    if (signature !== expectedSignature) return false

    // 验证时间戳（token是否过期）
    const tokenTime = parseInt(timestamp, 10)
    const now = Date.now()
    
    if (now - tokenTime > this.config.maxAge) return false

    return true
  }

  // 从请求中获取token
  private getTokenFromRequest(req: NextRequest): string | null {
    // 优先从header获取
    const headerToken = req.headers.get(this.config.headerName)
    if (headerToken) return headerToken

    // 从cookie获取
    const cookieToken = req.cookies.get(this.config.cookieName)?.value
    if (cookieToken) return cookieToken

    // 从表单数据获取（如果是POST请求）
    if (req.method === 'POST' && req.headers.get('content-type')?.includes('application/x-www-form-urlencoded')) {
      // 注意：这里需要在实际使用时解析表单数据
      // const formData = await req.formData()
      // return formData.get('csrf-token')?.toString() || null
    }

    return null
  }

  // 中间件函数
  middleware() {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      try {
        const method = req.method.toUpperCase()

        // 忽略指定的HTTP方法
        if (this.config.ignoreMethods.includes(method)) {
          return NextResponse.next()
        }

        // 获取并验证CSRF token
        const token = this.getTokenFromRequest(req)
        
        if (!token || !this.validateToken(token)) {
          return new NextResponse(
            JSON.stringify({
              error: 'CSRF token validation failed',
              message: '无效的CSRF令牌'
            }),
            {
              status: 403,
              headers: {
                'Content-Type': 'application/json'
              }
            }
          )
        }

        return NextResponse.next()
      } catch (error) {
        console.error('CSRF protection middleware error:', error)
        
        return new NextResponse(
          JSON.stringify({
            error: 'CSRF protection error',
            message: 'CSRF保护机制错误'
          }),
          {
            status: 500,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      }
    }
  }

  // 设置CSRF token到cookie
  setTokenCookie(response: NextResponse, token?: string): NextResponse {
    const csrfToken = token || this.generateToken()
    
    response.cookies.set(this.config.cookieName, csrfToken, {
      httpOnly: this.config.httpOnly,
      secure: this.config.secure,
      sameSite: this.config.sameSite,
      maxAge: Math.floor(this.config.maxAge / 1000), // seconds
      path: '/'
    })

    // 同时设置到响应头，方便客户端获取
    if (!this.config.httpOnly) {
      response.headers.set('X-CSRF-Token', csrfToken)
    }

    return response
  }
}

// 默认CSRF保护实例
export const csrfProtection = new CSRFProtection({
  secret: process.env.CSRF_SECRET || process.env.NEXTAUTH_SECRET || 'default-csrf-secret',
  cookieName: 'erp-csrf-token',
  headerName: 'x-csrf-token'
})

// API路由CSRF保护
export const apiCSRFProtection = new CSRFProtection({
  secret: process.env.CSRF_SECRET || process.env.NEXTAUTH_SECRET || 'default-csrf-secret',
  cookieName: 'api-csrf-token',
  headerName: 'x-api-csrf-token',
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'] // API的GET请求通常不需要CSRF保护
})

// 管理员操作CSRF保护（更严格）
export const adminCSRFProtection = new CSRFProtection({
  secret: process.env.ADMIN_CSRF_SECRET || process.env.NEXTAUTH_SECRET || 'admin-csrf-secret',
  cookieName: 'admin-csrf-token',
  headerName: 'x-admin-csrf-token',
  tokenLength: 64, // 更长的token
  maxAge: 30 * 60 * 1000, // 30分钟过期（更短）
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS']
})

// 工具函数：为API路由添加CSRF保护
export function withCSRFProtection(csrfInstance: CSRFProtection, handler: Function) {
  return async (req: NextRequest, ...args: any[]) => {
    const protectionResult = await csrfInstance.middleware()(req)
    
    if (protectionResult && protectionResult.status !== 200) {
      return protectionResult
    }

    return handler(req, ...args)
  }
}

// 工具函数：获取CSRF token的API端点
export function createCSRFTokenEndpoint(csrfInstance: CSRFProtection) {
  return async (req: NextRequest) => {
    if (req.method !== 'GET') {
      return new NextResponse('Method not allowed', { status: 405 })
    }

    const token = csrfInstance.generateToken()
    
    const response = new NextResponse(
      JSON.stringify({ token }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

    return csrfInstance.setTokenCookie(response, token)
  }
}

// 双重提交Cookie模式
export class DoubleSubmitCSRF {
  private secret: string

  constructor(secret: string) {
    this.secret = secret
  }

  // 生成token值
  generateTokenValue(): string {
    return randomBytes(32).toString('hex')
  }

  // 创建签名token
  createSignedToken(value: string): string {
    const signature = createHmac('sha256', this.secret)
      .update(value)
      .digest('hex')
    return `${value}.${signature}`
  }

  // 验证签名token
  validateSignedToken(signedToken: string): string | null {
    const [value, signature] = signedToken.split('.')
    if (!value || !signature) return null

    const expectedSignature = createHmac('sha256', this.secret)
      .update(value)
      .digest('hex')

    if (signature !== expectedSignature) return null
    return value
  }

  // 中间件
  middleware() {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method.toUpperCase())) {
        return NextResponse.next()
      }

      const cookieToken = req.cookies.get('csrf-token')?.value
      const headerToken = req.headers.get('x-csrf-token')

      if (!cookieToken || !headerToken) {
        return new NextResponse('CSRF token missing', { status: 403 })
      }

      const cookieValue = this.validateSignedToken(cookieToken)
      if (!cookieValue || cookieValue !== headerToken) {
        return new NextResponse('CSRF token mismatch', { status: 403 })
      }

      return NextResponse.next()
    }
  }
}

// 工具函数：检查请求来源
export function validateOrigin(req: NextRequest, allowedOrigins: string[]): boolean {
  const origin = req.headers.get('origin')
  const referer = req.headers.get('referer')

  // 检查Origin头
  if (origin && !allowedOrigins.includes(origin)) {
    return false
  }

  // 检查Referer头
  if (referer) {
    const refererURL = new URL(referer)
    const refererOrigin = `${refererURL.protocol}//${refererURL.host}`
    if (!allowedOrigins.includes(refererOrigin)) {
      return false
    }
  }

  return true
}

// 安全头部设置
export function setSecurityHeaders(response: NextResponse): NextResponse {
  // 内容安全策略
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'"
  )

  // 防止点击劫持
  response.headers.set('X-Frame-Options', 'DENY')

  // 防止MIME类型嗅探
  response.headers.set('X-Content-Type-Options', 'nosniff')

  // XSS保护
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // 严格传输安全
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  }

  // Referrer Policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  return response
}