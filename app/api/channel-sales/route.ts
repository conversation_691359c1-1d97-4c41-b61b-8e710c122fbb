import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const channelId = searchParams.get('channelId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const skip = (page - 1) * limit;

    const where: any = {};

    if (channelId) {
      where.channelId = parseInt(channelId);
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    const [sales, total] = await Promise.all([
      prisma.channelSale.findMany({
        where,
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  category: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.channelSale.count({ where }),
    ]);

    // 计算统计数据
    const stats = await prisma.channelSale.aggregate({
      where,
      _sum: {
        totalAmount: true,
        commissionAmount: true,
      },
      _count: true,
    });

    return NextResponse.json({
      data: sales,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats: {
        totalSales: stats._count,
        totalAmount: stats._sum.totalAmount || 0,
        totalCommission: stats._sum.commissionAmount || 0,
      },
    });
  } catch (error) {
    console.error('Error fetching channel sales:', error);
    return NextResponse.json(
      { error: 'Failed to fetch channel sales' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // 验证必填字段
    if (!data.channelId || !data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json(
        { error: '渠道和销售项目为必填项' },
        { status: 400 }
      );
    }

    // 验证每个销售项目
    for (const item of data.items) {
      if (!item.productId || !item.quantity || !item.unitPrice) {
        return NextResponse.json(
          { error: '每个销售项目必须包含产品、数量和单价' },
          { status: 400 }
        );
      }
    }

    // 检查渠道是否存在
    const channel = await prisma.channel.findUnique({
      where: { id: parseInt(data.channelId) },
    });

    if (!channel) {
      return NextResponse.json(
        { error: '渠道不存在' },
        { status: 404 }
      );
    }

    // 使用事务创建销售记录
    const result = await prisma.$transaction(async (tx) => {
      // 创建销售记录
      const sale = await tx.channelSale.create({
        data: {
          channelId: parseInt(data.channelId),
          orderNumber: data.orderNumber || `CS-${Date.now()}`,
          customerName: data.customerName,
          customerPhone: data.customerPhone,
          customerAddress: data.customerAddress,
          saleDate: data.saleDate ? new Date(data.saleDate) : new Date(),
          status: data.status || 'PENDING',
          paymentMethod: data.paymentMethod,
          totalAmount: 0, // 将在创建项目后计算
          commissionAmount: 0, // 将在创建项目后计算
          notes: data.notes,
        },
      });

      // 创建销售项目
      let totalAmount = 0;
      let commissionAmount = 0;

      for (const itemData of data.items) {
        const product = await tx.product.findUnique({
          where: { id: parseInt(itemData.productId) },
        });

        if (!product) {
          throw new Error(`产品 ${itemData.productId} 不存在`);
        }

        const quantity = parseInt(itemData.quantity);
        const unitPrice = parseFloat(itemData.unitPrice);
        const itemTotal = quantity * unitPrice;
        const itemCommission = parseFloat(itemData.commissionRate || '0') * itemTotal / 100;

        await tx.channelSaleItem.create({
          data: {
            channelSaleId: sale.id,
            productId: parseInt(itemData.productId),
            quantity,
            unitPrice,
            totalPrice: itemTotal,
            commissionRate: parseFloat(itemData.commissionRate || '0'),
            commissionAmount: itemCommission,
          },
        });

        totalAmount += itemTotal;
        commissionAmount += itemCommission;

        // 更新渠道库存（如果启用）
        if (data.updateInventory) {
          const channelInventory = await tx.channelInventory.findUnique({
            where: {
              channelId_productId: {
                channelId: parseInt(data.channelId),
                productId: parseInt(itemData.productId),
              },
            },
          });

          if (channelInventory) {
            if (channelInventory.quantity < quantity) {
              throw new Error(`产品 ${product.name} 库存不足`);
            }

            await tx.channelInventory.update({
              where: {
                channelId_productId: {
                  channelId: parseInt(data.channelId),
                  productId: parseInt(itemData.productId),
                },
              },
              data: {
                quantity: channelInventory.quantity - quantity,
              },
            });
          }
        }
      }

      // 更新销售总金额和佣金
      const updatedSale = await tx.channelSale.update({
        where: { id: sale.id },
        data: {
          totalAmount,
          commissionAmount,
        },
        include: {
          channel: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  category: true,
                },
              },
            },
          },
        },
      });

      return updatedSale;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating channel sale:', error);
    
    if (error instanceof Error && error.message.includes('不存在')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }
    
    if (error instanceof Error && error.message.includes('库存不足')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create channel sale' },
      { status: 500 }
    );
  }
}