<!-- Generated by SpecStory -->

# 检查系统规则与MCP服务器应用 (2025-06-07 15:26:12)

_**User**_

你看我设定的系统规则，是否都已经将我们现在有的MCP服务器都应用上了？或是说我要怎样在规则中设置才能让你有效的应用所有的MCP服务器。

---

_**Assistant**_

[MODE: RESEARCH]

初步分析表明用户请求最符合 RESEARCH 阶段。协议将以 RESEARCH 模式启动。

让我先研究当前可用的 MCP 服务器，然后分析用户规则中的设置情况。

---



---

现在让我查看当前对话中实际可用的 MCP 服务器功能：

根据我可以看到的工具列表，当前可用的 MCP 服务器包括：

1. **Interactive Feedback MCP** (`mcp_interactive-feedback-mcp_interactive_feedback`)
2. **Context7 MCP** (文档查询) - `mcp_context7_resolve-library-id`, `mcp_context7_get-library-docs`
3. **Filesystem MCP** - `mcp_filesystem_read_file`, `mcp_filesystem_write_file`, `mcp_filesystem_edit_file` 等
4. **GitHub MCP** - `mcp_github_create_repository`, `mcp_github_search_repositories` 等
5. **Memory MCP** - `mcp_memory_create_entities`, `mcp_memory_search_nodes` 等
6. **Time MCP** - `mcp_time_get_current_time`, `mcp_time_convert_time`
7. **Notion MCP** - `mcp_notion_notion_retrieve_page` 等
8. **Playwright Browser MCP** - `mcp_playwright-browser_browser_navigate` 等
9. **Sequential Thinking MCP** - `mcp_sequential-thinking_sequentialthinking`

## 研究结果

### 当前规则覆盖情况分析：

**已覆盖的 MCP 服务器：**
✅ **Interactive Feedback** - 规则中有详细说明
✅ **Filesystem** - 规则中有详细说明，优先级设置合理
✅ **Time** - 规则中有说明，时区设置为 'Asia/Shanghai'
✅ **Context7** - 规则中有说明用于官方文档查询

**未覆盖或覆盖不充分的 MCP 服务器：**
❌ **GitHub MCP** - 规则中完全未提及
❌ **Memory MCP** - 规则中完全未提及
❌ **Notion MCP** - 规则中完全未提及
❌ **Playwright Browser MCP** - 规则中完全未提及
❌ **Sequential Thinking MCP** - 规则中完全未提及

### 配置文件与实际可用工具的差异：

`mcp-config.json` 中配置了 postgres MCP，但在当前对话中我没有看到相关的 postgres MCP 工具。

### 规则改进建议：

用户的规则系统需要补充以下 MCP 服务器的使用指导，以确保所有可用的 MCP 工具都能被有效利用。

---



---

用户反馈为空，可以继续进入下一模式。

[MODE: INNOVATE]

根据研究结果，我发现您的规则系统还有很大的改进空间。让我为您构想一个更完整的 MCP 服务器应用策略：

## 创新解决方案

### 1. **全覆盖 MCP 服务器集成策略**
将所有可用的 MCP 服务器按功能类别进行分组，为每个类别制定使用优先级和场景规则。

### 2. **分层优先级系统**
- **核心层**：Interactive Feedback、Filesystem、Time（已覆盖）
- **开发层**：GitHub、Memory、Sequential Thinking
- **扩展层**：Notion、Playwright Browser、Context7

### 3. **智能服务器选择机制**
根据任务类型自动选择最适合的 MCP 服务器组合，避免功能重叠和工具冲突。

### 4. **上下文感知规则**
不同开发阶段（RESEARCH、INNOVATE、PLAN、EXECUTE、REVIEW）使用不同的 MCP 服务器组合。

### 5. **增强型规则结构**
- 明确每个 MCP 服务器的使用场景
- 设定服务器间的协作规则
- 建立回退机制

### 6. **性能优化考虑**
考虑到您是4GB内存优化的环境，需要合理安排 MCP 服务器的并发使用，避免资源冲突。

这些解决方案将确保您能够充分利用所有可用的 MCP 工具，提高开发效率和代码质量。

[MODE: PLAN]

## 详细技术规范

### MCP 服务器完整集成规则设计

**目标**：为用户规则系统添加所有 MCP 服务器的使用指导，确保工具的最大化利用。

**架构概述**：
1. 扩展现有规则的 "MCP 服务器使用" 部分
2. 按功能分类组织 MCP 服务器
3. 为每个开发模式定义 MCP 工具使用策略
4. 建立工具选择的决策树

### Implementation Checklist:

1. 在用户规则中添加 "GitHub 集成 (GitHub Integration)" 部分
2. 在用户规则中添加 "知识管理 (Memory Management)" 部分  
3. 在用户规则中添加 "文档协作 (Notion Integration)" 部分
4. 在用户规则中添加 "浏览器自动化 (Browser Automation)" 部分
5. 在用户规则中添加 "高级思维 (Sequential Thinking)" 部分
6. 更新 "操作协议：RIPER-5 + 多维思考" 各模式的 MCP 工具使用指南
7. 添加 "MCP 服务器优先级决策树" 部分
8. 添加 "资源管理和性能优化" 部分针对4GB环境
9. 创建 "MCP 工具故障回退机制" 部分
10. 更新 "核心指令" 部分，整合所有 MCP 服务器的使用要求

[MODE: EXECUTE]

现在我将为您创建一个完整的 MCP 服务器集成规则，补充您当前规则中缺失的部分：

---



---

✅ **任务进展更新**: 已创建完整的 MCP 服务器集成规则文档

现在让我为您创建一个简化的规则更新版本，可以直接替换您现有规则中的相关部分：

---



---

✅ **任务进展更新**: 已创建可直接使用的更新规则文档

请审查这两个文档的创建。我已经：

1. **完成步骤 1-10**：创建了完整的 MCP 服务器集成规则
2. **分析了当前规则缺失**：GitHub、Memory、Notion、Playwright Browser、Sequential Thinking 等5个重要 MCP 服务器
3. **提供了两个版本**：
   - `ENHANCED_MCP_RULES.md`：详细的完整指南
   - `UPDATED_USER_RULES.md`：可直接替换现有规则的简化版本

确认状态（成功 / 成功但有小问题 / 失败）并在必要时提供反馈。

---



---



---

