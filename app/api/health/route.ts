/**
 * 健康检查API端点
 * 用于容器健康检查和负载均衡器监控
 */

import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/db'

interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  uptime: number
  version: string
  checks: {
    database: 'healthy' | 'unhealthy'
    memory: 'healthy' | 'unhealthy'
    disk: 'healthy' | 'unhealthy'
  }
  metrics?: {
    memoryUsage: NodeJS.MemoryUsage
    cpuUsage: NodeJS.CpuUsage
    processUptime: number
  }
}

// 缓存健康检查结果，避免频繁检查
let lastHealthCheck: HealthStatus | null = null
let lastCheckTime = 0
const CACHE_DURATION = 30000 // 30秒缓存

export async function GET(request: NextRequest) {
  const now = Date.now()
  
  // 如果缓存未过期，直接返回缓存结果
  if (lastHealthCheck && (now - lastCheckTime) < CACHE_DURATION) {
    return NextResponse.json(lastHealthCheck, {
      status: lastHealthCheck.status === 'healthy' ? 200 : 503
    })
  }

  const startTime = process.hrtime.bigint()
  
  try {
    // 检查数据库连接
    const databaseStatus = await checkDatabase()
    
    // 检查内存使用情况
    const memoryStatus = checkMemory()
    
    // 检查磁盘空间（简化版）
    const diskStatus = checkDisk()
    
    // 获取系统指标
    const metrics = getSystemMetrics()
    
    const overallStatus = databaseStatus === 'healthy' && 
                         memoryStatus === 'healthy' && 
                         diskStatus === 'healthy' ? 'healthy' : 'unhealthy'

    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      checks: {
        database: databaseStatus,
        memory: memoryStatus,
        disk: diskStatus
      },
      metrics
    }

    // 更新缓存
    lastHealthCheck = healthStatus
    lastCheckTime = now

    const endTime = process.hrtime.bigint()
    const responseTime = Number(endTime - startTime) / 1000000 // 转换为毫秒

    return NextResponse.json(healthStatus, {
      status: overallStatus === 'healthy' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Response-Time': `${responseTime.toFixed(2)}ms`
      }
    })

  } catch (error) {
    console.error('Health check failed:', error)
    
    const errorStatus: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      checks: {
        database: 'unhealthy',
        memory: 'unhealthy',
        disk: 'unhealthy'
      }
    }

    return NextResponse.json(errorStatus, { status: 503 })
  }
}

async function checkDatabase(): Promise<'healthy' | 'unhealthy'> {
  try {
    // 执行简单的数据库查询
    await prisma.$queryRaw`SELECT 1`
    return 'healthy'
  } catch (error) {
    console.error('Database health check failed:', error)
    return 'unhealthy'
  }
}

function checkMemory(): 'healthy' | 'unhealthy' {
  try {
    const memoryUsage = process.memoryUsage()
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024
    
    // 如果堆内存使用超过90%，认为不健康
    const memoryUsagePercent = (heapUsedMB / heapTotalMB) * 100
    
    return memoryUsagePercent < 90 ? 'healthy' : 'unhealthy'
  } catch (error) {
    console.error('Memory health check failed:', error)
    return 'unhealthy'
  }
}

function checkDisk(): 'healthy' | 'unhealthy' {
  try {
    // 简化的磁盘检查，实际项目中可以使用 fs.statSync 检查磁盘空间
    // 这里只是检查能否访问文件系统
    const fs = require('fs')
    fs.accessSync(process.cwd(), fs.constants.R_OK | fs.constants.W_OK)
    return 'healthy'
  } catch (error) {
    console.error('Disk health check failed:', error)
    return 'unhealthy'
  }
}

function getSystemMetrics() {
  try {
    return {
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      processUptime: process.uptime()
    }
  } catch (error) {
    console.error('Failed to get system metrics:', error)
    return undefined
  }
}

// 简化的健康检查端点（用于快速检查）
export async function HEAD(request: NextRequest) {
  try {
    // 只检查数据库连接
    await prisma.$queryRaw`SELECT 1`
    return new NextResponse(null, { status: 200 })
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
